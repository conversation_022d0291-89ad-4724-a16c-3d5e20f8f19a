<template>
  <div class="w-full space-y-4">
    <!-- Upload Area -->
    <div
      class="card border-2 border-dashed border-gray-600 hover:border-blue-500 transition-colors duration-300 cursor-pointer group"
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @click="fileInput?.click()"
    >
      <div class="p-6 text-center space-y-3">
        <div
          class="text-4xl mb-2 group-hover:scale-110 transition-transform duration-300"
        >
          📁
        </div>
        <h3 class="text-lg font-semibold text-white mb-1">
          将游戏ZIP文件拖拽到这里
        </h3>
        <p class="text-gray-400 text-sm mb-4">或点击选择ZIP文件</p>

        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".zip"
          @change="handleFileSelect"
          class="hidden"
        />

        <button
          type="button"
          class="btn-primary inline-flex items-center gap-2 text-sm"
          @click.stop="fileInput?.click()"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            ></path>
          </svg>
          选择ZIP文件
        </button>
      </div>
    </div>

    <!-- Supported Formats -->
    <div v-if="supportedFormats.length > 0" class="card p-4">
      <h4 class="text-sm font-semibold text-white mb-3 flex items-center gap-2">
        <svg
          class="w-4 h-4 text-blue-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          ></path>
        </svg>
        上传ZIP文件，支持解压以下游戏格式：
      </h4>
      <div class="flex flex-wrap gap-1.5">
        <span
          v-for="format in supportedFormats"
          :key="format"
          class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30"
        >
          .{{ format }}
        </span>
      </div>
    </div>

    <!-- Processing Indicator -->
    <div v-if="isProcessing" class="card p-4">
      <div class="flex items-center justify-center space-x-3">
        <div class="loading-spinner"></div>
        <p class="text-white font-medium text-sm">正在处理文件...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  handleFileUpload,
  getAllSupportedExtensions,
} from "../utils/fileHandler";
import type { GameFile } from "../types/emulator";
import toast from "../utils/toast";

const emit = defineEmits<{
  "files-selected": [games: GameFile[]];
}>();

const fileInput = ref<HTMLInputElement>();
const isProcessing = ref(false);
const supportedFormats = ref<string[]>([]);

onMounted(() => {
  supportedFormats.value = getAllSupportedExtensions();
});

async function processFiles(files: FileList) {
  if (files.length === 0) return;

  isProcessing.value = true;
  try {
    const allGames: GameFile[] = [];

    for (const file of Array.from(files)) {
      try {
        const games = await handleFileUpload(file);
        allGames.push(...games);
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        // 显示详细的错误信息
        if (error instanceof Error) {
          toast.error(error.message, {
            title: `处理文件 ${file.name} 时出错`,
            duration: 10000,
            persistent: false,
          });
        } else {
          toast.error(`处理文件 ${file.name} 时出错：未知错误`);
        }
        return; // 遇到错误就停止处理
      }
    }

    if (allGames.length > 0) {
      emit("files-selected", allGames);
      toast.success(`成功加载 ${allGames.length} 个游戏文件`);
    } else {
      toast.warning("未找到支持的游戏文件！");
    }
  } catch (error) {
    console.error("Error processing files:", error);
    toast.error("处理文件时出错，请重试。");
  } finally {
    isProcessing.value = false;
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    processFiles(target.files);
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  if (event.dataTransfer?.files) {
    processFiles(event.dataTransfer.files);
  }
}
</script>
