<template>
  <div class="emulator-canvas">
    <div class="canvas-container">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>正在加载模拟器...</h3>
          <p>正在下载 {{ core?.name }} 核心...</p>
        </div>
      </div>

      <div id="game-canvas" class="game-display"></div>

      <div class="controls">
        <button @click="stopEmulator" class="btn-stop">⏹️ 停止游戏</button>
        <button @click="toggleFullscreen" class="btn-fullscreen">
          🔳 全屏
        </button>
        <button @click="saveState" class="btn-save">💾 保存状态</button>
        <button @click="loadState" class="btn-load">📁 加载状态</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, nextTick } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
}>();

let emulator: any = null;

onMounted(async () => {
  await nextTick();
  if (props.core && props.game) {
    try {
      await initializeEmulator();
    } catch (error) {
      console.error("初始化模拟器失败:", error);
      emit("error", "初始化模拟器失败: " + (error as Error).message);
    }
  }
});

onUnmounted(() => {
  if (emulator) {
    try {
      emulator.stop();
    } catch (error) {
      console.error("停止模拟器时出错:", error);
    }
  }
});

async function initializeEmulator() {
  if (!props.core || !props.game) return;

  try {
    console.log("开始加载EmulatorJS...");
    // Load EmulatorJS script dynamically
    await loadEmulatorJS();

    console.log("EmulatorJS加载完成，开始初始化模拟器...");

    // Check if EJS_player is available
    const EJS_player = (window as any).EJS_player;
    if (!EJS_player) {
      throw new Error("EmulatorJS未正确加载");
    }

    // Convert ArrayBuffer to Blob URL
    const gameBlob = new Blob([props.game.data]);
    const gameUrl = URL.createObjectURL(gameBlob);

    console.log("游戏文件准备完成，系统:", mapCoreToSystem(props.core.id));

    emulator = new EJS_player("#game-canvas", {
      system: mapCoreToSystem(props.core.id),
      core: props.core.id,
      url: gameUrl,
      biosUrl: "", // BIOS files would need to be provided separately
      gameParentUrl: "",
      threads: true,
      sound: true,
      volume: 0.8,
      loadState: "",

      // Styling
      color: "#667eea",
      backgroundColor: "#222",

      // Controls
      controls: {
        enabled: true,
        volume: true,
        expand: true,
        loadState: true,
        saveState: true,
        quickSaveState: true,
        quickLoadState: true,
        screenshot: true,
        fullscreen: true,
        restart: true,
        mute: true,
        settings: true,
        gamepad: true,
      },

      // Callbacks
      onLoaded: () => {
        console.log("游戏加载成功");
      },

      onError: (error: string) => {
        console.error("模拟器错误:", error);
        emit("error", `模拟器错误: ${error}`);
      },
    });

    console.log("模拟器初始化完成");
  } catch (error) {
    console.error("初始化模拟器时出错:", error);
    emit("error", "初始化模拟器失败: " + (error as Error).message);
  }
}

async function loadEmulatorJS() {
  return new Promise<void>((resolve, reject) => {
    // Check if EmulatorJS is already loaded
    if ((window as any).EJS_player) {
      resolve();
      return;
    }

    // Load the main EmulatorJS script
    const script = document.createElement("script");
    script.src = "https://cdn.emulatorjs.org/latest/data/loader.js";
    script.onload = () => {
      // Wait a bit for the script to fully initialize
      setTimeout(() => {
        if ((window as any).EJS_player) {
          resolve();
        } else {
          reject(new Error("EmulatorJS未能正确加载"));
        }
      }, 1000);
    };
    script.onerror = () => reject(new Error("加载EmulatorJS失败"));
    document.head.appendChild(script);
  });
}

function mapCoreToSystem(coreId: string): string {
  const systemMap: { [key: string]: string } = {
    gambatte: "gb",
    mgba: "gba",
    snes9x: "snes",
    fceumm: "nes",
    genesis_plus_gx: "segaMD",
    smsplus: "segaMS",
    stella2014: "atari2600",
    a5200: "atari5200",
    desmume2015: "nds",
    mupen64plus_next: "n64",
    mednafen_psx_hw: "psx",
  };

  return systemMap[coreId] || coreId;
}

function stopEmulator() {
  if (emulator) {
    try {
      emulator.stop();
    } catch (error) {
      console.error("Error stopping emulator:", error);
    }
  }
  emit("stop");
}

function toggleFullscreen() {
  if (emulator && emulator.elements && emulator.elements.parent) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      emulator.elements.parent.requestFullscreen();
    }
  }
}

function saveState() {
  if (emulator && emulator.saveState) {
    try {
      emulator.saveState();
    } catch (error) {
      console.error("Error saving state:", error);
    }
  }
}

function loadState() {
  if (emulator && emulator.loadState) {
    try {
      emulator.loadState();
    } catch (error) {
      console.error("Error loading state:", error);
    }
  }
}
</script>

<style scoped>
.emulator-canvas {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.canvas-container {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.loading-content p {
  opacity: 0.8;
}

.game-display {
  width: 100%;
  min-height: 400px;
  background: #000;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  position: relative;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.controls button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
}

.controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-stop {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: rgba(244, 67, 54, 0.3) !important;
}

.btn-stop:hover {
  background: rgba(244, 67, 54, 0.3) !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* EmulatorJS styling overrides */
:global(#game-canvas) {
  width: 100% !important;
  height: auto !important;
  border-radius: 12px !important;
}

:global(.ejs-container) {
  border-radius: 12px !important;
}
</style>
