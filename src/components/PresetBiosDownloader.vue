<template>
  <div class="w-full space-y-4">
    <!-- 预置 BIOS 说明 -->
    <div class="card bg-green-900/20 border-green-500/30">
      <div class="p-4">
        <h3 class="text-lg font-semibold text-green-400 mb-2 flex items-center">
          <span class="mr-2">🌐</span>
          预置 BIOS 下载
        </h3>
        <p class="text-gray-300 text-sm mb-3">
          我们为你准备了经过验证的 BIOS 文件，可以一键下载安装。这些 BIOS
          文件已经过测试，确保兼容性。
        </p>

        <!-- 统计信息 -->
        <div class="flex items-center gap-4 text-xs">
          <div class="flex items-center gap-1">
            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
            <span class="text-gray-300"
              >已安装: {{ installationStatus.installed.length }}</span
            >
          </div>
          <div class="flex items-center gap-1">
            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
            <span class="text-gray-300"
              >可下载: {{ installationStatus.available.length }}</span
            >
          </div>
          <div class="flex items-center gap-1">
            <span class="w-2 h-2 bg-gray-500 rounded-full"></span>
            <span class="text-gray-300"
              >总计: {{ installationStatus.total }}</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- BIOS 列表 -->
    <div class="space-y-3">
      <div
        v-for="bios in presetBiosList"
        :key="bios.id"
        class="card p-4 hover:bg-gray-800/50 transition-colors"
      >
        <div class="flex items-center justify-between">
          <!-- BIOS 信息 -->
          <div class="flex-1">
            <div class="flex items-center gap-3 mb-2">
              <div
                class="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center"
              >
                <span class="text-white font-bold text-sm">
                  {{ bios.name.charAt(0) }}
                </span>
              </div>

              <div>
                <h4 class="font-semibold text-white flex items-center gap-2">
                  {{ bios.name }}
                  <span v-if="bios.isVerified" class="text-green-400 text-xs"
                    >✓ 已验证</span
                  >
                </h4>
                <p class="text-gray-400 text-sm">{{ bios.description }}</p>
              </div>
            </div>

            <!-- 详细信息 -->
            <div
              class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-500 mb-2"
            >
              <div>文件: {{ bios.filename }}</div>
              <div>大小: {{ formatFileSize(bios.size) }}</div>
              <div>区域: {{ bios.region }}</div>
              <div>版本: {{ bios.version }}</div>
            </div>

            <!-- 支持的游戏 -->
            <div v-if="bios.games.length > 0" class="mb-2">
              <div class="text-xs text-gray-400 mb-1">支持游戏:</div>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="game in bios.games.slice(0, 4)"
                  :key="game"
                  class="px-2 py-1 bg-gray-700 rounded text-xs text-gray-300"
                >
                  {{ game }}
                </span>
                <span
                  v-if="bios.games.length > 4"
                  class="px-2 py-1 bg-gray-700 rounded text-xs text-gray-400"
                >
                  +{{ bios.games.length - 4 }}
                </span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col items-end gap-2 ml-4">
            <!-- 状态指示器 -->
            <div class="flex items-center gap-2">
              <div
                :class="['w-3 h-3 rounded-full', getBiosStatusColor(bios.id)]"
              ></div>
              <span class="text-xs text-gray-400">
                {{ getBiosStatusText(bios.id) }}
              </span>
            </div>

            <!-- 下载按钮 -->
            <button
              v-if="!isInstalled(bios.id) && bios.url"
              @click="downloadBios(bios.id)"
              :disabled="isDownloading(bios.id)"
              class="btn-primary text-sm px-4 py-2 min-w-[80px]"
            >
              <span
                v-if="isDownloading(bios.id)"
                class="flex items-center gap-1"
              >
                <div
                  class="animate-spin rounded-full h-3 w-3 border-b-2 border-white"
                ></div>
                {{ getDownloadProgress(bios.id) }}%
              </span>
              <span v-else>下载</span>
            </button>

            <!-- 已安装标识 -->
            <div
              v-else-if="isInstalled(bios.id)"
              class="text-green-400 text-sm flex items-center gap-1"
            >
              <span>✓</span>
              已安装
            </div>

            <!-- 无下载链接 -->
            <div v-else class="text-gray-500 text-sm">暂无下载</div>
          </div>
        </div>

        <!-- 下载进度条 -->
        <div v-if="isDownloading(bios.id)" class="mt-3">
          <div class="w-full bg-gray-700 rounded-full h-2">
            <div
              class="bg-blue-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: getDownloadProgress(bios.id) + '%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="card p-4 bg-blue-900/20 border-blue-500/30">
      <h4 class="font-semibold text-blue-400 mb-3">批量操作</h4>
      <div class="flex flex-wrap gap-2">
        <button
          @click="downloadAllAvailable"
          :disabled="isAnyDownloading"
          class="btn-primary text-sm"
        >
          下载所有可用 BIOS
        </button>

        <button @click="refreshStatus" class="btn-secondary text-sm">
          刷新状态
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  getPresetBiosList,
  downloadAndInstallPresetBios,
  getBiosInstallationStatus,
  type PresetBiosItem,
} from "../utils/presetBiosManager";
import toast from "../utils/toast";

const presetBiosList = ref<PresetBiosItem[]>([]);
const installationStatus = ref({
  installed: [] as string[],
  available: [] as string[],
  total: 0,
});

const downloadingBios = ref<Map<string, number>>(new Map()); // biosId -> progress

const isAnyDownloading = computed(() => downloadingBios.value.size > 0);

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 检查 BIOS 是否已安装
function isInstalled(biosId: string): boolean {
  return installationStatus.value.installed.includes(biosId);
}

// 检查 BIOS 是否正在下载
function isDownloading(biosId: string): boolean {
  return downloadingBios.value.has(biosId);
}

// 获取下载进度
function getDownloadProgress(biosId: string): number {
  return downloadingBios.value.get(biosId) || 0;
}

// 获取 BIOS 状态颜色
function getBiosStatusColor(biosId: string): string {
  if (isInstalled(biosId)) return "bg-green-500";
  if (isDownloading(biosId)) return "bg-blue-500 animate-pulse";
  const bios = presetBiosList.value.find((b) => b.id === biosId);
  if (bios?.url) return "bg-yellow-500";
  return "bg-gray-500";
}

// 获取 BIOS 状态文本
function getBiosStatusText(biosId: string): string {
  if (isInstalled(biosId)) return "已安装";
  if (isDownloading(biosId)) return "下载中";
  const bios = presetBiosList.value.find((b) => b.id === biosId);
  if (bios?.url) return "可下载";
  return "暂无下载";
}

// 下载单个 BIOS
async function downloadBios(biosId: string) {
  try {
    const bios = presetBiosList.value.find((b) => b.id === biosId);
    if (!bios) return;

    downloadingBios.value.set(biosId, 0);

    await downloadAndInstallPresetBios(biosId, (progress) => {
      downloadingBios.value.set(biosId, progress);
    });

    downloadingBios.value.delete(biosId);
    await refreshStatus();

    toast.success(`BIOS ${bios.name} 下载安装成功！`);
  } catch (error) {
    downloadingBios.value.delete(biosId);
    console.error("下载 BIOS 失败:", error);
    toast.error(`下载 BIOS 失败: ${error}`);
  }
}

// 下载所有可用的 BIOS
async function downloadAllAvailable() {
  const availableBios = presetBiosList.value.filter(
    (bios) => !isInstalled(bios.id) && bios.url && !isDownloading(bios.id)
  );

  if (availableBios.length === 0) {
    toast.info("没有可下载的 BIOS 文件");
    return;
  }

  toast.info(`开始下载 ${availableBios.length} 个 BIOS 文件...`);

  for (const bios of availableBios) {
    try {
      await downloadBios(bios.id);
    } catch (error) {
      console.error(`下载 ${bios.name} 失败:`, error);
    }
  }
}

// 刷新安装状态
async function refreshStatus() {
  try {
    installationStatus.value = await getBiosInstallationStatus();
  } catch (error) {
    console.error("刷新状态失败:", error);
  }
}

// 初始化
onMounted(async () => {
  presetBiosList.value = getPresetBiosList();
  await refreshStatus();
});
</script>
