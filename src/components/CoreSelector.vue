<template>
  <div class="core-selector">
    <div class="selector-card">
      <h3>选择模拟器核心</h3>

      <div v-if="recommendedCore" class="recommended-section">
        <h4>🎯 推荐：</h4>
        <div
          class="core-option recommended"
          :class="{ selected: selectedCore?.id === recommendedCore.id }"
          @click="selectCore(recommendedCore)"
        >
          <div class="core-info">
            <h5>{{ recommendedCore.name }}</h5>
            <p>{{ recommendedCore.description }}</p>
            <div class="extensions">
              <span
                v-for="ext in recommendedCore.extensions"
                :key="ext"
                class="ext-tag"
              >
                .{{ ext }}
              </span>
            </div>
          </div>
          <div class="selection-indicator">
            <div
              v-if="selectedCore?.id === recommendedCore.id"
              class="checkmark"
            >
              ✓
            </div>
          </div>
        </div>
      </div>

      <div class="all-cores-section">
        <h4>所有可用核心：</h4>
        <div class="cores-grid">
          <div
            v-for="core in allCores"
            :key="core.id"
            class="core-option"
            :class="{
              selected: selectedCore?.id === core.id,
              recommended: core.id === recommendedCore?.id,
            }"
            @click="selectCore(core)"
          >
            <div class="core-info">
              <h5>{{ core.name }}</h5>
              <p>{{ core.description }}</p>
              <div class="extensions">
                <span v-for="ext in core.extensions" :key="ext" class="ext-tag">
                  .{{ ext }}
                </span>
              </div>
            </div>
            <div class="selection-indicator">
              <div v-if="selectedCore?.id === core.id" class="checkmark">✓</div>
            </div>
          </div>
        </div>
      </div>

      <div class="actions">
        <button
          @click="$emit('play')"
          :disabled="!selectedCore"
          class="btn-play"
        >
          🎮 开始游戏
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";
import {
  EMULATOR_CORES,
  detectCoreByExtension,
  detectCoreBySystem,
} from "../utils/cores";

const props = defineProps<{
  game: GameFile;
  selectedCore: EmulatorCore | null;
  gameSystem?: string;
}>();

const emit = defineEmits<{
  "core-selected": [core: EmulatorCore];
  play: [];
}>();

const allCores = computed(() => EMULATOR_CORES);

const recommendedCore = computed(() => {
  // 优先根据系统类型选择核心
  if (props.gameSystem) {
    const coreBySystem = detectCoreBySystem(props.gameSystem);
    if (coreBySystem) {
      return coreBySystem;
    }
  }

  // 如果没有系统信息或找不到对应核心，则根据文件扩展名选择
  return detectCoreByExtension(props.game.extension);
});

onMounted(() => {
  // Auto-select recommended core if available
  if (recommendedCore.value && !props.selectedCore) {
    selectCore(recommendedCore.value);
  }
});

function selectCore(core: EmulatorCore) {
  emit("core-selected", core);
}
</script>

<style scoped>
.core-selector {
  width: 100%;
  margin-bottom: 2rem;
}

.selector-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.selector-card h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  font-weight: 700;
  text-align: center;
}

.recommended-section {
  margin-bottom: 2rem;
}

.recommended-section h4,
.all-cores-section h4 {
  margin-bottom: 1rem;
  font-weight: 600;
  opacity: 0.9;
}

.core-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
  background: rgba(255, 255, 255, 0.05);
}

.core-option:hover {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.core-option.selected {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
}

.core-option.recommended {
  border-color: #ff9800;
}

.core-option.recommended.selected {
  border-color: #4caf50;
}

.core-info {
  flex: 1;
}

.core-info h5 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.core-info p {
  opacity: 0.8;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.extensions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.ext-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.selection-indicator {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkmark {
  width: 24px;
  height: 24px;
  background: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.cores-grid {
  display: grid;
  gap: 1rem;
}

.all-cores-section {
  margin-bottom: 2rem;
}

.actions {
  text-align: center;
}

.btn-play {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-play:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-play:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
</style>
