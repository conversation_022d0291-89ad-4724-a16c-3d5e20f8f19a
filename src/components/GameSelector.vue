<template>
  <div class="game-selector">
    <h3>
      <img
        src="/images/game-icons/mario.png"
        alt="游戏图标"
        class="inline-block w-6 h-6 mr-2"
      />
      选择游戏
    </h3>
    <div class="games-grid">
      <div
        v-for="game in availableGames"
        :key="game.path"
        class="relative game-card"
        @click="selectGame(game)"
      >
        <div class="game-icon">
          <img
            v-if="game.icon && game.icon.startsWith('/images/')"
            :src="game.icon"
            :alt="game.name"
            class="game-icon-img"
          />
          <span v-else>{{ game.icon || "🎮" }}</span>
        </div>
        <div class="game-name">{{ game.name }}</div>
        <div v-if="game.description" class="game-description">
          {{ game.description }}
        </div>
        <div v-if="game.system" class="game-system">
          {{ getSystemName(game.system) }}
        </div>
        <!-- 存档状态指示器 -->
        <div v-if="gameProgressStatus[game.name]" class="save-indicator">
          <span class="save-icon">💾</span>
          <span class="save-text">有存档</span>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载游戏...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getAvailableGames,
  loadLocalGame,
  type GameInfo,
} from "../utils/gameLoader";
import type { GameFile } from "../types/emulator";
import { saveStateManager } from "../utils/saveStateManager";

const emit = defineEmits<{
  gameSelected: [game: GameFile];
  error: [message: string];
}>();

const availableGames = ref<GameInfo[]>([]);
const loading = ref(false);
const gameProgressStatus = ref<Record<string, boolean>>({});

// 检查游戏进度状态
async function checkGameProgress(): Promise<void> {
  try {
    await saveStateManager.init();
    const progressStatus: Record<string, boolean> = {};

    for (const game of availableGames.value) {
      progressStatus[game.name] = await saveStateManager.hasGameProgress(
        game.name
      );
    }

    gameProgressStatus.value = progressStatus;
  } catch (error) {
    console.error("检查游戏进度失败:", error);
  }
}

onMounted(async () => {
  try {
    availableGames.value = await getAvailableGames();
    // 检查游戏进度状态
    await checkGameProgress();
  } catch (error) {
    console.error("获取游戏列表失败:", error);
    emit("error", "获取游戏列表失败");
  }
});

function getSystemName(system: string): string {
  const systemNames: { [key: string]: string } = {
    nds: "Nintendo DS",
    gb: "Game Boy",
    gbc: "Game Boy Color",
    gba: "Game Boy Advance",
    nes: "Nintendo Entertainment System",
    snes: "Super Nintendo",
    md: "Sega Genesis",
    sms: "Sega Master System",
    n64: "Nintendo 64",
    psx: "PlayStation",
  };
  return systemNames[system] || system.toUpperCase();
}

async function selectGame(gameInfo: GameInfo) {
  loading.value = true;
  try {
    const game = await loadLocalGame(gameInfo.path, gameInfo.name);
    if (game) {
      // 添加系统信息到游戏文件
      game.system = gameInfo.system;
      emit("gameSelected", game);
    } else {
      emit("error", `无法加载游戏: ${gameInfo.name}`);
    }
  } catch (error) {
    console.error("加载游戏失败:", error);
    emit("error", `加载游戏失败: ${(error as Error).message}`);
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.game-selector {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
  position: static;
}

.game-selector h3 {
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.games-grid {
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.game-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.game-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 4rem;
}

.game-icon-img {
  width: 3rem;
  height: 3rem;
  object-fit: cover;
  border-radius: 8px;
}

.game-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.game-description {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.game-system {
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
  opacity: 0.9;
}

.save-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(34, 197, 94, 0.9);
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 0.2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.save-icon {
  font-size: 0.8rem;
}

.save-text {
  font-weight: 500;
}

.loading {
  text-align: center;
  color: white;
  padding: 2rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
