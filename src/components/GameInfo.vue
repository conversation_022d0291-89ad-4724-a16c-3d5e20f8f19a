<!--
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:19:44
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-30 16:44:34
 * @Description: 
 * 
-->
<template>
  <div class="w-full mb-8">
    <div class="p-8 card">
      <div class="flex items-center gap-6 mb-8">
        <div class="text-5xl">🎮</div>
        <div class="flex-1 text-white">
          <!-- 一行省略号 -->
          <h2
            class="w-[50vw] text-3xl font-bold mb-2 whitespace-nowrap overflow-hidden text-ellipsis"
          >
            {{ game.name }}
          </h2>
          <p class="mb-1 text-base opacity-80">
            {{ formatFileSize(game.size) }}
          </p>
          <p class="text-sm font-semibold uppercase opacity-70">
            .{{ game.extension.toUpperCase() }} 文件
          </p>
        </div>
        <!-- <button
          @click="$emit('back')"
          class="bg-white/20 border border-white/30 text-white px-6 py-3 rounded-xl cursor-pointer transition-all duration-300 font-semibold hover:bg-white/30 hover:-translate-y-0.5"
        >
          ← 返回
        </button> -->
      </div>

      <div v-if="core" class="text-white">
        <h3 class="mb-4 font-semibold opacity-90">已选择的核心：</h3>
        <div class="p-6 border bg-white/5 border-white/10 rounded-xl">
          <h4 class="mb-2 text-xl font-semibold">{{ core.name }}</h4>
          <p class="leading-relaxed opacity-80">{{ core.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GameFile, EmulatorCore } from "../types/emulator";
import { formatFileSize } from "../utils/fileHandler";

defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
}>();

defineEmits<{
  back: [];
}>();
</script>
