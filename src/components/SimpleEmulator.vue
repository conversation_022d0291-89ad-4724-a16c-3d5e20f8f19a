<template>
  <div class="simple-emulator">
    <div class="emulator-container">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h3>正在加载模拟器...</h3>
          <p>正在准备 {{ core?.name }} 核心...</p>
        </div>
      </div>

      <div id="emulator-container" class="game-display"></div>

      <div class="controls">
        <button @click="stopEmulator" class="btn-stop">⏹️ 停止游戏</button>
        <button @click="$emit('back')" class="btn-back">← 返回选择</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";
// EmulatorJS will be loaded dynamically

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);

onMounted(async () => {
  if (props.core && props.game) {
    try {
      await initializeSimpleEmulator();
    } catch (error) {
      console.error("初始化模拟器失败:", error);
      emit("error", "初始化模拟器失败: " + (error as Error).message);
    }
  }
});

async function initializeSimpleEmulator() {
  try {
    loading.value = true;

    // Create a data URL from the game file
    const gameData = new Uint8Array(props.game.data);
    const gameBlob = new Blob([gameData]);
    const gameUrl = URL.createObjectURL(gameBlob);

    // Create the emulator HTML structure
    const container = document.getElementById("emulator-container");
    if (!container) {
      throw new Error("找不到模拟器容器");
    }

    // Clear any existing content and create emulator element
    container.innerHTML = "";
    const gameCanvas = document.createElement("div");
    gameCanvas.id = "game";
    gameCanvas.style.width = "100%";
    gameCanvas.style.height = "400px";
    container.appendChild(gameCanvas);

    console.log(
      "开始初始化EmulatorJS，系统:",
      getSystemFromCore(props.core?.id || "")
    );

    // Load EmulatorJS with the game configuration
    await loadLocalEmulatorJS(gameUrl, getSystemFromCore(props.core?.id || ""));
  } catch (error) {
    loading.value = false;
    console.error("初始化模拟器失败:", error);

    // Show fallback message
    const container = document.getElementById("emulator-container");
    if (container) {
      container.innerHTML = `
        <div style="width: 100%; height: 400px; background: #000; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
          <div style="text-align: center;">
            <div style="font-size: 48px; margin-bottom: 16px;">🎮</div>
            <h3>${props.game.name}</h3>
            <p>核心: ${props.core?.name}</p>
            <p>文件大小: ${(props.game.size / 1024).toFixed(1)} KB</p>
            <p style="margin-top: 20px; opacity: 0.7; color: #ff6b6b;">
              模拟器加载失败<br>
              ${(error as Error).message}
            </p>
          </div>
        </div>
      `;
    }
  }
}

async function loadLocalEmulatorJS(gameUrl: string, system: string) {
  return new Promise<void>((resolve, reject) => {
    // 清理之前的实例
    if ((window as any).EJS_emulator) {
      try {
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("清理旧实例失败:", e);
      }
      delete (window as any).EJS_emulator;
    }

    console.log("设置EmulatorJS配置:", {
      gameUrl,
      system,
      gameName: props.game.name,
      gameSize: props.game.size,
    });

    // 清理并重新设置全局变量
    const ejsKeys = Object.keys(window).filter((k) => k.startsWith("EJS_"));
    ejsKeys.forEach((key) => delete (window as any)[key]);

    // 设置必需的全局变量
    (window as any).EJS_player = "#game";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_core = system;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_DEBUG_XX = true; // 强制使用非压缩文件
    (window as any).EJS_biosUrl = "";
    (window as any).EJS_gameParentUrl = "";
    (window as any).EJS_gameName = props.game.name;
    (window as any).EJS_color = "#667eea";
    (window as any).EJS_backgroundColor = "#222";
    (window as any).EJS_threads = true;
    (window as any).EJS_volume = 0.8;
    (window as any).EJS_startOnLoaded = true;

    // 对于街机游戏，不要解压ZIP文件
    if (
      system === "arcade" ||
      system === "fbalpha" ||
      system === "fbneo" ||
      props.game.extension === "zip"
    ) {
      (window as any).EJS_dontExtractBIOS = true;
    }

    // 设置调试回调
    (window as any).EJS_ready = () => {
      console.log("🎮 EmulatorJS ready!");
      loading.value = false;
    };
    (window as any).EJS_onGameStart = () => {
      console.log("🎮 Game started!");
      loading.value = false;
    };
    (window as any).EJS_onLoadState = () => {
      console.log("🎮 Game loaded!");
    };

    // 检查是否已经加载了脚本
    const existingScript = document.querySelector(
      'script[src="/emulatorjs/loader.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const existingCSS = document.querySelector(
      'link[href="/emulatorjs/emulator.css"]'
    );
    if (!existingCSS) {
      const css = document.createElement("link");
      css.rel = "stylesheet";
      css.href = "/emulatorjs/emulator.css";
      document.head.appendChild(css);
    }

    // 重新加载脚本
    const script = document.createElement("script");
    script.src = "/emulatorjs/loader.js";
    script.onload = () => {
      console.log("loader.js加载完成，等待EmulatorJS初始化...");

      // 增加等待时间，确保EmulatorJS完全初始化
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          console.log("✅ EmulatorJS初始化成功");
          console.log("EJS_emulator对象:", (window as any).EJS_emulator);

          // 添加所有事件监听器
          (window as any).EJS_emulator.on("ready", () => {
            console.log("🎮 模拟器准备就绪");
          });

          (window as any).EJS_emulator.on("start", () => {
            console.log("🎮 游戏开始");
            loading.value = false;
          });

          (window as any).EJS_emulator.on("error", (error: any) => {
            console.error("❌ EmulatorJS错误:", error);
            loading.value = false;
            emit("error", `模拟器错误: ${error}`);
          });

          resolve();
        } else {
          console.error("❌ EJS_emulator仍未定义");
          console.log(
            "可用的EJS变量:",
            Object.keys(window).filter((k) => k.startsWith("EJS"))
          );
          loading.value = false;
          reject(new Error("EmulatorJS初始化超时"));
        }
      }, 5000); // 增加到5秒等待时间
    };

    script.onerror = (error) => {
      console.error("❌ 加载loader.js失败:", error);
      loading.value = false;
      reject(new Error("无法加载EmulatorJS脚本"));
    };

    document.head.appendChild(script);
  });
}

function stopEmulator() {
  emit("stop");
}

function getSystemFromCore(coreId: string): string {
  const systemMap: { [key: string]: string } = {
    gambatte: "gb",
    mgba: "gba",
    snes9x: "snes",
    fceumm: "nes",
    genesis_plus_gx: "segaMD",
    smsplus: "segaMS",
    stella2014: "atari2600",
    a5200: "atari5200",
    desmume2015: "nds",
    mupen64plus_next: "n64",
    mednafen_psx_hw: "psx",
  };

  return systemMap[coreId] || "gb";
}

onUnmounted(() => {
  // Cleanup if needed
});
</script>

<style scoped>
.simple-emulator {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.emulator-container {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.loading-content p {
  opacity: 0.8;
}

.game-display {
  width: 100%;
  min-height: 400px;
  background: #000;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  position: relative;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.controls button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
}

.controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.btn-stop {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: rgba(244, 67, 54, 0.3) !important;
}

.btn-stop:hover {
  background: rgba(244, 67, 54, 0.3) !important;
}

.btn-back {
  background: rgba(108, 117, 125, 0.2) !important;
  border-color: rgba(108, 117, 125, 0.3) !important;
}

.btn-back:hover {
  background: rgba(108, 117, 125, 0.3) !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
