<template>
  <div class="flex flex-col min-h-screen">
    <!-- Hero Section -->
    <section
      class="flex flex-col items-center justify-center flex-1 px-4 py-16 text-center"
    >
      <div class="max-w-4xl mx-auto space-y-8">
        <!-- 主标题 -->
        <h1
          class="flex items-center justify-center gap-4 text-5xl font-bold md:text-7xl text-gradient animate-float"
        >
          <span>🎮</span>
          游戏模拟器
        </h1>

        <!-- 副标题 -->
        <p
          class="max-w-2xl mx-auto text-xl leading-relaxed text-gray-300 md:text-2xl"
        >
          在浏览器中畅玩经典游戏！支持 Game Boy、GBA、NES、SNES 等多种游戏机
        </p>

        <!-- 特性介绍 -->
        <div class="grid grid-cols-1 gap-6 mt-12 md:grid-cols-3">
          <div class="p-6 text-center card">
            <div class="mb-4 text-4xl">🚀</div>
            <h3 class="mb-2 text-lg font-semibold">即开即玩</h3>
            <p class="text-sm text-gray-400">
              无需下载安装，直接在浏览器中运行
            </p>
          </div>

          <div class="p-6 text-center card">
            <div class="mb-4 text-4xl">🎯</div>
            <h3 class="mb-2 text-lg font-semibold">多平台支持</h3>
            <p class="text-sm text-gray-400">支持多种经典游戏机平台和格式</p>
          </div>

          <div class="p-6 text-center card">
            <div class="mb-4 text-4xl">📱</div>
            <h3 class="mb-2 text-lg font-semibold">移动端优化</h3>
            <p class="text-sm text-gray-400">完美适配手机和平板设备</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div
          class="flex flex-col items-center justify-center gap-4 mt-12 sm:flex-row"
        >
          <router-link
            to="/library"
            class="px-8 py-4 text-lg btn-primary animate-bounce-slow hover:animate-none"
          >
            <span>🎮</span>
            开始游戏
          </router-link>

          <router-link to="/about" class="px-8 py-4 text-lg btn-secondary">
            了解更多
          </router-link>
        </div>
      </div>
    </section>

    <!-- 快速开始 -->
    <section class="py-8 border-t bg-black/20 backdrop-blur-sm border-white/10">
      <div class="max-w-4xl px-4 mx-auto text-center">
        <h2 class="mb-4 text-2xl font-bold">快速开始</h2>
        <p class="mb-6 text-gray-300">
          上传你的游戏文件或选择内置游戏，立即开始游戏体验
        </p>
        <div class="flex flex-wrap justify-center gap-4 text-sm">
          <span
            class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300"
          >
            .gb / .gbc
          </span>
          <span
            class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300"
          >
            .gba
          </span>
          <span
            class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300"
          >
            .nes
          </span>
          <span
            class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300"
          >
            .snes / .smc
          </span>
          <span
            class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300"
          >
            .zip
          </span>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 页面组件逻辑
</script>

<style scoped>
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes bounce-slow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}
</style>
