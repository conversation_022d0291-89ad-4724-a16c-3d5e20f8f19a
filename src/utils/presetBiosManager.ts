/*
 * 预置 BIOS 管理器
 * 处理预置 BIOS 配置的下载、安装和管理
 */

import presetBiosConfig from '../config/presetBios.json'
import { storeBiosFile, hasBiosFile } from './biosStorage'

export interface PresetBiosItem {
  id: string
  name: string
  supportedCores: string[]
  filename: string
  description: string
  version: string
  region: string
  size: number
  md5: string
  url: string
  isVerified: boolean
  tags: string[]
  games: string[]
}

export interface PresetBiosConfig {
  version: string
  description: string
  bios: PresetBiosItem[]
}

// 获取预置 BIOS 配置
export function getPresetBiosConfig(): PresetBiosConfig {
  return presetBiosConfig as PresetBiosConfig
}

// 获取所有预置 BIOS 列表
export function getPresetBiosList(): PresetBiosItem[] {
  return presetBiosConfig.bios as PresetBiosItem[]
}

// 根据 ID 获取预置 BIOS
export function getPresetBiosById(id: string): PresetBiosItem | null {
  const bios = presetBiosConfig.bios.find(b => b.id === id)
  return bios as PresetBiosItem || null
}

// 根据文件名获取预置 BIOS
export function getPresetBiosByFilename(filename: string): PresetBiosItem | null {
  const bios = presetBiosConfig.bios.find(b => b.filename === filename)
  return bios as PresetBiosItem || null
}

// 检查预置 BIOS 是否已安装
export async function isPresetBiosInstalled(biosId: string): Promise<boolean> {
  const presetBios = getPresetBiosById(biosId)
  if (!presetBios) return false

  return await hasBiosFile(presetBios.filename)
}

// 下载并安装预置 BIOS
export async function downloadAndInstallPresetBios(
  biosId: string,
  onProgress?: (progress: number) => void
): Promise<boolean> {
  const presetBios = getPresetBiosById(biosId)
  if (!presetBios) {
    throw new Error(`未找到 ID 为 ${biosId} 的预置 BIOS`)
  }

  if (!presetBios.url) {
    throw new Error(`BIOS ${presetBios.name} 没有可用的下载链接`)
  }

  try {
    console.log(`🔽 开始下载 BIOS: ${presetBios.name}`)

    // 检查是否已经安装
    if (await isPresetBiosInstalled(biosId)) {
      console.log(`✅ BIOS ${presetBios.name} 已经安装`)
      return true
    }

    // 下载文件
    const response = await fetch(presetBios.url)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    // 获取文件大小用于进度计算
    const contentLength = response.headers.get('content-length')
    const totalSize = contentLength ? parseInt(contentLength, 10) : presetBios.size

    if (!response.body) {
      throw new Error('响应体为空')
    }

    // 使用 ReadableStream 读取数据并显示进度
    const reader = response.body.getReader()
    const chunks: Uint8Array[] = []
    let receivedLength = 0

    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      chunks.push(value)
      receivedLength += value.length

      // 更新进度
      if (onProgress && totalSize > 0) {
        const progress = Math.round((receivedLength / totalSize) * 100)
        onProgress(progress)
      }
    }

    // 合并所有数据块
    const allChunks = new Uint8Array(receivedLength)
    let position = 0
    for (const chunk of chunks) {
      allChunks.set(chunk, position)
      position += chunk.length
    }

    // 验证文件大小
    if (presetBios.size > 0 && allChunks.length !== presetBios.size) {
      console.warn(`⚠️ 文件大小不匹配: 期望 ${presetBios.size}, 实际 ${allChunks.length}`)
    }

    // 存储 BIOS 文件
    const biosFile = {
      name: presetBios.filename,
      data: allChunks.buffer,
      size: allChunks.length,
      system: presetBios.name,
      uploadDate: new Date(),
      isPreset: true,
      presetId: presetBios.id
    }

    await storeBiosFile(biosFile)

    console.log(`✅ BIOS ${presetBios.name} 下载并安装成功`)
    return true

  } catch (error) {
    console.error(`❌ 下载 BIOS ${presetBios.name} 失败:`, error)
    throw error
  }
}

// 根据核心 ID 获取推荐的 BIOS
export function getRecommendedPresetBiosByCore(coreId: string): PresetBiosItem[] {
  const lowerCoreId = coreId.toLowerCase()

  return presetBiosConfig.bios.filter(bios => {
    // FBNeo 核心 - 主要用于街机游戏
    if (lowerCoreId.includes('fbneo') || lowerCoreId.includes('fbalpha')) {
      return bios.tags.includes('neogeo') ||
        bios.tags.includes('pgm') ||
        bios.tags.includes('naomi') ||
        bios.tags.includes('atomiswave')
    }

    // PlayStation 核心
    if (lowerCoreId.includes('psx') || lowerCoreId.includes('beetle') || lowerCoreId.includes('pcsx')) {
      return bios.tags.includes('playstation')
    }

    // Dreamcast 核心
    if (lowerCoreId.includes('flycast') || lowerCoreId.includes('reicast')) {
      return bios.tags.includes('dreamcast')
    }

    // Saturn 核心
    if (lowerCoreId.includes('saturn') || lowerCoreId.includes('yabause')) {
      return bios.tags.includes('saturn')
    }

    return false
  })
}

// 获取推荐的 BIOS（基于游戏名称）
export function getRecommendedPresetBios(gameName: string): PresetBiosItem[] {
  const lowerGameName = gameName.toLowerCase()

  return presetBiosConfig.bios.filter(bios => {
    // 检查游戏列表
    const matchesGames = bios.games.some(game =>
      lowerGameName.includes(game.toLowerCase()) ||
      game.toLowerCase().includes(lowerGameName)
    )

    // 检查标签
    const matchesTags = bios.tags.some(tag =>
      lowerGameName.includes(tag.toLowerCase())
    )

    // 检查文件名模式和特殊匹配
    const matchesFilename = bios.tags.some(tag => {
      // Neo Geo 游戏匹配
      if (tag === 'neogeo' && (
        lowerGameName.includes('kof') ||
        lowerGameName.includes('拳皇') ||
        lowerGameName.includes('metal') ||
        lowerGameName.includes('合金弹头') ||
        lowerGameName.includes('samurai') ||
        lowerGameName.includes('侍魂') ||
        lowerGameName.includes('garou') ||
        lowerGameName.includes('饿狼')
      )) {
        return true
      }

      // PGM 游戏匹配
      if (tag === 'pgm' && (
        lowerGameName.includes('kov') ||
        lowerGameName.includes('三国') ||
        lowerGameName.includes('西游') ||
        lowerGameName.includes('傲剑')
      )) {
        return true
      }

      // Naomi 游戏匹配
      if (tag === 'naomi' && (
        lowerGameName.includes('crazy') ||
        lowerGameName.includes('taxi') ||
        lowerGameName.includes('出租车') ||
        lowerGameName.includes('virtua') ||
        lowerGameName.includes('fighter')
      )) {
        return true
      }

      // PlayStation 游戏匹配
      if (tag === 'playstation' && (
        lowerGameName.includes('ff') ||
        lowerGameName.includes('final') ||
        lowerGameName.includes('最终幻想') ||
        lowerGameName.includes('resident') ||
        lowerGameName.includes('生化危机') ||
        lowerGameName.includes('tekken') ||
        lowerGameName.includes('铁拳')
      )) {
        return true
      }

      // Dreamcast 游戏匹配
      if (tag === 'dreamcast' && (
        lowerGameName.includes('shenmue') ||
        lowerGameName.includes('莎木') ||
        lowerGameName.includes('sonic') ||
        lowerGameName.includes('索尼克')
      )) {
        return true
      }

      return false
    })

    return matchesGames || matchesTags || matchesFilename
  }) as PresetBiosItem[]
}

// 批量安装推荐的 BIOS
export async function installRecommendedBios(
  gameName: string,
  onProgress?: (biosId: string, progress: number) => void
): Promise<{ success: string[], failed: string[] }> {
  const recommended = getRecommendedPresetBios(gameName)
  const results = { success: [] as string[], failed: [] as string[] }

  for (const bios of recommended) {
    try {
      // 检查是否已安装
      if (await isPresetBiosInstalled(bios.id)) {
        results.success.push(bios.id)
        continue
      }

      // 只下载有 URL 的 BIOS
      if (bios.url) {
        await downloadAndInstallPresetBios(bios.id, (progress) => {
          onProgress?.(bios.id, progress)
        })
        results.success.push(bios.id)
      } else {
        console.warn(`⚠️ BIOS ${bios.name} 没有下载链接，跳过`)
        results.failed.push(bios.id)
      }
    } catch (error) {
      console.error(`安装 BIOS ${bios.name} 失败:`, error)
      results.failed.push(bios.id)
    }
  }

  return results
}

// 获取 BIOS 安装状态
export async function getBiosInstallationStatus(): Promise<{
  installed: string[]
  available: string[]
  total: number
}> {
  const allBios = getPresetBiosList()
  const installed: string[] = []
  const available: string[] = []

  for (const bios of allBios) {
    if (await isPresetBiosInstalled(bios.id)) {
      installed.push(bios.id)
    } else if (bios.url) {
      available.push(bios.id)
    }
  }

  return {
    installed,
    available,
    total: allBios.length
  }
}
