/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-30 14:57:00
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-30 14:57:00
 * @Description: IndexedDB 游戏数据存储工具
 */

import type { GameFile } from '../types/emulator'

const DB_NAME = 'GameEmulatorDB'
const DB_VERSION = 1
const STORE_NAME = 'games'

export interface StoredGame {
  id: string
  name: string
  extension: string
  data: ArrayBuffer
  size: number
  system?: string
  timestamp: number
}

class GameStorage {
  private db: IDBDatabase | null = null

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        console.error('IndexedDB 打开失败:', request.error)
        reject(request.error)
      }

      request.onsuccess = () => {
        this.db = request.result
        console.log('IndexedDB 初始化成功')
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // 创建对象存储
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' })
          store.createIndex('name', 'name', { unique: false })
          store.createIndex('timestamp', 'timestamp', { unique: false })
          console.log('IndexedDB 对象存储创建成功')
        }
      }
    })
  }

  async saveGame(game: GameFile): Promise<string> {
    if (!this.db) {
      await this.init()
    }

    // 检查是否已存在相同的游戏（基于名称和大小）
    const existingGame = await this.findExistingGame(game.name, game.size)
    if (existingGame) {
      // 如果游戏已存在，更新时间戳并返回现有ID
      await this.updateGameTimestamp(existingGame.id)
      console.log('游戏已存在，更新时间戳:', existingGame.id)
      return existingGame.id
    }

    const gameId = `game_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

    const storedGame: StoredGame = {
      id: gameId,
      name: game.name,
      extension: game.extension,
      data: game.data,
      size: game.size,
      system: game.system,
      timestamp: Date.now()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.put(storedGame)

      request.onsuccess = () => {
        console.log('游戏数据保存成功:', gameId)
        resolve(gameId)
      }

      request.onerror = () => {
        console.error('游戏数据保存失败:', request.error)
        reject(request.error)
      }
    })
  }

  private async findExistingGame(name: string, size: number): Promise<StoredGame | null> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.getAll()

      request.onsuccess = () => {
        const games = request.result as StoredGame[]
        const existingGame = games.find(g => g.name === name && g.size === size)
        resolve(existingGame || null)
      }

      request.onerror = () => {
        console.error('查找现有游戏失败:', request.error)
        reject(request.error)
      }
    })
  }

  async getGame(gameId: string): Promise<GameFile | null> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.get(gameId)

      request.onsuccess = () => {
        const storedGame = request.result as StoredGame
        if (storedGame) {
          const gameFile: GameFile = {
            name: storedGame.name,
            extension: storedGame.extension,
            data: storedGame.data,
            size: storedGame.size,
            system: storedGame.system
          }
          console.log('游戏数据读取成功:', gameId, '大小:', storedGame.size, '字节')
          resolve(gameFile)
        } else {
          console.log('未找到游戏数据:', gameId)
          resolve(null)
        }
      }

      request.onerror = () => {
        console.error('游戏数据读取失败:', request.error)
        reject(request.error)
      }
    })
  }

  async deleteGame(gameId: string): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.delete(gameId)

      request.onsuccess = () => {
        console.log('游戏数据删除成功:', gameId)
        resolve()
      }

      request.onerror = () => {
        console.error('游戏数据删除失败:', request.error)
        reject(request.error)
      }
    })
  }

  async getAllGames(): Promise<StoredGame[]> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.getAll()

      request.onsuccess = () => {
        const games = request.result as StoredGame[]
        console.log('获取所有游戏数据成功:', games.length, '个游戏')
        resolve(games)
      }

      request.onerror = () => {
        console.error('获取所有游戏数据失败:', request.error)
        reject(request.error)
      }
    })
  }

  async getRecentGames(limit: number = 5): Promise<StoredGame[]> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const index = store.index('timestamp')
      const request = index.openCursor(null, 'prev') // 按时间戳降序

      const games: StoredGame[] = []
      let count = 0

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor && count < limit) {
          games.push(cursor.value as StoredGame)
          count++
          cursor.continue()
        } else {
          console.log('获取最近游戏数据成功:', games.length, '个游戏')
          resolve(games)
        }
      }

      request.onerror = () => {
        console.error('获取最近游戏数据失败:', request.error)
        reject(request.error)
      }
    })
  }

  async updateGameTimestamp(gameId: string): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const getRequest = store.get(gameId)

      getRequest.onsuccess = () => {
        const storedGame = getRequest.result as StoredGame
        if (storedGame) {
          storedGame.timestamp = Date.now()
          const putRequest = store.put(storedGame)

          putRequest.onsuccess = () => {
            console.log('游戏时间戳更新成功:', gameId)
            resolve()
          }

          putRequest.onerror = () => {
            console.error('游戏时间戳更新失败:', putRequest.error)
            reject(putRequest.error)
          }
        } else {
          reject(new Error('游戏不存在'))
        }
      }

      getRequest.onerror = () => {
        console.error('获取游戏数据失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  }

  async updateGameName(gameId: string, newName: string): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const getRequest = store.get(gameId)

      getRequest.onsuccess = () => {
        const storedGame = getRequest.result as StoredGame
        if (storedGame) {
          storedGame.name = newName
          storedGame.timestamp = Date.now() // 同时更新时间戳
          const putRequest = store.put(storedGame)

          putRequest.onsuccess = () => {
            console.log('游戏名称更新成功:', gameId, '->', newName)
            resolve()
          }

          putRequest.onerror = () => {
            console.error('游戏名称更新失败:', putRequest.error)
            reject(putRequest.error)
          }
        } else {
          reject(new Error('游戏不存在'))
        }
      }

      getRequest.onerror = () => {
        console.error('获取游戏数据失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  }

  async clearOldGames(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    const cutoffTime = Date.now() - maxAge

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const index = store.index('timestamp')
      const request = index.openCursor(IDBKeyRange.upperBound(cutoffTime))

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          cursor.delete()
          cursor.continue()
        } else {
          console.log('旧游戏数据清理完成')
          resolve()
        }
      }

      request.onerror = () => {
        console.error('清理旧游戏数据失败:', request.error)
        reject(request.error)
      }
    })
  }
}

// 创建单例实例
export const gameStorage = new GameStorage()
