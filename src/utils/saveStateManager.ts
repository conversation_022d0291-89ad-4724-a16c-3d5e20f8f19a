/**
 * 游戏进度保存管理器
 * 支持自动保存、手动保存、导入导出进度
 */

export interface SaveState {
  id: string;
  gameName: string;
  gameSystem: string;
  saveData: ArrayBuffer;
  screenshot?: string; // base64 截图
  saveTime: Date;
  autoSave: boolean; // 是否为自动保存
  description?: string;
}

export interface GameProgress {
  gameName: string;
  gameSystem: string;
  lastPlayTime: Date;
  totalPlayTime: number; // 总游戏时间（秒）
  saveStates: SaveState[];
  quickSave?: SaveState; // 快速保存槽
  autoSave?: SaveState; // 自动保存槽
}

class SaveStateManager {
  private dbName = 'GameEmulatorSaves';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;
  private autoSaveInterval: number | null = null;
  private currentGame: string | null = null;
  private gameStartTime: Date | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建游戏进度存储
        if (!db.objectStoreNames.contains('gameProgress')) {
          const progressStore = db.createObjectStore('gameProgress', { keyPath: 'gameName' });
          progressStore.createIndex('gameSystem', 'gameSystem', { unique: false });
          progressStore.createIndex('lastPlayTime', 'lastPlayTime', { unique: false });
        }

        // 创建存档存储
        if (!db.objectStoreNames.contains('saveStates')) {
          const saveStore = db.createObjectStore('saveStates', { keyPath: 'id' });
          saveStore.createIndex('gameName', 'gameName', { unique: false });
          saveStore.createIndex('saveTime', 'saveTime', { unique: false });
          saveStore.createIndex('autoSave', 'autoSave', { unique: false });
        }
      };
    });
  }

  // 开始游戏会话
  startGameSession(gameName: string, _gameSystem: string): void {
    this.currentGame = gameName;
    this.gameStartTime = new Date();

    // 启动自动保存（5秒间隔）
    this.startAutoSave();

    console.log(`🎮 开始游戏会话: ${gameName}`, {
      currentGame: this.currentGame,
      gameStartTime: this.gameStartTime
    });
  }

  // 获取当前游戏状态（调试用）
  getCurrentGameStatus() {
    const emulator = (window as any).EJS_emulator;
    const gameManager = emulator?.gameManager;

    return {
      currentGame: this.currentGame,
      gameStartTime: this.gameStartTime,
      autoSaveInterval: this.autoSaveInterval,
      emulatorExists: !!emulator,
      gameManagerExists: !!gameManager,
      emulatorMethods: emulator ? Object.keys(emulator).filter(key => typeof emulator[key] === 'function') : [],
      emulatorProperties: emulator ? Object.keys(emulator) : [],
      gameManagerMethods: gameManager ? Object.keys(gameManager).filter(key => typeof gameManager[key] === 'function') : [],
      gameManagerProperties: gameManager ? Object.keys(gameManager) : [],
      // 检查关键方法
      getStateAvailable: !!(gameManager?.getState),
      loadStateAvailable: !!(gameManager?.loadState),
      supportsStates: gameManager ? gameManager.supportsStates() : false
    };
  }

  // 结束游戏会话
  async endGameSession(): Promise<void> {
    if (!this.currentGame || !this.gameStartTime) return;

    // 停止自动保存
    this.stopAutoSave();

    // 更新游戏时间
    const playTime = (new Date().getTime() - this.gameStartTime.getTime()) / 1000;
    await this.updateGameProgress(this.currentGame, playTime);

    console.log(`🎮 结束游戏会话: ${this.currentGame}, 游戏时长: ${Math.round(playTime)}秒`);

    this.currentGame = null;
    this.gameStartTime = null;
  }

  // 启动自动保存
  private startAutoSave(): void {
    this.stopAutoSave(); // 先停止之前的自动保存

    this.autoSaveInterval = window.setInterval(async () => {
      if (this.currentGame && (window as any).EJS_emulator) {
        try {
          await this.autoSaveGame();
        } catch (error) {
          console.warn('自动保存失败:', error);
        }
      }
    }, 5000); // 5秒自动保存一次
  }

  // 停止自动保存
  private stopAutoSave(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
  }

  // 自动保存游戏
  private async autoSaveGame(): Promise<void> {
    if (!this.currentGame) return;

    const emulator = (window as any).EJS_emulator;
    if (!emulator || !emulator.gameManager || !emulator.gameManager.getState) return;

    try {
      // 使用 EmulatorJS 的 gameManager.getState() 方法
      const saveData = emulator.gameManager.getState();
      if (saveData) {
        // 删除之前的自动保存存档
        const progress = await this.getGameProgress(this.currentGame);
        if (progress?.autoSave) {
          await this.deleteSaveState(progress.autoSave.id);
        }

        const saveState: SaveState = {
          id: `${this.currentGame}_auto`,  // 固定ID，自动覆盖
          gameName: this.currentGame,
          gameSystem: this.getCurrentGameSystem(),
          saveData: saveData.buffer, // 转换为 ArrayBuffer
          saveTime: new Date(),
          autoSave: true,
          description: '自动保存'
        };

        await this.saveSaveState(saveState);
        await this.updateAutoSave(this.currentGame, saveState);

        console.log(`💾 自动保存完成: ${this.currentGame}`);
      }
    } catch (error) {
      console.warn('自动保存失败:', error);
    }
  }

  // 手动保存游戏
  async manualSaveGame(description?: string, gameName?: string): Promise<SaveState | null> {
    // 如果没有当前游戏但提供了游戏名称，使用提供的游戏名称
    const gameToSave = this.currentGame || gameName;

    if (!gameToSave) {
      console.error('保存状态调试信息:', {
        currentGame: this.currentGame,
        providedGameName: gameName,
        emulatorExists: !!(window as any).EJS_emulator
      });
      throw new Error('没有正在进行的游戏');
    }

    try {
      // 使用 EmulatorJS 的 gameManager.getState() 方法
      const emulator = (window as any).EJS_emulator;
      if (!emulator || !emulator.gameManager) {
        throw new Error('EmulatorJS gameManager 不可用');
      }

      console.log("🎮 开始手动保存游戏状态...");

      // 获取保存状态数据
      const saveData = emulator.gameManager.getState();
      console.log("📦 获取到保存数据:", saveData);

      // 获取截图
      const screenshot = await this.captureScreenshot();

      const saveState: SaveState = {
        id: `${gameToSave}_manual_${Date.now()}`,
        gameName: gameToSave,
        gameSystem: this.getCurrentGameSystem(),
        saveData: saveData.buffer, // 转换为 ArrayBuffer
        screenshot: screenshot,
        saveTime: new Date(),
        autoSave: false,
        description: description || `手动保存 ${new Date().toLocaleString()}`
      };

      await this.saveSaveState(saveState);
      await this.addSaveStateToProgress(gameToSave, saveState);

      console.log("💾 保存状态成功:", saveState.id);
      return saveState;
    } catch (error) {
      console.error("保存状态失败:", error);
      throw error;
    }
  }

  // 快速保存
  async quickSaveGame(gameName?: string): Promise<SaveState | null> {
    const gameToSave = this.currentGame || gameName;
    if (!gameToSave) return null;

    try {
      // 使用 EmulatorJS 的 gameManager.getState() 方法
      const emulator = (window as any).EJS_emulator;
      if (!emulator || !emulator.gameManager) {
        console.warn('EmulatorJS gameManager 不可用，快速保存失败');
        return null;
      }

      console.log("⚡ 开始快速保存游戏状态...");

      // 获取保存状态数据
      const saveData = emulator.gameManager.getState();
      console.log("📦 获取到快速保存数据:", saveData);

      // 获取截图
      const screenshot = await this.captureScreenshot();

      const saveState: SaveState = {
        id: `${gameToSave}_quick`,
        gameName: gameToSave,
        gameSystem: this.getCurrentGameSystem(),
        saveData: saveData.buffer, // 转换为 ArrayBuffer
        screenshot: screenshot,
        saveTime: new Date(),
        autoSave: false,
        description: '快速保存'
      };

      await this.saveSaveState(saveState);
      await this.updateQuickSave(gameToSave, saveState);

      console.log(`⚡ 快速保存完成: ${gameToSave}`);
      return saveState;
    } catch (error) {
      console.error('快速保存失败:', error);
      return null;
    }
  }

  // 加载存档
  async loadSaveState(saveStateId: string): Promise<boolean> {
    const emulator = (window as any).EJS_emulator;
    if (!emulator || !emulator.gameManager) {
      throw new Error('模拟器未初始化');
    }

    if (!emulator.gameManager.loadState) {
      throw new Error('模拟器不支持读档功能');
    }

    // 检查游戏是否已启动
    if (!emulator.started) {
      throw new Error('请先启动游戏再读取存档');
    }

    try {
      const saveState = await this.getSaveState(saveStateId);
      if (!saveState) {
        throw new Error('存档不存在');
      }

      // 将 ArrayBuffer 转换为 Uint8Array
      const stateData = new Uint8Array(saveState.saveData);

      console.log(`📂 开始读档: ${saveState.description}`, {
        dataSize: stateData.length,
        gameStarted: emulator.started,
        hasGameManager: !!emulator.gameManager,
        hasLoadState: !!emulator.gameManager.loadState
      });

      // 使用 gameManager.loadState 方法
      emulator.gameManager.loadState(stateData);
      console.log(`📂 读档完成: ${saveState.description}`);
      return true;
    } catch (error) {
      console.error('读档失败:', error);
      throw error;
    }
  }

  // 获取游戏进度
  async getGameProgress(gameName: string): Promise<GameProgress | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['gameProgress'], 'readonly');
      const store = transaction.objectStore('gameProgress');
      const request = store.get(gameName);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 检查游戏是否有存档
  async hasGameProgress(gameName: string): Promise<boolean> {
    const progress = await this.getGameProgress(gameName);
    return progress !== null && (
      progress.quickSave !== undefined ||
      progress.autoSave !== undefined ||
      progress.saveStates.length > 0
    );
  }

  // 获取最新存档（用于继续游戏）
  async getLatestSaveState(gameName: string): Promise<SaveState | null> {
    const progress = await this.getGameProgress(gameName);
    if (!progress) return null;

    // 优先返回快速保存，然后是自动保存，最后是最新的手动保存
    if (progress.quickSave) return progress.quickSave;
    if (progress.autoSave) return progress.autoSave;
    if (progress.saveStates.length > 0) {
      return progress.saveStates.sort((a, b) =>
        new Date(b.saveTime).getTime() - new Date(a.saveTime).getTime()
      )[0];
    }

    return null;
  }

  // 删除存档
  async deleteSaveState(saveStateId: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['saveStates'], 'readwrite');
      const store = transaction.objectStore('saveStates');
      const request = store.delete(saveStateId);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 导出存档
  async exportSaveState(saveStateId: string): Promise<Blob> {
    const saveState = await this.getSaveState(saveStateId);
    if (!saveState) {
      throw new Error('存档不存在');
    }

    const exportData = {
      version: '1.0',
      saveState: {
        ...saveState,
        saveData: Array.from(new Uint8Array(saveState.saveData)) // 转换为数组以便JSON序列化
      }
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    return new Blob([jsonString], { type: 'application/json' });
  }

  // 导入存档
  async importSaveState(file: File): Promise<SaveState> {
    const text = await file.text();
    const importData = JSON.parse(text);

    if (!importData.version || !importData.saveState) {
      throw new Error('无效的存档文件格式');
    }

    const saveState: SaveState = {
      ...importData.saveState,
      id: `${importData.saveState.gameName}_import_${Date.now()}`, // 生成新的ID避免冲突
      saveData: new Uint8Array(importData.saveState.saveData).buffer, // 转换回ArrayBuffer
      saveTime: new Date(importData.saveState.saveTime)
    };

    await this.saveSaveState(saveState);
    await this.addSaveStateToProgress(saveState.gameName, saveState);

    console.log(`📥 导入存档完成: ${saveState.gameName}`);
    return saveState;
  }

  // 私有方法：保存存档到数据库
  private async saveSaveState(saveState: SaveState): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['saveStates'], 'readwrite');
      const store = transaction.objectStore('saveStates');
      const request = store.put(saveState);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 私有方法：获取存档
  private async getSaveState(saveStateId: string): Promise<SaveState | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['saveStates'], 'readonly');
      const store = transaction.objectStore('saveStates');
      const request = store.get(saveStateId);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // 私有方法：更新游戏进度
  private async updateGameProgress(gameName: string, additionalPlayTime: number): Promise<void> {
    const progress = await this.getGameProgress(gameName) || {
      gameName,
      gameSystem: this.getCurrentGameSystem(),
      lastPlayTime: new Date(),
      totalPlayTime: 0,
      saveStates: []
    };

    progress.lastPlayTime = new Date();
    progress.totalPlayTime += additionalPlayTime;

    await this.saveGameProgress(progress);
  }

  // 私有方法：保存游戏进度
  private async saveGameProgress(progress: GameProgress): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['gameProgress'], 'readwrite');
      const store = transaction.objectStore('gameProgress');
      const request = store.put(progress);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // 私有方法：添加存档到进度
  private async addSaveStateToProgress(gameName: string, saveState: SaveState): Promise<void> {
    const progress = await this.getGameProgress(gameName) || {
      gameName,
      gameSystem: this.getCurrentGameSystem(),
      lastPlayTime: new Date(),
      totalPlayTime: 0,
      saveStates: []
    };

    progress.saveStates.push(saveState);
    // 保持最多10个手动存档
    progress.saveStates = progress.saveStates
      .filter(s => !s.autoSave)
      .sort((a, b) => new Date(b.saveTime).getTime() - new Date(a.saveTime).getTime())
      .slice(0, 10);

    await this.saveGameProgress(progress);
  }

  // 私有方法：更新快速保存
  private async updateQuickSave(gameName: string, saveState: SaveState): Promise<void> {
    const progress = await this.getGameProgress(gameName) || {
      gameName,
      gameSystem: this.getCurrentGameSystem(),
      lastPlayTime: new Date(),
      totalPlayTime: 0,
      saveStates: []
    };

    progress.quickSave = saveState;
    await this.saveGameProgress(progress);
  }

  // 私有方法：更新自动保存
  private async updateAutoSave(gameName: string, saveState: SaveState): Promise<void> {
    const progress = await this.getGameProgress(gameName) || {
      gameName,
      gameSystem: this.getCurrentGameSystem(),
      lastPlayTime: new Date(),
      totalPlayTime: 0,
      saveStates: []
    };

    progress.autoSave = saveState;
    await this.saveGameProgress(progress);
  }

  // 移除快速保存
  async removeQuickSave(gameName: string): Promise<void> {
    const progress = await this.getGameProgress(gameName);
    if (progress && progress.quickSave) {
      progress.quickSave = undefined;
      await this.saveGameProgress(progress);
    }
  }

  // 移除自动保存
  async removeAutoSave(gameName: string): Promise<void> {
    const progress = await this.getGameProgress(gameName);
    if (progress && progress.autoSave) {
      progress.autoSave = undefined;
      await this.saveGameProgress(progress);
    }
  }

  // 从手动保存列表中移除存档
  async removeManualSave(gameName: string, saveStateId: string): Promise<void> {
    const progress = await this.getGameProgress(gameName);
    if (progress) {
      const saveIndex = progress.saveStates.findIndex(s => s.id === saveStateId);
      if (saveIndex !== -1) {
        progress.saveStates.splice(saveIndex, 1);
        await this.saveGameProgress(progress);
      }
    }
  }

  // 私有方法：获取当前游戏系统
  private getCurrentGameSystem(): string {
    return (window as any).EJS_system || 'unknown';
  }

  // 私有方法：截取游戏截图
  private async captureScreenshot(): Promise<string | undefined> {
    try {
      const canvas = document.querySelector('#canvas canvas') as HTMLCanvasElement;
      if (canvas) {
        return canvas.toDataURL('image/png');
      }
    } catch (error) {
      console.warn('截图失败:', error);
    }
    return undefined;
  }
}

// 导出单例实例
export const saveStateManager = new SaveStateManager();
