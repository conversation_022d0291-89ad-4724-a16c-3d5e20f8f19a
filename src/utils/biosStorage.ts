/*
 * BIOS 文件存储管理
 * 使用 IndexedDB 存储 BIOS 文件
 */

interface BiosFile {
  name: string
  data: ArrayBuffer
  size: number
  system?: string
  uploadDate?: Date
}

const DB_NAME = 'EmulatorBiosDB'
const DB_VERSION = 1
const STORE_NAME = 'biosFiles'

// 初始化 IndexedDB
function initDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result

      // 创建对象存储
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'name' })
        store.createIndex('system', 'system', { unique: false })
        store.createIndex('uploadDate', 'uploadDate', { unique: false })
      }
    }
  })
}

// 存储 BIOS 文件
export async function storeBiosFile(biosFile: BiosFile): Promise<void> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)

    const biosData = {
      ...biosFile,
      uploadDate: new Date()
    }

    const request = store.put(biosData)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      console.log(`BIOS 文件 ${biosFile.name} 已存储`)
      resolve()
    }

    transaction.onerror = () => reject(transaction.error)
  })
}

// 获取所有 BIOS 文件
export async function getAllBiosFiles(): Promise<BiosFile[]> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.getAll()

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
  })
}

// 根据名称获取 BIOS 文件
export async function getBiosFile(name: string): Promise<BiosFile | null> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.get(name)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result || null)
  })
}

// 根据系统类型获取 BIOS 文件
export async function getBiosFilesBySystem(system: string): Promise<BiosFile[]> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly')
    const store = transaction.objectStore(STORE_NAME)
    const index = store.index('system')
    const request = index.getAll(system)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
  })
}

// 删除 BIOS 文件
export async function removeBiosFile(name: string): Promise<void> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.delete(name)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      console.log(`BIOS 文件 ${name} 已删除`)
      resolve()
    }
  })
}

// 检查是否存在特定的 BIOS 文件
export async function hasBiosFile(name: string): Promise<boolean> {
  try {
    const biosFile = await getBiosFile(name)
    return biosFile !== null
  } catch (error) {
    console.error('检查 BIOS 文件失败:', error)
    return false
  }
}

// 清空所有 BIOS 文件
export async function clearAllBiosFiles(): Promise<void> {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.clear()

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      console.log('所有 BIOS 文件已清空')
      resolve()
    }
  })
}

// 获取 BIOS 文件的 Blob URL（用于传递给模拟器）
export async function getBiosFileUrl(name: string): Promise<string | null> {
  try {
    const biosFile = await getBiosFile(name)
    if (!biosFile) return null

    const blob = new Blob([biosFile.data], { type: 'application/octet-stream' })
    return URL.createObjectURL(blob)
  } catch (error) {
    console.error('获取 BIOS 文件 URL 失败:', error)
    return null
  }
}

// 常用 BIOS 文件映射
export const COMMON_BIOS_FILES = {
  'neogeo': ['neogeo.zip'],
  'pgm': ['pgm.zip'],
  'playstation': ['scph1001.bin', 'scph5501.bin', 'scph7001.bin'],
  'dreamcast': ['dc_boot.bin', 'dc_flash.bin'],
  'saturn': ['sega_101.bin', 'mpr-17933.bin'],
  'sega32x': ['32x_g_bios.bin', '32x_m_bios.bin', '32x_s_bios.bin'],
  'segacd': ['bios_CD_E.bin', 'bios_CD_U.bin', 'bios_CD_J.bin'],
  'pcengine': ['syscard3.pce'],
  'fds': ['disksys.rom'],
  'gba': ['gba_bios.bin'],
  'nds': ['bios7.bin', 'bios9.bin', 'firmware.bin']
} as const

// 根据游戏系统获取推荐的 BIOS 文件
export function getRecommendedBiosFiles(system: string): string[] {
  const systemKey = system.toLowerCase() as keyof typeof COMMON_BIOS_FILES
  return [...(COMMON_BIOS_FILES[systemKey] || [])]
}

// 检查系统所需的 BIOS 文件是否都已上传
export async function checkSystemBiosFiles(system: string): Promise<{
  required: string[]
  available: string[]
  missing: string[]
}> {
  const required = getRecommendedBiosFiles(system)
  const available: string[] = []
  const missing: string[] = []

  for (const biosName of required) {
    const exists = await hasBiosFile(biosName)
    if (exists) {
      available.push(biosName)
    } else {
      missing.push(biosName)
    }
  }

  return { required, available, missing }
}
