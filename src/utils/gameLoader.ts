/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:22:40
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-30 17:13:20
 * @Description: 
 * 
 */
import type { GameFile } from '../types/emulator'
import { getFileExtension } from './fileHandler'

export interface GameInfo {
  name: string
  path: string
  description?: string
  system?: string
  icon?: string
}

export async function loadGameFromUrl(url: string, filename: string): Promise<GameFile> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`获取游戏文件失败: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const extension = getFileExtension(filename)

    return {
      name: filename,
      extension,
      data: arrayBuffer,
      size: arrayBuffer.byteLength
    }
  } catch (error) {
    console.error('从URL加载游戏时出错:', error)
    throw error
  }
}

// 获取所有可用的本地游戏列表
export async function getAvailableGames(): Promise<GameInfo[]> {
  try {
    // 尝试从游戏配置文件获取游戏列表
    const response = await fetch('/games/games.json')
    if (response.ok) {
      const gamesConfig = await response.json()
      console.log('从配置文件加载游戏列表:', gamesConfig)
      return gamesConfig.games || []
    }
  } catch (error) {
    console.log('无法加载游戏配置文件，尝试自动发现游戏')
  }

  // 备用方案：尝试检查常见的游戏文件
  const potentialGames = [
    { name: '新超级马里奥', path: '/games/新超级马里奥.zip' },
    { name: '星之卡比', path: '/games/星之卡比.zip' }
  ]

  const availableGames: GameInfo[] = []

  for (const game of potentialGames) {
    try {
      const response = await fetch(game.path, { method: 'HEAD' })
      if (response.ok) {
        availableGames.push(game)
        console.log(`发现游戏: ${game.name}`)
      }
    } catch (e) {
      console.log(`游戏文件不存在: ${game.path}`)
    }
  }

  return availableGames
}

// 从本地游戏路径加载游戏
export async function loadLocalGame(gamePath: string, gameName: string): Promise<GameFile | null> {
  try {
    console.log('正在加载本地游戏:', gameName, '路径:', gamePath)

    const response = await fetch(gamePath)
    if (!response.ok) {
      console.warn('未找到游戏文件:', gamePath)
      return null
    }

    const arrayBuffer = await response.arrayBuffer()
    const extension = getFileExtension(gameName)

    // 如果是 ZIP 文件，尝试解压
    if (extension === 'zip') {
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()
      const zipContent = await zip.loadAsync(arrayBuffer)

      console.log('ZIP文件内容:', Object.keys(zipContent.files))

      // 查找游戏文件（按优先级）
      const gameExtensions = ['nds', 'gb', 'gbc', 'gba', 'nes', 'smc', 'sfc', 'md', 'gen', 'n64', 'v64', 'z64']

      for (const ext of gameExtensions) {
        for (const [filename, zipEntry] of Object.entries(zipContent.files)) {
          if (!zipEntry.dir && filename.toLowerCase().endsWith(`.${ext}`)) {
            const gameData = await zipEntry.async('arraybuffer')
            console.log('找到游戏文件:', filename, '大小:', gameData.byteLength)
            return {
              name: filename,
              extension: ext,
              data: gameData,
              size: gameData.byteLength
            }
          }
        }
      }

      throw new Error('ZIP文件中未找到支持的游戏文件')
    } else {
      // 直接返回游戏文件
      return {
        name: gameName,
        extension,
        data: arrayBuffer,
        size: arrayBuffer.byteLength
      }
    }
  } catch (error) {
    console.warn('无法加载游戏:', gameName, error)
    return null
  }
}

// Load the Game Boy game from root directory (保持向后兼容)
export async function loadDefaultGame(): Promise<GameFile | null> {
  try {
    const gameUrl = '/f2c137516b31f1551fa0b14505dc8290e2fb5040f92abd4cadd4b92c9467b5d6.gb.zip'

    const response = await fetch(gameUrl)
    if (!response.ok) {
      console.warn('未找到默认游戏文件')
      return null
    }

    // Extract the .gb file from the ZIP
    const JSZip = (await import('jszip')).default
    const zipData = await response.arrayBuffer()
    const zip = new JSZip()
    const zipContent = await zip.loadAsync(zipData)

    // Find the first .gb file in the ZIP
    console.log('ZIP文件内容:', Object.keys(zipContent.files));

    for (const [filename, zipEntry] of Object.entries(zipContent.files)) {
      console.log('检查文件:', filename, '是否为目录:', zipEntry.dir);
      if (!zipEntry.dir && filename.toLowerCase().endsWith('.gb')) {
        const gameData = await zipEntry.async('arraybuffer')
        console.log('找到GB文件:', filename, '大小:', gameData.byteLength);
        return {
          name: filename,
          extension: 'gb',
          data: gameData,
          size: gameData.byteLength
        }
      }
    }

    throw new Error('ZIP文件中未找到.gb游戏文件')
  } catch (error) {
    console.warn('无法加载默认游戏:', error)
    return null
  }
}