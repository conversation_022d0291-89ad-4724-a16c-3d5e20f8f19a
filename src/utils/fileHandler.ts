/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:15:48
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-30 17:21:25
 * @Description: 
 * 
 */
import type { GameFile } from '../types/emulator'
import { getAllSupportedExtensions } from './cores'
import JSZip from 'jszip'

// Re-export the function so it can be imported from this module
export { getAllSupportedExtensions }

export async function handleFileUpload(file: File): Promise<GameFile[]> {
  const extension = getFileExtension(file.name)

  if (extension === 'zip') {
    return await handleZipFile(file)
  } else {
    throw new Error(`只支持ZIP文件格式，当前文件格式: .${extension}`)
  }
}

async function handleZipFile(file: File): Promise<GameFile[]> {
  // 首先检查是否是街机游戏ZIP文件（通过文件名判断）
  const fileName = file.name.toLowerCase()
  const isArcadeGame = fileName.includes('kof') || fileName.includes('拳皇') ||
    fileName.includes('king') || fileName.includes('fighter') ||
    fileName.includes('neogeo') || fileName.includes('snk') ||
    fileName.includes('mvs') || fileName.includes('arcade') ||
    fileName.includes('97') || fileName.includes('98') ||
    fileName.includes('2002') || fileName.includes('街机')

  // 如果是街机游戏，直接将ZIP文件作为游戏文件
  if (isArcadeGame) {
    const arrayBuffer = await file.arrayBuffer()
    return [{
      name: file.name,
      extension: 'zip',
      data: arrayBuffer,
      size: arrayBuffer.byteLength,
      system: 'arcade'
    }]
  }

  const supportedExtensions = getAllSupportedExtensions()
  const games: GameFile[] = []
  const foundFiles: string[] = []
  const unsupportedFiles: string[] = []

  try {
    const zip = new JSZip()
    const zipContent = await zip.loadAsync(file)

    // 遍历ZIP文件中的所有文件
    for (const [filename, zipEntry] of Object.entries(zipContent.files)) {
      // 跳过文件夹
      if (zipEntry.dir) continue

      foundFiles.push(filename)
      const fileExtension = getFileExtension(filename)

      // 检查是否是支持的游戏文件格式
      if (supportedExtensions.includes(fileExtension)) {
        const arrayBuffer = await zipEntry.async('arraybuffer')

        games.push({
          name: filename,
          extension: fileExtension,
          data: arrayBuffer,
          size: arrayBuffer.byteLength
        })
      } else {
        unsupportedFiles.push(filename)
      }
    }

    if (games.length === 0) {
      let errorMessage = 'ZIP文件中未找到支持的游戏文件格式！\n\n'

      if (foundFiles.length === 0) {
        errorMessage += 'ZIP文件为空或只包含文件夹。'
      } else {
        errorMessage += `发现的文件：\n${foundFiles.join('\n')}\n\n`
        errorMessage += `支持的格式：${supportedExtensions.map(ext => '.' + ext).join(', ')}\n\n`
        errorMessage += '请确保ZIP文件包含正确格式的游戏文件。'
      }

      throw new Error(errorMessage)
    }

    return games
  } catch (error) {
    if (error instanceof Error) {
      // 如果是我们自定义的错误消息，直接抛出
      if (error.message.includes('ZIP文件中未找到支持的游戏文件格式')) {
        throw error
      }
      throw new Error(`解压ZIP文件失败: ${error.message}`)
    } else {
      throw new Error('解压ZIP文件失败: 未知错误')
    }
  }
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
} 