import { createApp, h } from 'vue'
import Toast, { type ToastProps } from '../components/Toast.vue'

export interface ToastOptions extends Omit<ToastProps, 'message'> {
  message: string
}

class ToastManager {
  private toasts: Array<{ id: string; app: any }> = []

  show(options: ToastOptions) {
    const id = Math.random().toString(36).substr(2, 9)
    
    // 创建一个容器元素
    const container = document.createElement('div')
    container.id = `toast-${id}`
    document.body.appendChild(container)
    
    // 创建Vue应用实例
    const app = createApp({
      render() {
        return h(Toast, {
          ...options,
          onClose: () => {
            this.remove(id)
          }
        })
      }
    })
    
    // 挂载应用
    app.mount(container)
    
    // 保存引用
    this.toasts.push({ id, app })
    
    return id
  }

  remove(id: string) {
    const index = this.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      const { app } = this.toasts[index]
      
      // 卸载应用
      app.unmount()
      
      // 移除容器
      const container = document.getElementById(`toast-${id}`)
      if (container) {
        container.remove()
      }
      
      // 从数组中移除
      this.toasts.splice(index, 1)
    }
  }

  success(message: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'success',
      message,
      ...options
    })
  }

  error(message: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'error',
      message,
      duration: 8000, // 错误消息显示更长时间
      ...options
    })
  }

  warning(message: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'warning',
      message,
      ...options
    })
  }

  info(message: string, options?: Partial<ToastOptions>) {
    return this.show({
      type: 'info',
      message,
      ...options
    })
  }

  clear() {
    this.toasts.forEach(({ id }) => {
      this.remove(id)
    })
  }
}

// 创建全局实例
export const toast = new ToastManager()

// 默认导出
export default toast
