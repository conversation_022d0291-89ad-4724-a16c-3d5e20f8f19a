{"version": "1.0", "description": "RetroHub 预置BIOS配置", "bios": [{"id": "neogeo", "name": "Neo Geo BIOS", "supportedCores": ["arcade", "mame2003", "fbneo", "f<PERSON><PERSON>"], "filename": "neogeo.zip", "description": "Neo Geo BIOS - 支持所有 Neo Geo 街机游戏", "version": "1.0", "region": "Asia", "size": 131072, "md5": "", "url": "https://objectstorageapi.us-east-1.clawcloudrun.com/xeovhv72-1/BIOS/neogeo.zip", "isVerified": true, "tags": ["neogeo", "arcade", "asia", "verified"], "games": ["拳皇", "合金弹头", "侍魂", "饿狼传说", "kof", "king of fighters", "metal slug", "samurai shodown", "garou", "fatal fury", "neogeo"]}, {"id": "pgm", "name": "PGM BIOS", "supportedCores": ["arcade", "mame2003", "fbneo"], "filename": "pgm.zip", "description": "PGM (PolyGame Master) BIOS - 支持 IGS PGM 街机游戏", "version": "1.0", "region": "Asia", "size": 2094636, "md5": "87cc944eef4c671aa2629a8ba48a08e0", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/pgm.zip", "isVerified": true, "tags": ["pgm", "arcade", "asia", "verified"], "games": ["三国战纪", "西游释厄传", "傲剑狂刀"]}, {"id": "naomi", "name": "Naomi BIOS", "supportedCores": ["arcade", "mame2003", "fbneo"], "filename": "naomi.zip", "description": "Sega Naomi BIOS - 支持 Sega Naomi 街机游戏", "version": "1.0", "region": "World", "size": 9321533, "md5": "526eda1e2a7920c92c88178789d71d84", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/naomi.zip", "isVerified": true, "tags": ["naomi", "sega", "arcade", "verified"], "games": ["疯狂出租车", "死亡之屋2", "VR战士3"]}, {"id": "aw<PERSON><PERSON>", "name": "Atomiswave BIOS", "supportedCores": ["arcade", "mame2003", "fbneo"], "filename": "awbios.zip", "description": "Sega Atomiswave BIOS - 支持 Atomiswave 街机游戏", "version": "1.0", "region": "World", "size": 42296, "md5": "85254fbe320ca82a768ec2c26bb08def", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/awbios.zip", "isVerified": true, "tags": ["atomiswave", "sega", "arcade", "verified"], "games": ["拳皇XI", "侍魂6", "合金弹头6"]}, {"id": "skns", "name": "Super Kaneko Nova System BIOS", "supportedCores": ["arcade", "mame2003", "fbneo"], "filename": "skns.zip", "description": "Super Kaneko Nova System BIOS - 支持 SKNS 街机游戏", "version": "1.0", "region": "World", "size": 924762, "md5": "3f956c4e7008804cb47cbde49bd5b908", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/skns.zip", "isVerified": true, "tags": ["skns", "kaneko", "arcade", "verified"], "games": ["Jo<PERSON>o的奇妙冒险", "街头霸王EX"]}, {"id": "scph1001", "name": "PlayStation BIOS (US)", "supportedCores": ["pcsx_rearmed", "beetle_psx", "beetle_psx_hw"], "filename": "scph1001.bin", "description": "Sony PlayStation BIOS (美版) - 支持 PlayStation 游戏", "version": "1.0", "region": "US", "size": 524288, "md5": "924e392ed05558ffdb115408c263dccf", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Sony%20-%20PlayStation/scph1001.bin", "isVerified": true, "tags": ["playstation", "sony", "console", "verified"], "games": ["最终幻想7", "生化危机", "铁拳3"]}, {"id": "scph5500", "name": "PlayStation BIOS (JP)", "supportedCores": ["pcsx_rearmed", "beetle_psx", "beetle_psx_hw"], "filename": "scph5500.bin", "description": "Sony PlayStation BIOS (日版) - 支持 PlayStation 游戏", "version": "1.0", "region": "JP", "size": 524288, "md5": "8dd7d5296a650fac7319bce665a6a53c", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Sony%20-%20PlayStation/scph5500.bin", "isVerified": true, "tags": ["playstation", "sony", "console", "verified"], "games": ["最终幻想7", "生化危机", "铁拳3"]}, {"id": "dc_boot", "name": "Dreamcast BIOS", "supportedCores": ["flycast", "reicast"], "filename": "dc_boot.bin", "description": "Sega Dreamcast BIOS - 支持 Dreamcast 游戏", "version": "1.0", "region": "World", "size": 2097152, "md5": "e10c53c2f8b90bab96ead2d368858623", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Sega%20-%20Dreamcast/dc_boot.bin", "isVerified": true, "tags": ["dreamcast", "sega", "console", "verified"], "games": ["莎木", "疯狂出租车", "索尼克大冒险"]}, {"id": "dc_flash", "name": "Dreamcast Flash", "supportedCores": ["flycast", "reicast"], "filename": "dc_flash.bin", "description": "Sega Dreamcast Flash ROM - Dreamcast 系统配置文件", "version": "1.0", "region": "World", "size": 131072, "md5": "0a93f7940c455905bea6e392dfde92a4", "url": "https://github.com/Abdess/retroarch_system/raw/libretro/Sega%20-%20Dreamcast/dc_flash.bin", "isVerified": true, "tags": ["dreamcast", "sega", "console", "flash", "verified"], "games": ["莎木", "疯狂出租车", "索尼克大冒险"]}]}