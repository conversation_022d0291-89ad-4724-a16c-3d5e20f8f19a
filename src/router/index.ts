import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 页面组件
import Home from '../views/Home.vue'
import GameLibrary from '../views/GameLibrary.vue'
import GamePlay from '../views/GamePlay.vue'
import About from '../views/About.vue'



const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页 - 游戏模拟器'
    }
  },
  {
    path: '/library',
    name: 'GameLibrary',
    component: GameLibrary,
    meta: {
      title: '游戏库 - 游戏模拟器'
    }
  },
  {
    path: '/play',
    name: 'GamePlay',
    component: GamePlay,
    meta: {
      title: '游戏中 - 游戏模拟器'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于 - 游戏模拟器'
    }
  },


  {
    // 404 页面重定向到首页
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, _from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
