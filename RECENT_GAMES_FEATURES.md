# 最近游戏功能说明

## 新增功能

### 1. 最近游戏展示
- 在游戏库页面顶部显示"最近游戏"卡片
- 显示最近6个游戏，按最后游玩时间排序
- 每个游戏卡片包含：
  - 游戏名称（过长显示省略号）
  - 文件格式和大小
  - 最后游玩时间（相对时间显示）

### 2. 游戏去重功能
- **智能去重**：基于游戏名称和文件大小进行去重
- **时间戳更新**：重复上传相同游戏时，只更新最后游玩时间
- **避免重复存储**：相同游戏不会重复占用存储空间

### 3. 游戏重命名功能
- **双击重命名**：双击游戏名称进入编辑模式
- **按钮重命名**：悬停时显示蓝色重命名按钮
- **键盘操作**：
  - Enter键保存重命名
  - Escape键取消编辑
  - 失去焦点时自动保存
- **输入验证**：不允许空名称
- **实时更新**：重命名后立即更新显示和时间戳

### 4. 交互优化
- **删除按钮修复**：
  - 删除按钮现在有正确的z-index层级
  - 添加了`pointer-events-none`到播放覆盖层
  - 删除按钮有悬停效果和背景色
- **快速启动**：点击游戏卡片直接启动游戏
- **批量管理**：支持清空所有游戏记录
- **操作按钮**：悬停时显示重命名（蓝色）和删除（红色）按钮

## 技术实现

### 重命名功能
```typescript
async function updateGameName(gameId: string, newName: string): Promise<void> {
  // 获取游戏数据
  const storedGame = getRequest.result as StoredGame
  if (storedGame) {
    storedGame.name = newName
    storedGame.timestamp = Date.now() // 同时更新时间戳
    // 保存到IndexedDB
  }
}
```

### 去重逻辑
```typescript
// 检查是否已存在相同的游戏（基于名称和大小）
const existingGame = await this.findExistingGame(game.name, game.size)
if (existingGame) {
  // 如果游戏已存在，更新时间戳并返回现有ID
  await this.updateGameTimestamp(existingGame.id)
  return existingGame.id
}
```

### 删除按钮层级修复
```vue
<!-- 删除按钮有正确的z-index -->
<div class="opacity-0 group-hover:opacity-100 transition-opacity relative z-10">
  <button @click.stop="deleteGame(game.id)">
    <!-- 删除图标 -->
  </button>
</div>

<!-- 播放覆盖层不阻挡点击 -->
<div class="... pointer-events-none">
  <!-- 播放按钮 -->
</div>
```

## 使用方法

### 测试去重功能
1. 上传一个游戏ZIP文件
2. 再次上传相同的ZIP文件
3. 观察最近游戏列表，应该只显示一个游戏记录
4. 时间戳应该更新为最新的上传时间

### 测试重命名功能
1. **双击重命名**：双击游戏名称进入编辑模式
2. **按钮重命名**：悬停在游戏卡片上，点击蓝色重命名按钮
3. 输入新名称，按Enter保存或按Escape取消
4. 重命名成功后会显示Toast提示

### 测试删除功能
1. 悬停在游戏卡片上
2. 右下角会出现蓝色重命名按钮和红色删除按钮
3. 点击删除按钮（现在应该可以正常点击）
4. 游戏会从列表中移除

### 测试快速启动
1. 点击任意游戏卡片
2. 游戏应该直接启动
3. 时间戳会自动更新

## 数据持久化
- 所有游戏数据存储在IndexedDB中
- 支持大文件存储
- 浏览器关闭后数据仍然保留
- 自动清理过期数据（可配置）
