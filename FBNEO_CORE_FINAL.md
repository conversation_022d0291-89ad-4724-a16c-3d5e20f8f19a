# 🎮 FBNeo 核心最终配置

## ✅ 已完成的修改

### 1. 核心配置 (src/utils/cores.ts)
```typescript
{
  id: 'fbneo',
  name: 'Arcade (FBNeo)',
  extensions: ['zip', 'neo', 'mvs'],
  description: 'FinalBurn Neo街机模拟器 - 支持拳皇97等经典街机游戏',
  cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fbneo-wasm.data'
}
```

### 2. 系统映射
```typescript
arcade: ['fbneo'],  // 只使用FBNeo核心
neogeo: ['fbneo']
```

### 3. 强制核心选择
```typescript
// 对于街机游戏，强制使用fbneo核心
if (systemType === "arcade") {
  (window as any).EJS_core = "fbneo";
}
```

### 4. WebGL2强制启用
```typescript
// 全局配置
(window as any).EJS_forceLegacyCores = false;

// 街机游戏专用
if (systemType === "arcade") {
  (window as any).EJS_webgl2 = true;
}

// 运行时强制设置
setTimeout(() => {
  if ((window as any).EJS_emulator) {
    const emulator = (window as any).EJS_emulator;
    emulator.webgl2Enabled = true;
    emulator.supportsWebgl2 = true;
  }
}, 100);
```

## 🎯 预期结果

现在系统应该：

1. **✅ 下载正确核心**：`fbneo-wasm.data` (非legacy版本)
2. **✅ 强制使用FBNeo**：不再使用fbalpha2012_cps2
3. **✅ 启用WebGL2**：避免自动添加-legacy后缀
4. **✅ 游戏名称标准化**：拳皇97 → kof97

## 🔧 技术细节

### WebGL2检测逻辑
EmulatorJS内部逻辑：
```javascript
let legacy = (this.supportsWebgl2 && this.webgl2Enabled ? "" : "-legacy");
let filename = this.getCore() + legacy + "-wasm.data";
```

我们的解决方案：
- 设置 `EJS_forceLegacyCores = false`
- 设置 `EJS_webgl2 = true`
- 运行时强制 `webgl2Enabled = true`

### 核心文件映射
- **期望下载**：`fbneo-wasm.data`
- **避免下载**：`fbneo-legacy-wasm.data`
- **核心类型**：现代WebGL2版本

## 🎮 拳皇97支持

### ROM要求
```
文件名: kof97.zip
系统: Neo Geo MVS
核心: fbneo
大小: ~45-50MB
```

### 自动处理
- 文件名检测：包含"kof"和"97"
- 自动标准化：任何拳皇97文件名 → "kof97"
- 核心选择：自动选择FBNeo
- 系统识别：自动识别为arcade

## 🚀 测试步骤

### 1. 清除缓存
- 清除浏览器缓存
- 刷新页面

### 2. 上传游戏
- 使用标准的kof97.zip文件
- 确保文件名包含"kof"和"97"

### 3. 检查网络
- 打开开发者工具 (F12)
- 查看Network标签
- 应该看到下载 `fbneo-wasm.data`

### 4. 验证配置
- 查看Console输出
- 确认WebGL2启用状态
- 检查核心选择

## ⚠️ 故障排除

### 如果仍然下载legacy版本
1. **清除浏览器缓存**
2. **检查WebGL2支持**：
   ```javascript
   // 在控制台运行
   console.log(!!document.createElement("canvas").getContext("webgl2"));
   ```
3. **强制刷新页面** (Ctrl+F5)

### 如果游戏无法启动
1. **检查ROM文件**：确保是正确的Neo Geo MVS版本
2. **查看控制台错误**：检查具体错误信息
3. **尝试不同ROM**：使用不同版本的拳皇97

### 浏览器兼容性
- **Chrome/Edge**：完全支持WebGL2
- **Firefox**：基本支持
- **Safari**：可能需要手动启用WebGL2

## 📊 配置对比

| 配置项 | 之前 | 现在 |
|--------|------|------|
| 主要核心 | fbalpha2012_cps2 | fbneo |
| 核心文件 | *-legacy-wasm.data | fbneo-wasm.data |
| WebGL2 | 自动检测 | 强制启用 |
| 系统映射 | 多核心 | 单一FBNeo |

## 🎉 预期效果

现在重新测试应该看到：

1. **网络请求**：下载 `fbneo-wasm.data`
2. **控制台输出**：显示"强制启用WebGL2: true"
3. **核心选择**：自动选择FBNeo
4. **游戏启动**：拳皇97正常运行

所有修改已通过HMR热更新应用，请重新测试！🎮
