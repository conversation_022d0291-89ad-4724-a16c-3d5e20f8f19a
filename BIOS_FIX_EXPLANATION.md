# BIOS 应用修复说明

## 🐛 问题描述

用户反馈 KOF97 游戏启动时，虽然系统成功下载了 Neo Geo BIOS (`neogeo.zip`)，但 FBNeo 模拟器仍然报告找不到 BIOS 文件。

## 🔍 问题分析

### 原因
FBNeo 街机模拟器对 BIOS 文件有特殊要求：
1. **不能解压 BIOS 文件**：FBNeo 需要接收原始的 ZIP 格式 BIOS 文件
2. **需要设置特殊标志**：必须设置 `EJS_dontExtractBIOS = true`
3. **时机问题**：BIOS 配置必须在模拟器启动前正确设置

### 错误表现
- 控制台显示："This game is known but one of your romsets is missing files for THIS VERSION of FBNeo"
- 提示："None of those archives was found in your paths"
- 游戏无法启动

## ✅ 修复方案

### 1. 核心检测逻辑
```typescript
// 检测是否为 FBNeo 核心
if (
  props.core?.id === "fbneo" ||
  props.core?.id?.includes("fbalpha") ||
  systemType === "arcade"
) {
  (window as any).EJS_dontExtractBIOS = true;
  console.log("🎮 FBNeo/街机模式：设置不解压 BIOS 文件");
}
```

### 2. BIOS 加载优化
在所有 BIOS 加载点都应用 FBNeo 特殊处理：

#### A. 已存在 BIOS 加载
```typescript
if (props.core?.id === "fbneo" || props.core?.id?.includes("fbalpha")) {
  (window as any).EJS_dontExtractBIOS = true;
  (window as any).EJS_biosUrl = biosUrl;
  console.log(`✅ FBNeo BIOS 文件 ${bios.filename} 已加载 (不解压模式)`);
}
```

#### B. 自动下载后加载
```typescript
if (props.core?.id === "fbneo" || props.core?.id?.includes("fbalpha")) {
  (window as any).EJS_dontExtractBIOS = true;
  (window as any).EJS_biosUrl = biosUrl;
  console.log(`✅ FBNeo BIOS 文件 ${bios.filename} 下载并加载成功 (不解压模式)`);
}
```

#### C. 传统检测方式加载
```typescript
if (props.core?.id === "fbneo" || props.core?.id?.includes("fbalpha")) {
  (window as any).EJS_dontExtractBIOS = true;
  (window as any).EJS_biosUrl = biosUrl;
  console.log(`✅ FBNeo BIOS 文件 ${biosFileName} 已加载 (不解压模式)`);
}
```

### 3. 调试信息增强
添加详细的调试日志：
```typescript
console.log(`🔧 EJS_dontExtractBIOS = ${(window as any).EJS_dontExtractBIOS}`);
console.log(`🔧 EJS_biosUrl = ${(window as any).EJS_biosUrl}`);
```

## 🔧 技术细节

### EmulatorJS 配置
- `EJS_dontExtractBIOS = true`：告诉 EmulatorJS 不要解压 BIOS ZIP 文件
- `EJS_biosUrl`：指向 BIOS 文件的 Blob URL
- 必须在模拟器初始化前设置

### FBNeo 核心要求
- 需要完整的 ZIP 格式 BIOS 文件
- BIOS 文件内部结构必须保持原始状态
- 支持 Neo Geo、PGM、CPS-1、CPS-2 等街机系统

### 兼容性处理
- 只对 FBNeo 和 FBAlpha 核心应用特殊处理
- 其他核心保持原有的 BIOS 加载方式
- 向后兼容现有功能

## 🎯 修复效果

### 预期结果
1. **KOF97 正常启动**：Neo Geo BIOS 正确应用
2. **其他街机游戏支持**：PGM、CPS 等系统正常工作
3. **自动下载功能**：BIOS 下载后立即可用
4. **用户体验提升**：无需手动干预

### 验证方法
1. 上传 KOF97 游戏文件
2. 观察控制台日志：
   - 看到 "🎮 FBNeo/街机模式：设置不解压 BIOS 文件"
   - 看到 "✅ FBNeo BIOS 文件 neogeo.zip 已加载 (不解压模式)"
   - 看到 "🔧 EJS_dontExtractBIOS = true"
3. 游戏成功启动，不再显示 BIOS 错误

## 📝 相关文件

### 修改的文件
- `src/components/RetroArchEmulator.vue`：主要修复文件

### 修复的函数
- `loadBiosForSystem()`：BIOS 加载主函数
- 智能推荐 BIOS 加载逻辑
- 自动下载 BIOS 加载逻辑  
- 传统检测 BIOS 加载逻辑

### 保持不变
- BIOS 下载功能
- BIOS 管理界面
- 其他模拟器核心的 BIOS 处理

## 🚀 后续优化

### 可能的改进
1. **更多街机系统支持**：添加更多街机 BIOS 配置
2. **BIOS 验证**：检查 BIOS 文件完整性
3. **错误处理**：更友好的错误提示
4. **性能优化**：BIOS 缓存机制

### 测试建议
1. 测试不同的街机游戏
2. 测试 BIOS 自动下载功能
3. 测试非街机游戏的兼容性
4. 测试浏览器兼容性
