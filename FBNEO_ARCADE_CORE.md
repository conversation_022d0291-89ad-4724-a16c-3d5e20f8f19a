# FBNeo Legacy 街机核心添加说明

## 🎮 新增核心

已成功添加 **FBNeo Legacy** 街机模拟器核心，专门用于运行拳皇97等经典SNK Neo Geo街机游戏。

### 核心信息
- **核心ID**: `fbneo-legacy`
- **核心名称**: Arcade (FBNeo Legacy)
- **支持格式**: `.zip`, `.neo`, `.mvs`
- **CDN地址**: `https://cdn.emulatorjs.org/latest/data/cores/fbneo-legacy-wasm.data`
- **描述**: SNK Neo Geo街机模拟器 - 支持拳皇97等经典街机游戏

## 🔧 技术实现

### 1. 核心定义 (src/utils/cores.ts)
```typescript
// 主要FBNeo核心
{
  id: 'fbneo',
  name: 'Arcade (FBNeo)',
  extensions: ['zip', 'neo', 'mvs'],
  description: 'FinalBurn Neo街机模拟器 - 支持拳皇97等经典街机游戏',
  cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fbneo-wasm.data'
}

// 备用CPS2核心
{
  id: 'fbalpha2012_cps2',
  name: 'Arcade (FBA CPS2)',
  extensions: ['zip'],
  description: 'FinalBurn Alpha CPS2街机模拟器 - 专门支持CPS2游戏',
  cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fbalpha2012_cps2-wasm.data'
}
```

### 2. 系统映射
```typescript
const systemCoreMap: { [key: string]: string[] } = {
  // ... 其他系统
  arcade: ['fbneo', 'fbalpha2012_cps2'],
  neogeo: ['fbneo']
}
```

### 3. EmulatorJS集成 (src/components/RetroArchEmulator.vue)
```typescript
// 强制使用fbneo核心
if (systemType === "arcade") {
  (window as any).EJS_core = "fbneo";
} else {
  (window as any).EJS_core = coreType;
}

const coreMap: { [key: string]: string } = {
  // ... 其他核心
  fbneo: "fbneo",
  fbalpha2012_cps2: "fbalpha2012_cps2",
};

const systemMap: { [key: string]: string } = {
  // ... 其他系统
  fbneo: "arcade",
  fbalpha2012_cps2: "arcade",
};
```

### 4. 智能文件检测 (src/utils/fileHandler.ts)
```typescript
// 检测街机游戏ZIP文件
const isArcadeGame = fileName.includes('kof') || fileName.includes('拳皇') || 
                   fileName.includes('king') || fileName.includes('fighter') ||
                   fileName.includes('neogeo') || fileName.includes('snk') ||
                   fileName.includes('mvs') || fileName.includes('arcade') ||
                   fileName.includes('97') || fileName.includes('98') ||
                   fileName.includes('2002') || fileName.includes('街机')

// 街机游戏直接使用ZIP文件
if (isArcadeGame) {
  return [{
    name: file.name,
    extension: 'zip',
    data: arrayBuffer,
    size: arrayBuffer.byteLength,
    system: 'arcade'
  }]
}
```

## 🎯 支持的游戏

### 拳皇系列
- 拳皇97 (The King of Fighters '97)
- 拳皇98 (The King of Fighters '98)
- 拳皇2002 (The King of Fighters 2002)

### 其他SNK游戏
- 饿狼传说系列
- 龙虎之拳系列
- 侍魂系列
- 合金弹头系列

## 📁 文件格式要求

### ZIP文件命名
为了正确识别为街机游戏，ZIP文件名应包含以下关键词之一：
- `kof` (拳皇英文缩写)
- `拳皇` (中文)
- `king`, `fighter`
- `neogeo`, `snk`
- `mvs`, `arcade`
- `97`, `98`, `2002` (年份)
- `街机` (中文)

### 示例文件名
- `kof97.zip` ✅
- `拳皇97.zip` ✅
- `The King of Fighters 97.zip` ✅
- `neogeo-kof97.zip` ✅
- `街机游戏-拳皇97.zip` ✅

## 🚀 使用方法

### 1. 上传游戏
1. 准备拳皇97的ZIP ROM文件
2. 确保文件名包含识别关键词
3. 在游戏库页面上传ZIP文件

### 2. 游戏启动
1. 系统会自动识别为街机游戏
2. 自动选择FBNeo Legacy核心
3. 点击开始游戏即可

### 3. 核心选择
- 系统会自动推荐FBNeo Legacy核心
- 也可以手动选择其他街机核心（如果有）
- 支持核心切换和配置

## ⚠️ 注意事项

### ROM要求
- 需要正确的街机ROM文件
- ZIP文件应包含完整的游戏数据
- 确保ROM版本与FBNeo兼容

### 性能考虑
- 街机游戏对性能要求较高
- 建议在现代浏览器中运行
- 移动设备可能存在性能限制

### 法律声明
- 请确保拥有游戏的合法使用权
- 仅用于个人学习和研究目的
- 遵守当地法律法规

## 🔍 故障排除

### 游戏无法启动
1. 检查文件名是否包含识别关键词
2. 确认ZIP文件完整性
3. 尝试重新上传文件

### 核心加载失败
1. 检查网络连接
2. 清除浏览器缓存
3. 刷新页面重试

### 性能问题
1. 关闭其他浏览器标签页
2. 降低游戏画质设置
3. 使用桌面浏览器而非移动端

## 📈 后续扩展

### 计划支持
- 更多街机核心（MAME等）
- CPS1/CPS2游戏支持
- 街机游戏数据库集成
- 游戏截图和信息展示

### 优化方向
- 自动ROM验证
- 游戏兼容性检测
- 性能优化设置
- 移动端适配改进
