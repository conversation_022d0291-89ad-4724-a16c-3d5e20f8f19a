# ZIP文件上传功能说明

## 功能概述
现在游戏文件上传组件已经修改为只接受ZIP文件格式，并会自动解压ZIP文件来查找支持的游戏文件。

## 支持的游戏文件格式
ZIP文件中可以包含以下格式的游戏文件：
- `.gb` - Game Boy
- `.gbc` - Game Boy Color  
- `.gba` - Game Boy Advance
- `.nes` - Nintendo Entertainment System
- `.smc`, `.sfc`, `.swc` - Super Nintendo
- `.md`, `.gen`, `.bin` - Sega Genesis/Mega Drive
- `.sms` - Sega Master System
- `.a26` - Atari 2600
- `.a52` - Atari 5200
- `.nds` - Nintendo DS
- `.n64`, `.v64`, `.z64` - Nintendo 64
- `.cue`, `.img`, `.mdf`, `.pbp`, `.toc`, `.cbn`, `.m3u` - PlayStation

## 友好的提示系统
现在系统使用友好的Toast提示替代了原来的alert弹窗：

### 错误提示（红色）
如果ZIP文件中不包含任何支持的游戏文件格式，系统会：
1. 显示红色的错误Toast提示
2. 列出ZIP文件中发现的所有文件
3. 显示支持的文件格式列表
4. 停止后续处理，不会继续加载游戏
5. 错误提示会显示10秒钟，用户也可以手动关闭

### 成功提示（绿色）
当成功加载游戏文件时，会显示绿色的成功提示，告知用户加载了多少个游戏文件。

### 警告提示（黄色）
当没有找到支持的游戏文件时，会显示黄色的警告提示。

## 测试方法
1. 创建一个包含不支持格式文件的ZIP文件（如.txt, .jpg, .pdf等）
2. 尝试上传该ZIP文件
3. 应该会看到详细的错误提示，说明文件格式不支持

## 最近游戏功能
现在游戏库添加了"最近游戏"功能：

### 功能特点
- **自动保存**：所有上传和选择的游戏都会自动保存到IndexedDB
- **最近记录**：显示最近6个游戏，按最后游玩时间排序
- **快速启动**：点击游戏卡片即可快速启动游戏
- **游戏管理**：可以删除单个游戏或清空所有游戏
- **时间显示**：显示相对时间（如"5分钟前"、"2小时前"等）

### 游戏卡片信息
每个游戏卡片显示：
- 游戏名称（过长会显示省略号）
- 文件格式和大小
- 最后游玩时间
- 悬停时显示播放按钮和删除按钮

### 数据持久化
- 游戏数据存储在浏览器的IndexedDB中
- 即使关闭浏览器，游戏记录也会保留
- 支持大文件存储，不受localStorage限制

## 正常使用
1. 将游戏ROM文件压缩成ZIP格式
2. 上传ZIP文件
3. 系统会自动解压并识别其中的游戏文件
4. 支持的游戏文件会被加载到游戏列表中
5. 游戏会自动出现在"最近游戏"列表中
