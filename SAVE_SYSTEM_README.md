# 🎮 游戏进度保存系统

## 功能概述

我们为游戏模拟器添加了完整的进度保存功能，支持自动保存、手动保存、存档管理和导入导出功能。

## 主要功能

### 1. 自动保存 ⚡
- **5秒自动保存**: 游戏运行时每5秒自动保存一次进度
- **会话管理**: 自动跟踪游戏开始和结束时间
- **游戏时长统计**: 记录每个游戏的总游戏时间

### 2. 手动保存 💾
- **手动保存**: 玩家可以随时手动保存游戏进度
- **快速保存**: 一键快速保存功能
- **存档描述**: 支持为存档添加自定义描述
- **截图保存**: 保存时自动截取游戏画面作为存档预览

### 3. 存档管理 📂
- **存档列表**: 查看所有手动保存的存档
- **存档读取**: 选择任意存档进行读取
- **存档导出**: 将存档导出为JSON文件
- **存档导入**: 导入其他设备的存档文件

### 4. 智能游戏启动 🎯
- **继续游戏**: 有存档的游戏显示"继续游戏"按钮
- **自动读档**: 继续游戏时自动加载最新存档
- **存档指示**: 游戏选择界面显示哪些游戏有存档

## 技术实现

### 核心文件

#### `src/utils/saveStateManager.ts`
- 进度保存管理器的核心实现
- 使用 IndexedDB 存储存档数据
- 支持自动保存、手动保存、导入导出等功能

#### `src/components/RetroArchEmulator.vue`
- 集成了进度保存功能的模拟器组件
- 添加了保存相关的UI控件
- 实现了游戏会话管理

#### `src/components/GameSelector.vue`
- 修改了游戏选择界面
- 显示游戏的存档状态指示器

#### `src/components/SaveTestPage.vue`
- 进度保存功能的测试页面
- 可以测试各种保存功能

### 数据结构

#### SaveState (存档状态)
```typescript
interface SaveState {
  id: string;              // 存档唯一ID
  gameName: string;        // 游戏名称
  gameSystem: string;      // 游戏系统
  saveData: ArrayBuffer;   // 存档数据
  screenshot?: string;     // 截图(base64)
  saveTime: Date;          // 保存时间
  autoSave: boolean;       // 是否为自动保存
  description?: string;    // 存档描述
}
```

#### GameProgress (游戏进度)
```typescript
interface GameProgress {
  gameName: string;        // 游戏名称
  gameSystem: string;      // 游戏系统
  lastPlayTime: Date;      // 最后游戏时间
  totalPlayTime: number;   // 总游戏时间(秒)
  saveStates: SaveState[]; // 手动存档列表
  quickSave?: SaveState;   // 快速保存槽
  autoSave?: SaveState;    // 自动保存槽
}
```

### IndexedDB 数据库结构

#### 数据库: `GameEmulatorSaves`
- **gameProgress**: 存储游戏进度信息
- **saveStates**: 存储具体的存档数据

## 使用方法

### 1. 开始游戏
- 选择游戏后，如果有存档会显示"继续游戏"按钮
- 点击"继续游戏"会自动加载最新存档
- 点击"开始游戏"会从头开始

### 2. 游戏中保存
- **自动保存**: 游戏运行时每5秒自动保存
- **手动保存**: 点击"保存"按钮手动保存
- **快速保存**: 点击"快存"按钮快速保存

### 3. 存档管理
- 点击"存档"按钮打开存档管理面板
- 查看所有手动保存的存档
- 可以读取、导出任意存档
- 支持导入外部存档文件

### 4. 导出/导入存档
- **导出**: 在存档管理面板中点击"导出"按钮
- **导入**: 在存档管理面板中选择JSON文件导入

## 测试功能

访问 `/test-save` 页面可以测试进度保存功能：
- 初始化保存管理器
- 创建测试存档
- 测试导出/导入功能
- 查看测试日志

## 兼容性

- ✅ 支持所有模拟器核心
- ✅ 支持所有游戏系统
- ✅ 使用 IndexedDB，支持现代浏览器
- ✅ 存档数据完全本地存储，保护隐私

## 注意事项

1. **存档大小**: 存档文件大小取决于游戏的内存状态
2. **浏览器限制**: IndexedDB 有存储配额限制
3. **兼容性**: 需要支持 IndexedDB 的现代浏览器
4. **数据安全**: 建议定期导出重要存档进行备份

## 未来改进

- [ ] 云端存档同步
- [ ] 存档压缩优化
- [ ] 存档预览图片
- [ ] 存档分类管理
- [ ] 存档搜索功能
- [ ] 存档统计信息

## 开发者说明

如果需要扩展或修改保存功能，主要关注以下文件：
- `saveStateManager.ts`: 核心保存逻辑
- `RetroArchEmulator.vue`: UI集成
- `GameSelector.vue`: 游戏选择界面

所有保存相关的操作都通过 `saveStateManager` 单例进行，确保数据一致性。
