/*
 * @Author: wuqi_y <EMAIL>
 * @Date: 2025-07-29 19:15:04
 * @LastEditors: wuqi_y <EMAIL>
 * @LastEditTime: 2025-07-29 19:39:35
 * @Description: 
 * 
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    open: true,
    fs: {
      allow: ['..'] // Allow serving files from parent directories
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  },
  assetsInclude: ['**/*.wasm', '**/*.data'] // Include WASM and data files as assets
}) 