# 🎮 Game Emulator Web

A modern web-based game emulator built with Vue 3 + TypeScript that automatically detects game files and selects the appropriate emulator core.

## Features

- 🚀 **Automatic Core Detection**: Automatically selects the best emulator core based on file extension
- 🎯 **Manual Core Selection**: Override automatic selection with manual core picker
- 📁 **Drag & Drop Support**: Easy file upload with drag and drop interface
- 🎮 **Multiple System Support**: Supports Game Boy, NES, SNES, Genesis, and many more
- 💾 **Save States**: Save and load game progress
- 🔳 **Fullscreen Mode**: Immersive gaming experience
- 🎨 **Beautiful UI**: Modern gradient design with glassmorphism effects

## Supported Systems

- Nintendo Game Boy / Game Boy Color (.gb, .gbc)
- Nintendo Game Boy Advance (.gba)
- Nintendo Entertainment System (.nes)
- Super Nintendo Entertainment System (.smc, .sfc, .swc)
- Sega Genesis / Mega Drive (.md, .gen, .bin)
- Sega Master System (.sms)
- Atari 2600 (.a26, .bin)
- Atari 5200 (.a52, .bin)
- Nintendo DS (.nds)
- Nintendo 64 (.n64, .v64, .z64)
- Sony PlayStation (.bin, .cue, .img, .mdf, .pbp, .toc, .cbn, .m3u)

## Getting Started

### Prerequisites

- Node.js 16+ 
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

### Usage

1. **Upload a game file**: Drag and drop a game ROM file onto the upload area or click to browse
2. **Select emulator core**: The app will automatically recommend the best core, or you can choose manually
3. **Start playing**: Click "Start Game" to begin emulating
4. **Game controls**: Use the built-in controls for save states, fullscreen, and more

## Technical Details

- **Frontend**: Vue 3 with Composition API + TypeScript
- **Emulation**: EmulatorJS with libretro cores
- **Build Tool**: Vite
- **Styling**: CSS with glassmorphism effects

## Core Integration

The emulator cores are automatically downloaded from the EmulatorJS CDN:
- Base URL: `https://cdn.emulatorjs.org/latest/data/cores/`
- Cores are loaded dynamically based on the selected system
- No need to manually download or host core files

## Browser Compatibility

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.

## Acknowledgments

- [EmulatorJS](https://emulatorjs.org/) for the emulation engine
- [libretro](https://www.libretro.com/) for the emulator cores
- Vue.js team for the amazing framework 