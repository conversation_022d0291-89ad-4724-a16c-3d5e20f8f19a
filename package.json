{"name": "game-emulator-web", "version": "1.0.0", "description": "Web-based game emulator with automatic core selection", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "generate-games": "node scripts/generate-games-config.js"}, "dependencies": {"@emulatorjs/core-fbneo": "^4.2.3", "@emulatorjs/core-gambatte": "^4.2.3", "@emulatorjs/cores": "^4.2.3", "@emulatorjs/emulatorjs": "^4.2.3", "jszip": "^3.10.1", "vue": "^3.3.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.39", "tailwindcss": "^3.4.6", "typescript": "^5.2.0", "vite": "^4.4.0", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^3.0.5"}}