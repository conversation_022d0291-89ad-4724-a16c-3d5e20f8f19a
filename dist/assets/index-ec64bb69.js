var Ia=Object.defineProperty;var Ba=(e,t,n)=>t in e?Ia(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var vt=(e,t,n)=>(Ba(e,typeof t!="symbol"?t+"":t,n),n);function Ta(e,t){for(var n=0;n<t.length;n++){const s=t[n];if(typeof s!="string"&&!Array.isArray(s)){for(const r in s)if(r!=="default"&&!(r in e)){const o=Object.getOwnPropertyDescriptor(s,r);o&&Object.defineProperty(e,r,o.get?o:{enumerable:!0,get:()=>s[r]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function fr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Oe={},en=[],ot=()=>{},Pa=()=>!1,ds=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),dr=e=>e.startsWith("onUpdate:"),Me=Object.assign,hr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ra=Object.prototype.hasOwnProperty,Ce=(e,t)=>Ra.call(e,t),he=Array.isArray,tn=e=>hs(e)==="[object Map]",jo=e=>hs(e)==="[object Set]",pe=e=>typeof e=="function",Pe=e=>typeof e=="string",Ft=e=>typeof e=="symbol",Be=e=>e!==null&&typeof e=="object",Uo=e=>(Be(e)||pe(e))&&pe(e.then)&&pe(e.catch),Go=Object.prototype.toString,hs=e=>Go.call(e),$a=e=>hs(e).slice(8,-1),Jo=e=>hs(e)==="[object Object]",pr=e=>Pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,_n=fr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ps=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},za=/-(\w)/g,rt=ps(e=>e.replace(za,(t,n)=>n?n.toUpperCase():"")),Da=/\B([A-Z])/g,Nt=ps(e=>e.replace(Da,"-$1").toLowerCase()),ms=ps(e=>e.charAt(0).toUpperCase()+e.slice(1)),Bs=ps(e=>e?`on${ms(e)}`:""),zt=(e,t)=>!Object.is(e,t),Yn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ws=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Vs=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ma=e=>{const t=Pe(e)?Number(e):NaN;return isNaN(t)?e:t};let Dr;const gs=()=>Dr||(Dr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function vs(e){if(he(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=Pe(s)?ja(s):vs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(Pe(e)||Be(e))return e}const La=/;(?![^(]*\))/g,Fa=/:([^]+)/,Na=/\/\*[^]*?\*\//g;function ja(e){const t={};return e.replace(Na,"").split(La).forEach(n=>{if(n){const s=n.split(Fa);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Le(e){let t="";if(Pe(e))t=e;else if(he(e))for(let n=0;n<e.length;n++){const s=Le(e[n]);s&&(t+=s+" ")}else if(Be(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ua="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ga=fr(Ua);function Ho(e){return!!e||e===""}const Wo=e=>!!(e&&e.__v_isRef===!0),de=e=>Pe(e)?e:e==null?"":he(e)||Be(e)&&(e.toString===Go||!pe(e.toString))?Wo(e)?de(e.value):JSON.stringify(e,Vo,2):String(e),Vo=(e,t)=>Wo(t)?Vo(e,t.value):tn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Ts(s,o)+" =>"]=r,n),{})}:jo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ts(n))}:Ft(t)?Ts(t):Be(t)&&!he(t)&&!Jo(t)?String(t):t,Ts=(e,t="")=>{var n;return Ft(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ye;class Ja{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ye,!t&&Ye&&(this.index=(Ye.scopes||(Ye.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ye;try{return Ye=this,t()}finally{Ye=n}}}on(){++this._on===1&&(this.prevScope=Ye,Ye=this)}off(){this._on>0&&--this._on===0&&(Ye=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Ha(){return Ye}let Ie;const Ps=new WeakSet;class qo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ye&&Ye.active&&Ye.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Ps.has(this)&&(Ps.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Zo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Mr(this),Yo(this);const t=Ie,n=it;Ie=this,it=!0;try{return this.fn()}finally{Xo(this),Ie=t,it=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)vr(t);this.deps=this.depsTail=void 0,Mr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Ps.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){qs(this)&&this.run()}get dirty(){return qs(this)}}let Ko=0,yn,bn;function Zo(e,t=!1){if(e.flags|=8,t){e.next=bn,bn=e;return}e.next=yn,yn=e}function mr(){Ko++}function gr(){if(--Ko>0)return;if(bn){let t=bn;for(bn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;yn;){let t=yn;for(yn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Yo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),vr(s),Wa(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function qs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Qo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Qo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===On)||(e.globalVersion=On,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!qs(e))))return;e.flags|=2;const t=e.dep,n=Ie,s=it;Ie=e,it=!0;try{Yo(e);const r=e.fn(e._value);(t.version===0||zt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{Ie=n,it=s,Xo(e),e.flags&=-3}}function vr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)vr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Wa(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let it=!0;const ei=[];function Ct(){ei.push(it),it=!1}function At(){const e=ei.pop();it=e===void 0?!0:e}function Mr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Ie;Ie=void 0;try{t()}finally{Ie=n}}}let On=0;class Va{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _r{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Ie||!it||Ie===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Ie)n=this.activeLink=new Va(Ie,this),Ie.deps?(n.prevDep=Ie.depsTail,Ie.depsTail.nextDep=n,Ie.depsTail=n):Ie.deps=Ie.depsTail=n,ti(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=Ie.depsTail,n.nextDep=void 0,Ie.depsTail.nextDep=n,Ie.depsTail=n,Ie.deps===n&&(Ie.deps=s)}return n}trigger(t){this.version++,On++,this.notify(t)}notify(t){mr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{gr()}}}function ti(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)ti(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ks=new WeakMap,qt=Symbol(""),Zs=Symbol(""),In=Symbol("");function Ue(e,t,n){if(it&&Ie){let s=Ks.get(e);s||Ks.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new _r),r.map=s,r.key=n),r.track()}}function St(e,t,n,s,r,o){const i=Ks.get(e);if(!i){On++;return}const a=c=>{c&&c.trigger()};if(mr(),t==="clear")i.forEach(a);else{const c=he(e),d=c&&pr(n);if(c&&n==="length"){const u=Number(s);i.forEach((f,g)=>{(g==="length"||g===In||!Ft(g)&&g>=u)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),d&&a(i.get(In)),t){case"add":c?d&&a(i.get("length")):(a(i.get(qt)),tn(e)&&a(i.get(Zs)));break;case"delete":c||(a(i.get(qt)),tn(e)&&a(i.get(Zs)));break;case"set":tn(e)&&a(i.get(qt));break}}gr()}function Zt(e){const t=ke(e);return t===e?t:(Ue(t,"iterate",In),st(e)?t:t.map(Ne))}function _s(e){return Ue(e=ke(e),"iterate",In),e}const qa={__proto__:null,[Symbol.iterator](){return Rs(this,Symbol.iterator,Ne)},concat(...e){return Zt(this).concat(...e.map(t=>he(t)?Zt(t):t))},entries(){return Rs(this,"entries",e=>(e[1]=Ne(e[1]),e))},every(e,t){return _t(this,"every",e,t,void 0,arguments)},filter(e,t){return _t(this,"filter",e,t,n=>n.map(Ne),arguments)},find(e,t){return _t(this,"find",e,t,Ne,arguments)},findIndex(e,t){return _t(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return _t(this,"findLast",e,t,Ne,arguments)},findLastIndex(e,t){return _t(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return _t(this,"forEach",e,t,void 0,arguments)},includes(...e){return $s(this,"includes",e)},indexOf(...e){return $s(this,"indexOf",e)},join(e){return Zt(this).join(e)},lastIndexOf(...e){return $s(this,"lastIndexOf",e)},map(e,t){return _t(this,"map",e,t,void 0,arguments)},pop(){return dn(this,"pop")},push(...e){return dn(this,"push",e)},reduce(e,...t){return Lr(this,"reduce",e,t)},reduceRight(e,...t){return Lr(this,"reduceRight",e,t)},shift(){return dn(this,"shift")},some(e,t){return _t(this,"some",e,t,void 0,arguments)},splice(...e){return dn(this,"splice",e)},toReversed(){return Zt(this).toReversed()},toSorted(e){return Zt(this).toSorted(e)},toSpliced(...e){return Zt(this).toSpliced(...e)},unshift(...e){return dn(this,"unshift",e)},values(){return Rs(this,"values",Ne)}};function Rs(e,t,n){const s=_s(e),r=s[t]();return s!==e&&!st(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Ka=Array.prototype;function _t(e,t,n,s,r,o){const i=_s(e),a=i!==e&&!st(e),c=i[t];if(c!==Ka[t]){const f=c.apply(e,o);return a?Ne(f):f}let d=n;i!==e&&(a?d=function(f,g){return n.call(this,Ne(f),g,e)}:n.length>2&&(d=function(f,g){return n.call(this,f,g,e)}));const u=c.call(i,d,s);return a&&r?r(u):u}function Lr(e,t,n,s){const r=_s(e);let o=n;return r!==e&&(st(e)?n.length>3&&(o=function(i,a,c){return n.call(this,i,a,c,e)}):o=function(i,a,c){return n.call(this,i,Ne(a),c,e)}),r[t](o,...s)}function $s(e,t,n){const s=ke(e);Ue(s,"iterate",In);const r=s[t](...n);return(r===-1||r===!1)&&wr(n[0])?(n[0]=ke(n[0]),s[t](...n)):r}function dn(e,t,n=[]){Ct(),mr();const s=ke(e)[t].apply(e,n);return gr(),At(),s}const Za=fr("__proto__,__v_isRef,__isVue"),ni=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ft));function Ya(e){Ft(e)||(e=String(e));const t=ke(this);return Ue(t,"has",e),t.hasOwnProperty(e)}class si{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?al:ai:o?ii:oi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=he(t);if(!r){let c;if(i&&(c=qa[n]))return c;if(n==="hasOwnProperty")return Ya}const a=Reflect.get(t,n,He(t)?t:s);return(Ft(n)?ni.has(n):Za(n))||(r||Ue(t,"get",n),o)?a:He(a)?i&&pr(n)?a:a.value:Be(a)?r?ci(a):ys(a):a}}class ri extends si{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=Mt(o);if(!st(s)&&!Mt(s)&&(o=ke(o),s=ke(s)),!he(t)&&He(o)&&!He(s))return c?!1:(o.value=s,!0)}const i=he(t)&&pr(n)?Number(n)<t.length:Ce(t,n),a=Reflect.set(t,n,s,He(t)?t:r);return t===ke(r)&&(i?zt(s,o)&&St(t,"set",n,s):St(t,"add",n,s)),a}deleteProperty(t,n){const s=Ce(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&St(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Ft(n)||!ni.has(n))&&Ue(t,"has",n),s}ownKeys(t){return Ue(t,"iterate",he(t)?"length":qt),Reflect.ownKeys(t)}}class Xa extends si{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Qa=new ri,el=new Xa,tl=new ri(!0);const Ys=e=>e,Jn=e=>Reflect.getPrototypeOf(e);function nl(e,t,n){return function(...s){const r=this.__v_raw,o=ke(r),i=tn(o),a=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,d=r[e](...s),u=n?Ys:t?ss:Ne;return!t&&Ue(o,"iterate",c?Zs:qt),{next(){const{value:f,done:g}=d.next();return g?{value:f,done:g}:{value:a?[u(f[0]),u(f[1])]:u(f),done:g}},[Symbol.iterator](){return this}}}}function Hn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function sl(e,t){const n={get(r){const o=this.__v_raw,i=ke(o),a=ke(r);e||(zt(r,a)&&Ue(i,"get",r),Ue(i,"get",a));const{has:c}=Jn(i),d=t?Ys:e?ss:Ne;if(c.call(i,r))return d(o.get(r));if(c.call(i,a))return d(o.get(a));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Ue(ke(r),"iterate",qt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ke(o),a=ke(r);return e||(zt(r,a)&&Ue(i,"has",r),Ue(i,"has",a)),r===a?o.has(r):o.has(r)||o.has(a)},forEach(r,o){const i=this,a=i.__v_raw,c=ke(a),d=t?Ys:e?ss:Ne;return!e&&Ue(c,"iterate",qt),a.forEach((u,f)=>r.call(o,d(u),d(f),i))}};return Me(n,e?{add:Hn("add"),set:Hn("set"),delete:Hn("delete"),clear:Hn("clear")}:{add(r){!t&&!st(r)&&!Mt(r)&&(r=ke(r));const o=ke(this);return Jn(o).has.call(o,r)||(o.add(r),St(o,"add",r,r)),this},set(r,o){!t&&!st(o)&&!Mt(o)&&(o=ke(o));const i=ke(this),{has:a,get:c}=Jn(i);let d=a.call(i,r);d||(r=ke(r),d=a.call(i,r));const u=c.call(i,r);return i.set(r,o),d?zt(o,u)&&St(i,"set",r,o):St(i,"add",r,o),this},delete(r){const o=ke(this),{has:i,get:a}=Jn(o);let c=i.call(o,r);c||(r=ke(r),c=i.call(o,r)),a&&a.call(o,r);const d=o.delete(r);return c&&St(o,"delete",r,void 0),d},clear(){const r=ke(this),o=r.size!==0,i=r.clear();return o&&St(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=nl(r,e,t)}),n}function yr(e,t){const n=sl(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Ce(n,r)&&r in s?n:s,r,o)}const rl={get:yr(!1,!1)},ol={get:yr(!1,!0)},il={get:yr(!0,!1)};const oi=new WeakMap,ii=new WeakMap,ai=new WeakMap,al=new WeakMap;function ll(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function cl(e){return e.__v_skip||!Object.isExtensible(e)?0:ll($a(e))}function ys(e){return Mt(e)?e:br(e,!1,Qa,rl,oi)}function li(e){return br(e,!1,tl,ol,ii)}function ci(e){return br(e,!0,el,il,ai)}function br(e,t,n,s,r){if(!Be(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=cl(e);if(o===0)return e;const i=r.get(e);if(i)return i;const a=new Proxy(e,o===2?s:n);return r.set(e,a),a}function nn(e){return Mt(e)?nn(e.__v_raw):!!(e&&e.__v_isReactive)}function Mt(e){return!!(e&&e.__v_isReadonly)}function st(e){return!!(e&&e.__v_isShallow)}function wr(e){return e?!!e.__v_raw:!1}function ke(e){const t=e&&e.__v_raw;return t?ke(t):e}function ul(e){return!Ce(e,"__v_skip")&&Object.isExtensible(e)&&Ws(e,"__v_skip",!0),e}const Ne=e=>Be(e)?ys(e):e,ss=e=>Be(e)?ci(e):e;function He(e){return e?e.__v_isRef===!0:!1}function be(e){return ui(e,!1)}function fl(e){return ui(e,!0)}function ui(e,t){return He(e)?e:new dl(e,t)}class dl{constructor(t,n){this.dep=new _r,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ke(t),this._value=n?t:Ne(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||st(t)||Mt(t);t=s?t:ke(t),zt(t,n)&&(this._rawValue=t,this._value=s?t:Ne(t),this.dep.trigger())}}function Dt(e){return He(e)?e.value:e}const hl={get:(e,t,n)=>t==="__v_raw"?e:Dt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return He(r)&&!He(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function fi(e){return nn(e)?e:new Proxy(e,hl)}class pl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new _r(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=On-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Ie!==this)return Zo(this,!0),!0}get value(){const t=this.dep.track();return Qo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function ml(e,t,n=!1){let s,r;return pe(e)?s=e:(s=e.get,r=e.set),new pl(s,r,n)}const Wn={},rs=new WeakMap;let Ht;function gl(e,t=!1,n=Ht){if(n){let s=rs.get(n);s||rs.set(n,s=[]),s.push(e)}}function vl(e,t,n=Oe){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:a,call:c}=n,d=B=>r?B:st(B)||r===!1||r===0?kt(B,1):kt(B);let u,f,g,h,_=!1,m=!1;if(He(e)?(f=()=>e.value,_=st(e)):nn(e)?(f=()=>d(e),_=!0):he(e)?(m=!0,_=e.some(B=>nn(B)||st(B)),f=()=>e.map(B=>{if(He(B))return B.value;if(nn(B))return d(B);if(pe(B))return c?c(B,2):B()})):pe(e)?t?f=c?()=>c(e,2):e:f=()=>{if(g){Ct();try{g()}finally{At()}}const B=Ht;Ht=u;try{return c?c(e,3,[h]):e(h)}finally{Ht=B}}:f=ot,t&&r){const B=f,P=r===!0?1/0:r;f=()=>kt(B(),P)}const w=Ha(),b=()=>{u.stop(),w&&w.active&&hr(w.effects,u)};if(o&&t){const B=t;t=(...P)=>{B(...P),b()}}let x=m?new Array(e.length).fill(Wn):Wn;const k=B=>{if(!(!(u.flags&1)||!u.dirty&&!B))if(t){const P=u.run();if(r||_||(m?P.some((G,N)=>zt(G,x[N])):zt(P,x))){g&&g();const G=Ht;Ht=u;try{const N=[P,x===Wn?void 0:m&&x[0]===Wn?[]:x,h];x=P,c?c(t,3,N):t(...N)}finally{Ht=G}}}else u.run()};return a&&a(k),u=new qo(f),u.scheduler=i?()=>i(k,!1):k,h=B=>gl(B,!1,u),g=u.onStop=()=>{const B=rs.get(u);if(B){if(c)c(B,4);else for(const P of B)P();rs.delete(u)}},t?s?k(!0):x=u.run():i?i(k.bind(null,!0),!0):u.run(),b.pause=u.pause.bind(u),b.resume=u.resume.bind(u),b.stop=b,b}function kt(e,t=1/0,n){if(t<=0||!Be(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,He(e))kt(e.value,t,n);else if(he(e))for(let s=0;s<e.length;s++)kt(e[s],t,n);else if(jo(e)||tn(e))e.forEach(s=>{kt(s,t,n)});else if(Jo(e)){for(const s in e)kt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&kt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Mn(e,t,n,s){try{return s?e(...s):e()}catch(r){bs(r,t,n)}}function lt(e,t,n,s){if(pe(e)){const r=Mn(e,t,n,s);return r&&Uo(r)&&r.catch(o=>{bs(o,t,n)}),r}if(he(e)){const r=[];for(let o=0;o<e.length;o++)r.push(lt(e[o],t,n,s));return r}}function bs(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Oe;if(t){let a=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,c,d)===!1)return}a=a.parent}if(o){Ct(),Mn(o,null,10,[e,c,d]),At();return}}_l(e,n,r,s,i)}function _l(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Ke=[];let ht=-1;const sn=[];let Tt=null,Yt=0;const di=Promise.resolve();let os=null;function xr(e){const t=os||di;return e?t.then(this?e.bind(this):e):t}function yl(e){let t=ht+1,n=Ke.length;for(;t<n;){const s=t+n>>>1,r=Ke[s],o=Bn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Sr(e){if(!(e.flags&1)){const t=Bn(e),n=Ke[Ke.length-1];!n||!(e.flags&2)&&t>=Bn(n)?Ke.push(e):Ke.splice(yl(t),0,e),e.flags|=1,hi()}}function hi(){os||(os=di.then(mi))}function bl(e){he(e)?sn.push(...e):Tt&&e.id===-1?Tt.splice(Yt+1,0,e):e.flags&1||(sn.push(e),e.flags|=1),hi()}function Fr(e,t,n=ht+1){for(;n<Ke.length;n++){const s=Ke[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ke.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function pi(e){if(sn.length){const t=[...new Set(sn)].sort((n,s)=>Bn(n)-Bn(s));if(sn.length=0,Tt){Tt.push(...t);return}for(Tt=t,Yt=0;Yt<Tt.length;Yt++){const n=Tt[Yt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Tt=null,Yt=0}}const Bn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function mi(e){const t=ot;try{for(ht=0;ht<Ke.length;ht++){const n=Ke[ht];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Mn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;ht<Ke.length;ht++){const n=Ke[ht];n&&(n.flags&=-2)}ht=-1,Ke.length=0,pi(),os=null,(Ke.length||sn.length)&&mi()}}let Qe=null,gi=null;function is(e){const t=Qe;return Qe=e,gi=e&&e.type.__scopeId||null,t}function qe(e,t=Qe,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Xr(-1);const o=is(t);let i;try{i=e(...r)}finally{is(o),s._d&&Xr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function vi(e,t){if(Qe===null)return e;const n=Es(Qe),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,a,c=Oe]=t[r];o&&(pe(o)&&(o={mounted:o,updated:o}),o.deep&&kt(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:c}))}return e}function jt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const a=r[i];o&&(a.oldValue=o[i].value);let c=a.dir[s];c&&(Ct(),lt(c,n,8,[e.el,a,e,t]),At())}}const _i=Symbol("_vte"),yi=e=>e.__isTeleport,wn=e=>e&&(e.disabled||e.disabled===""),Nr=e=>e&&(e.defer||e.defer===""),jr=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ur=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Xs=(e,t)=>{const n=e&&e.to;return Pe(n)?t?t(n):null:n},bi={name:"Teleport",__isTeleport:!0,process(e,t,n,s,r,o,i,a,c,d){const{mc:u,pc:f,pbc:g,o:{insert:h,querySelector:_,createText:m,createComment:w}}=d,b=wn(t.props);let{shapeFlag:x,children:k,dynamicChildren:B}=t;if(e==null){const P=t.el=m(""),G=t.anchor=m("");h(P,n,s),h(G,n,s);const N=(M,q)=>{x&16&&(r&&r.isCE&&(r.ce._teleportTarget=M),u(k,M,q,r,o,i,a,c))},Z=()=>{const M=t.target=Xs(t.props,_),q=wi(M,t,m,h);M&&(i!=="svg"&&jr(M)?i="svg":i!=="mathml"&&Ur(M)&&(i="mathml"),b||(N(M,q),Xn(t,!1)))};b&&(N(n,G),Xn(t,!0)),Nr(t.props)?(t.el.__isMounted=!1,Ve(()=>{Z(),delete t.el.__isMounted},o)):Z()}else{if(Nr(t.props)&&e.el.__isMounted===!1){Ve(()=>{bi.process(e,t,n,s,r,o,i,a,c,d)},o);return}t.el=e.el,t.targetStart=e.targetStart;const P=t.anchor=e.anchor,G=t.target=e.target,N=t.targetAnchor=e.targetAnchor,Z=wn(e.props),M=Z?n:G,q=Z?P:N;if(i==="svg"||jr(G)?i="svg":(i==="mathml"||Ur(G))&&(i="mathml"),B?(g(e.dynamicChildren,B,M,r,o,i,a),Or(e,t,!0)):c||f(e,t,M,q,r,o,i,a,!1),b)Z?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Vn(t,n,P,d,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const ie=t.target=Xs(t.props,_);ie&&Vn(t,ie,null,d,0)}else Z&&Vn(t,G,N,d,1);Xn(t,b)}},remove(e,t,n,{um:s,o:{remove:r}},o){const{shapeFlag:i,children:a,anchor:c,targetStart:d,targetAnchor:u,target:f,props:g}=e;if(f&&(r(d),r(u)),o&&r(c),i&16){const h=o||!wn(g);for(let _=0;_<a.length;_++){const m=a[_];s(m,t,n,h,!!m.dynamicChildren)}}},move:Vn,hydrate:wl};function Vn(e,t,n,{o:{insert:s},m:r},o=2){o===0&&s(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:c,children:d,props:u}=e,f=o===2;if(f&&s(i,t,n),(!f||wn(u))&&c&16)for(let g=0;g<d.length;g++)r(d[g],t,n,2);f&&s(a,t,n)}function wl(e,t,n,s,r,o,{o:{nextSibling:i,parentNode:a,querySelector:c,insert:d,createText:u}},f){const g=t.target=Xs(t.props,c);if(g){const h=wn(t.props),_=g._lpa||g.firstChild;if(t.shapeFlag&16)if(h)t.anchor=f(i(e),t,a(e),n,s,r,o),t.targetStart=_,t.targetAnchor=_&&i(_);else{t.anchor=i(e);let m=_;for(;m;){if(m&&m.nodeType===8){if(m.data==="teleport start anchor")t.targetStart=m;else if(m.data==="teleport anchor"){t.targetAnchor=m,g._lpa=t.targetAnchor&&i(t.targetAnchor);break}}m=i(m)}t.targetAnchor||wi(g,t,u,d),f(_&&i(_),t,g,n,s,r,o)}Xn(t,h)}return t.anchor&&i(t.anchor)}const xl=bi;function Xn(e,t){const n=e.ctx;if(n&&n.ut){let s,r;for(t?(s=e.el,r=e.anchor):(s=e.targetStart,r=e.targetAnchor);s&&s!==r;)s.nodeType===1&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function wi(e,t,n,s){const r=t.targetStart=n(""),o=t.targetAnchor=n("");return r[_i]=o,e&&(s(r,e),s(o,e)),o}const Pt=Symbol("_leaveCb"),qn=Symbol("_enterCb");function Sl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ut(()=>{e.isMounted=!0}),Ii(()=>{e.isUnmounting=!0}),e}const tt=[Function,Array],xi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:tt,onEnter:tt,onAfterEnter:tt,onEnterCancelled:tt,onBeforeLeave:tt,onLeave:tt,onAfterLeave:tt,onLeaveCancelled:tt,onBeforeAppear:tt,onAppear:tt,onAfterAppear:tt,onAppearCancelled:tt},Si=e=>{const t=e.subTree;return t.component?Si(t.component):t},kl={name:"BaseTransition",props:xi,setup(e,{slots:t}){const n=Ki(),s=Sl();return()=>{const r=t.default&&Ci(t.default(),!0);if(!r||!r.length)return;const o=ki(r),i=ke(e),{mode:a}=i;if(s.isLeaving)return zs(o);const c=Gr(o);if(!c)return zs(o);let d=Qs(c,i,s,n,f=>d=f);c.type!==Ze&&Tn(c,d);let u=n.subTree&&Gr(n.subTree);if(u&&u.type!==Ze&&!Wt(c,u)&&Si(n).type!==Ze){let f=Qs(u,i,s,n);if(Tn(u,f),a==="out-in"&&c.type!==Ze)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,u=void 0},zs(o);a==="in-out"&&c.type!==Ze?f.delayLeave=(g,h,_)=>{const m=Ei(s,u);m[String(u.key)]=u,g[Pt]=()=>{h(),g[Pt]=void 0,delete d.delayedLeave,u=void 0},d.delayedLeave=()=>{_(),delete d.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return o}}};function ki(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Ze){t=n;break}}return t}const El=kl;function Ei(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Qs(e,t,n,s,r){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:c,onEnter:d,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:g,onLeave:h,onAfterLeave:_,onLeaveCancelled:m,onBeforeAppear:w,onAppear:b,onAfterAppear:x,onAppearCancelled:k}=t,B=String(e.key),P=Ei(n,e),G=(M,q)=>{M&&lt(M,s,9,q)},N=(M,q)=>{const ie=q[1];G(M,q),he(M)?M.every(C=>C.length<=1)&&ie():M.length<=1&&ie()},Z={mode:i,persisted:a,beforeEnter(M){let q=c;if(!n.isMounted)if(o)q=w||c;else return;M[Pt]&&M[Pt](!0);const ie=P[B];ie&&Wt(e,ie)&&ie.el[Pt]&&ie.el[Pt](),G(q,[M])},enter(M){let q=d,ie=u,C=f;if(!n.isMounted)if(o)q=b||d,ie=x||u,C=k||f;else return;let j=!1;const p=M[qn]=R=>{j||(j=!0,R?G(C,[M]):G(ie,[M]),Z.delayedLeave&&Z.delayedLeave(),M[qn]=void 0)};q?N(q,[M,p]):p()},leave(M,q){const ie=String(e.key);if(M[qn]&&M[qn](!0),n.isUnmounting)return q();G(g,[M]);let C=!1;const j=M[Pt]=p=>{C||(C=!0,q(),p?G(m,[M]):G(_,[M]),M[Pt]=void 0,P[ie]===e&&delete P[ie])};P[ie]=e,h?N(h,[M,j]):j()},clone(M){const q=Qs(M,t,n,s,r);return r&&r(q),q}};return Z}function zs(e){if(ws(e))return e=Lt(e),e.children=null,e}function Gr(e){if(!ws(e))return yi(e.type)&&e.children?ki(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&pe(n.default))return n.default()}}function Tn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Tn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ci(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===De?(i.patchFlag&128&&r++,s=s.concat(Ci(i.children,t,a))):(t||i.type!==Ze)&&s.push(a!=null?Lt(i,{key:a}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function je(e,t){return pe(e)?(()=>Me({name:e.name},t,{setup:e}))():e}function Ai(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function xn(e,t,n,s,r=!1){if(he(e)){e.forEach((_,m)=>xn(_,t&&(he(t)?t[m]:t),n,s,r));return}if(Sn(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&xn(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Es(s.component):s.el,i=r?null:o,{i:a,r:c}=e,d=t&&t.r,u=a.refs===Oe?a.refs={}:a.refs,f=a.setupState,g=ke(f),h=f===Oe?()=>!1:_=>Ce(g,_);if(d!=null&&d!==c&&(Pe(d)?(u[d]=null,h(d)&&(f[d]=null)):He(d)&&(d.value=null)),pe(c))Mn(c,a,12,[i,u]);else{const _=Pe(c),m=He(c);if(_||m){const w=()=>{if(e.f){const b=_?h(c)?f[c]:u[c]:c.value;r?he(b)&&hr(b,o):he(b)?b.includes(o)||b.push(o):_?(u[c]=[o],h(c)&&(f[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else _?(u[c]=i,h(c)&&(f[c]=i)):m&&(c.value=i,e.k&&(u[e.k]=i))};i?(w.id=-1,Ve(w,n)):w()}}}gs().requestIdleCallback;gs().cancelIdleCallback;const Sn=e=>!!e.type.__asyncLoader,ws=e=>e.type.__isKeepAlive;function Cl(e,t){Oi(e,"a",t)}function Al(e,t){Oi(e,"da",t)}function Oi(e,t,n=Ge){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(xs(t,s,n),n){let r=n.parent;for(;r&&r.parent;)ws(r.parent.vnode)&&Ol(s,t,n,r),r=r.parent}}function Ol(e,t,n,s){const r=xs(t,e,s,!0);as(()=>{hr(s[t],r)},n)}function xs(e,t,n=Ge,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Ct();const a=Ln(n),c=lt(t,n,e,i);return a(),At(),c});return s?r.unshift(o):r.push(o),o}}const Ot=e=>(t,n=Ge)=>{(!Rn||e==="sp")&&xs(e,(...s)=>t(...s),n)},Il=Ot("bm"),ut=Ot("m"),Bl=Ot("bu"),Tl=Ot("u"),Ii=Ot("bum"),as=Ot("um"),Pl=Ot("sp"),Rl=Ot("rtg"),$l=Ot("rtc");function zl(e,t=Ge){xs("ec",e,t)}const kr="components";function un(e,t){return Ti(kr,e,!0,t)||e}const Bi=Symbol.for("v-ndc");function Dl(e){return Pe(e)?Ti(kr,e,!1)||e:e||Bi}function Ti(e,t,n=!0,s=!1){const r=Qe||Ge;if(r){const o=r.type;if(e===kr){const a=xc(o,!1);if(a&&(a===t||a===rt(t)||a===ms(rt(t))))return o}const i=Jr(r[e]||o[e],t)||Jr(r.appContext[e],t);return!i&&s?o:i}}function Jr(e,t){return e&&(e[t]||e[rt(t)]||e[ms(rt(t))])}function mt(e,t,n,s){let r;const o=n&&n[s],i=he(e);if(i||Pe(e)){const a=i&&nn(e);let c=!1,d=!1;a&&(c=!st(e),d=Mt(e),e=_s(e)),r=new Array(e.length);for(let u=0,f=e.length;u<f;u++)r[u]=t(c?d?ss(Ne(e[u])):Ne(e[u]):e[u],u,void 0,o&&o[u])}else if(typeof e=="number"){r=new Array(e);for(let a=0;a<e;a++)r[a]=t(a+1,a,void 0,o&&o[a])}else if(Be(e))if(e[Symbol.iterator])r=Array.from(e,(a,c)=>t(a,c,void 0,o&&o[c]));else{const a=Object.keys(e);r=new Array(a.length);for(let c=0,d=a.length;c<d;c++){const u=a[c];r[c]=t(e[u],u,c,o&&o[c])}}else r=[];return n&&(n[s]=r),r}const er=e=>e?Zi(e)?Es(e):er(e.parent):null,kn=Me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>er(e.parent),$root:e=>er(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Er(e),$forceUpdate:e=>e.f||(e.f=()=>{Sr(e.update)}),$nextTick:e=>e.n||(e.n=xr.bind(e.proxy)),$watch:e=>sc.bind(e)}),Ds=(e,t)=>e!==Oe&&!e.__isScriptSetup&&Ce(e,t),Ml={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:a,appContext:c}=e;let d;if(t[0]!=="$"){const h=i[t];if(h!==void 0)switch(h){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Ds(s,t))return i[t]=1,s[t];if(r!==Oe&&Ce(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&Ce(d,t))return i[t]=3,o[t];if(n!==Oe&&Ce(n,t))return i[t]=4,n[t];tr&&(i[t]=0)}}const u=kn[t];let f,g;if(u)return t==="$attrs"&&Ue(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Oe&&Ce(n,t))return i[t]=4,n[t];if(g=c.config.globalProperties,Ce(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Ds(r,t)?(r[t]=n,!0):s!==Oe&&Ce(s,t)?(s[t]=n,!0):Ce(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let a;return!!n[i]||e!==Oe&&Ce(e,i)||Ds(t,i)||(a=o[0])&&Ce(a,i)||Ce(s,i)||Ce(kn,i)||Ce(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Ce(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Hr(e){return he(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let tr=!0;function Ll(e){const t=Er(e),n=e.proxy,s=e.ctx;tr=!1,t.beforeCreate&&Wr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:a,provide:c,inject:d,created:u,beforeMount:f,mounted:g,beforeUpdate:h,updated:_,activated:m,deactivated:w,beforeDestroy:b,beforeUnmount:x,destroyed:k,unmounted:B,render:P,renderTracked:G,renderTriggered:N,errorCaptured:Z,serverPrefetch:M,expose:q,inheritAttrs:ie,components:C,directives:j,filters:p}=t;if(d&&Fl(d,s,null),i)for(const D in i){const ee=i[D];pe(ee)&&(s[D]=ee.bind(n))}if(r){const D=r.call(n,n);Be(D)&&(e.data=ys(D))}if(tr=!0,o)for(const D in o){const ee=o[D],K=pe(ee)?ee.bind(n,n):pe(ee.get)?ee.get.bind(n,n):ot,oe=!pe(ee)&&pe(ee.set)?ee.set.bind(n):ot,F=Je({get:K,set:oe});Object.defineProperty(s,D,{enumerable:!0,configurable:!0,get:()=>F.value,set:L=>F.value=L})}if(a)for(const D in a)Pi(a[D],s,n,D);if(c){const D=pe(c)?c.call(n):c;Reflect.ownKeys(D).forEach(ee=>{Qn(ee,D[ee])})}u&&Wr(u,e,"c");function Y(D,ee){he(ee)?ee.forEach(K=>D(K.bind(n))):ee&&D(ee.bind(n))}if(Y(Il,f),Y(ut,g),Y(Bl,h),Y(Tl,_),Y(Cl,m),Y(Al,w),Y(zl,Z),Y($l,G),Y(Rl,N),Y(Ii,x),Y(as,B),Y(Pl,M),he(q))if(q.length){const D=e.exposed||(e.exposed={});q.forEach(ee=>{Object.defineProperty(D,ee,{get:()=>n[ee],set:K=>n[ee]=K,enumerable:!0})})}else e.exposed||(e.exposed={});P&&e.render===ot&&(e.render=P),ie!=null&&(e.inheritAttrs=ie),C&&(e.components=C),j&&(e.directives=j),M&&Ai(e)}function Fl(e,t,n=ot){he(e)&&(e=nr(e));for(const s in e){const r=e[s];let o;Be(r)?"default"in r?o=at(r.from||s,r.default,!0):o=at(r.from||s):o=at(r),He(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Wr(e,t,n){lt(he(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Pi(e,t,n,s){let r=s.includes(".")?Ji(n,s):()=>n[s];if(Pe(e)){const o=t[e];pe(o)&&on(r,o)}else if(pe(e))on(r,e.bind(n));else if(Be(e))if(he(e))e.forEach(o=>Pi(o,t,n,s));else{const o=pe(e.handler)?e.handler.bind(n):t[e.handler];pe(o)&&on(r,o,e)}}function Er(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let c;return a?c=a:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>ls(c,d,i,!0)),ls(c,t,i)),Be(t)&&o.set(t,c),c}function ls(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&ls(e,o,n,!0),r&&r.forEach(i=>ls(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const a=Nl[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Nl={data:Vr,props:qr,emits:qr,methods:vn,computed:vn,beforeCreate:We,created:We,beforeMount:We,mounted:We,beforeUpdate:We,updated:We,beforeDestroy:We,beforeUnmount:We,destroyed:We,unmounted:We,activated:We,deactivated:We,errorCaptured:We,serverPrefetch:We,components:vn,directives:vn,watch:Ul,provide:Vr,inject:jl};function Vr(e,t){return t?e?function(){return Me(pe(e)?e.call(this,this):e,pe(t)?t.call(this,this):t)}:t:e}function jl(e,t){return vn(nr(e),nr(t))}function nr(e){if(he(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function We(e,t){return e?[...new Set([].concat(e,t))]:t}function vn(e,t){return e?Me(Object.create(null),e,t):t}function qr(e,t){return e?he(e)&&he(t)?[...new Set([...e,...t])]:Me(Object.create(null),Hr(e),Hr(t??{})):t}function Ul(e,t){if(!e)return t;if(!t)return e;const n=Me(Object.create(null),e);for(const s in t)n[s]=We(e[s],t[s]);return n}function Ri(){return{app:null,config:{isNativeTag:Pa,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Gl=0;function Jl(e,t){return function(s,r=null){pe(s)||(s=Me({},s)),r!=null&&!Be(r)&&(r=null);const o=Ri(),i=new WeakSet,a=[];let c=!1;const d=o.app={_uid:Gl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:kc,get config(){return o.config},set config(u){},use(u,...f){return i.has(u)||(u&&pe(u.install)?(i.add(u),u.install(d,...f)):pe(u)&&(i.add(u),u(d,...f))),d},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),d},component(u,f){return f?(o.components[u]=f,d):o.components[u]},directive(u,f){return f?(o.directives[u]=f,d):o.directives[u]},mount(u,f,g){if(!c){const h=d._ceVNode||_e(s,r);return h.appContext=o,g===!0?g="svg":g===!1&&(g=void 0),f&&t?t(h,u):e(h,u,g),c=!0,d._container=u,u.__vue_app__=d,Es(h.component)}},onUnmount(u){a.push(u)},unmount(){c&&(lt(a,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,f){return o.provides[u]=f,d},runWithContext(u){const f=rn;rn=d;try{return u()}finally{rn=f}}};return d}}let rn=null;function Qn(e,t){if(Ge){let n=Ge.provides;const s=Ge.parent&&Ge.parent.provides;s===n&&(n=Ge.provides=Object.create(s)),n[e]=t}}function at(e,t,n=!1){const s=Ki();if(s||rn){let r=rn?rn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&pe(t)?t.call(s&&s.proxy):t}}const $i={},zi=()=>Object.create($i),Di=e=>Object.getPrototypeOf(e)===$i;function Hl(e,t,n,s=!1){const r={},o=zi();e.propsDefaults=Object.create(null),Mi(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:li(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Wl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,a=ke(r),[c]=e.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let g=u[f];if(Ss(e.emitsOptions,g))continue;const h=t[g];if(c)if(Ce(o,g))h!==o[g]&&(o[g]=h,d=!0);else{const _=rt(g);r[_]=sr(c,a,_,h,e,!1)}else h!==o[g]&&(o[g]=h,d=!0)}}}else{Mi(e,t,r,o)&&(d=!0);let u;for(const f in a)(!t||!Ce(t,f)&&((u=Nt(f))===f||!Ce(t,u)))&&(c?n&&(n[f]!==void 0||n[u]!==void 0)&&(r[f]=sr(c,a,f,void 0,e,!0)):delete r[f]);if(o!==a)for(const f in o)(!t||!Ce(t,f))&&(delete o[f],d=!0)}d&&St(e.attrs,"set","")}function Mi(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,a;if(t)for(let c in t){if(_n(c))continue;const d=t[c];let u;r&&Ce(r,u=rt(c))?!o||!o.includes(u)?n[u]=d:(a||(a={}))[u]=d:Ss(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,i=!0)}if(o){const c=ke(n),d=a||Oe;for(let u=0;u<o.length;u++){const f=o[u];n[f]=sr(r,c,f,d[f],e,!Ce(d,f))}}return i}function sr(e,t,n,s,r,o){const i=e[n];if(i!=null){const a=Ce(i,"default");if(a&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&pe(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const u=Ln(r);s=d[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!a?s=!1:i[1]&&(s===""||s===Nt(n))&&(s=!0))}return s}const Vl=new WeakMap;function Li(e,t,n=!1){const s=n?Vl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},a=[];let c=!1;if(!pe(e)){const u=f=>{c=!0;const[g,h]=Li(f,t,!0);Me(i,g),h&&a.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return Be(e)&&s.set(e,en),en;if(he(o))for(let u=0;u<o.length;u++){const f=rt(o[u]);Kr(f)&&(i[f]=Oe)}else if(o)for(const u in o){const f=rt(u);if(Kr(f)){const g=o[u],h=i[f]=he(g)||pe(g)?{type:g}:Me({},g),_=h.type;let m=!1,w=!0;if(he(_))for(let b=0;b<_.length;++b){const x=_[b],k=pe(x)&&x.name;if(k==="Boolean"){m=!0;break}else k==="String"&&(w=!1)}else m=pe(_)&&_.name==="Boolean";h[0]=m,h[1]=w,(m||Ce(h,"default"))&&a.push(f)}}const d=[i,a];return Be(e)&&s.set(e,d),d}function Kr(e){return e[0]!=="$"&&!_n(e)}const Cr=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",Ar=e=>he(e)?e.map(pt):[pt(e)],ql=(e,t,n)=>{if(t._n)return t;const s=qe((...r)=>Ar(t(...r)),n);return s._c=!1,s},Fi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Cr(r))continue;const o=e[r];if(pe(o))t[r]=ql(r,o,s);else if(o!=null){const i=Ar(o);t[r]=()=>i}}},Ni=(e,t)=>{const n=Ar(t);e.slots.default=()=>n},ji=(e,t,n)=>{for(const s in t)(n||!Cr(s))&&(e[s]=t[s])},Kl=(e,t,n)=>{const s=e.slots=zi();if(e.vnode.shapeFlag&32){const r=t.__;r&&Ws(s,"__",r,!0);const o=t._;o?(ji(s,t,n),n&&Ws(s,"_",o,!0)):Fi(t,s)}else t&&Ni(e,t)},Zl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=Oe;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:ji(r,t,n):(o=!t.$stable,Fi(t,r)),i=t}else t&&(Ni(e,t),i={default:1});if(o)for(const a in r)!Cr(a)&&i[a]==null&&delete r[a]},Ve=uc;function Yl(e){return Xl(e)}function Xl(e,t){const n=gs();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:a,createComment:c,setText:d,setElementText:u,parentNode:f,nextSibling:g,setScopeId:h=ot,insertStaticContent:_}=e,m=(S,l,A,T=null,y=null,v=null,E=void 0,$=null,z=!!l.dynamicChildren)=>{if(S===l)return;S&&!Wt(S,l)&&(T=W(S),L(S,y,v,!0),S=null),l.patchFlag===-2&&(z=!1,l.dynamicChildren=null);const{type:I,ref:J,shapeFlag:U}=l;switch(I){case ks:w(S,l,A,T);break;case Ze:b(S,l,A,T);break;case es:S==null&&x(l,A,T,E);break;case De:C(S,l,A,T,y,v,E,$,z);break;default:U&1?P(S,l,A,T,y,v,E,$,z):U&6?j(S,l,A,T,y,v,E,$,z):(U&64||U&128)&&I.process(S,l,A,T,y,v,E,$,z,se)}J!=null&&y?xn(J,S&&S.ref,v,l||S,!l):J==null&&S&&S.ref!=null&&xn(S.ref,null,v,S,!0)},w=(S,l,A,T)=>{if(S==null)s(l.el=a(l.children),A,T);else{const y=l.el=S.el;l.children!==S.children&&d(y,l.children)}},b=(S,l,A,T)=>{S==null?s(l.el=c(l.children||""),A,T):l.el=S.el},x=(S,l,A,T)=>{[S.el,S.anchor]=_(S.children,l,A,T,S.el,S.anchor)},k=({el:S,anchor:l},A,T)=>{let y;for(;S&&S!==l;)y=g(S),s(S,A,T),S=y;s(l,A,T)},B=({el:S,anchor:l})=>{let A;for(;S&&S!==l;)A=g(S),r(S),S=A;r(l)},P=(S,l,A,T,y,v,E,$,z)=>{l.type==="svg"?E="svg":l.type==="math"&&(E="mathml"),S==null?G(l,A,T,y,v,E,$,z):M(S,l,y,v,E,$,z)},G=(S,l,A,T,y,v,E,$)=>{let z,I;const{props:J,shapeFlag:U,transition:H,dirs:Q}=S;if(z=S.el=i(S.type,v,J&&J.is,J),U&8?u(z,S.children):U&16&&Z(S.children,z,null,T,y,Ms(S,v),E,$),Q&&jt(S,null,T,"created"),N(z,S,S.scopeId,E,T),J){for(const ue in J)ue!=="value"&&!_n(ue)&&o(z,ue,null,J[ue],v,T);"value"in J&&o(z,"value",null,J.value,v),(I=J.onVnodeBeforeMount)&&dt(I,T,S)}Q&&jt(S,null,T,"beforeMount");const ce=Ql(y,H);ce&&H.beforeEnter(z),s(z,l,A),((I=J&&J.onVnodeMounted)||ce||Q)&&Ve(()=>{I&&dt(I,T,S),ce&&H.enter(z),Q&&jt(S,null,T,"mounted")},y)},N=(S,l,A,T,y)=>{if(A&&h(S,A),T)for(let v=0;v<T.length;v++)h(S,T[v]);if(y){let v=y.subTree;if(l===v||Wi(v.type)&&(v.ssContent===l||v.ssFallback===l)){const E=y.vnode;N(S,E,E.scopeId,E.slotScopeIds,y.parent)}}},Z=(S,l,A,T,y,v,E,$,z=0)=>{for(let I=z;I<S.length;I++){const J=S[I]=$?Rt(S[I]):pt(S[I]);m(null,J,l,A,T,y,v,E,$)}},M=(S,l,A,T,y,v,E)=>{const $=l.el=S.el;let{patchFlag:z,dynamicChildren:I,dirs:J}=l;z|=S.patchFlag&16;const U=S.props||Oe,H=l.props||Oe;let Q;if(A&&Ut(A,!1),(Q=H.onVnodeBeforeUpdate)&&dt(Q,A,l,S),J&&jt(l,S,A,"beforeUpdate"),A&&Ut(A,!0),(U.innerHTML&&H.innerHTML==null||U.textContent&&H.textContent==null)&&u($,""),I?q(S.dynamicChildren,I,$,A,T,Ms(l,y),v):E||ee(S,l,$,null,A,T,Ms(l,y),v,!1),z>0){if(z&16)ie($,U,H,A,y);else if(z&2&&U.class!==H.class&&o($,"class",null,H.class,y),z&4&&o($,"style",U.style,H.style,y),z&8){const ce=l.dynamicProps;for(let ue=0;ue<ce.length;ue++){const me=ce[ue],Re=U[me],Ae=H[me];(Ae!==Re||me==="value")&&o($,me,Re,Ae,y,A)}}z&1&&S.children!==l.children&&u($,l.children)}else!E&&I==null&&ie($,U,H,A,y);((Q=H.onVnodeUpdated)||J)&&Ve(()=>{Q&&dt(Q,A,l,S),J&&jt(l,S,A,"updated")},T)},q=(S,l,A,T,y,v,E)=>{for(let $=0;$<l.length;$++){const z=S[$],I=l[$],J=z.el&&(z.type===De||!Wt(z,I)||z.shapeFlag&198)?f(z.el):A;m(z,I,J,null,T,y,v,E,!0)}},ie=(S,l,A,T,y)=>{if(l!==A){if(l!==Oe)for(const v in l)!_n(v)&&!(v in A)&&o(S,v,l[v],null,y,T);for(const v in A){if(_n(v))continue;const E=A[v],$=l[v];E!==$&&v!=="value"&&o(S,v,$,E,y,T)}"value"in A&&o(S,"value",l.value,A.value,y)}},C=(S,l,A,T,y,v,E,$,z)=>{const I=l.el=S?S.el:a(""),J=l.anchor=S?S.anchor:a("");let{patchFlag:U,dynamicChildren:H,slotScopeIds:Q}=l;Q&&($=$?$.concat(Q):Q),S==null?(s(I,A,T),s(J,A,T),Z(l.children||[],A,J,y,v,E,$,z)):U>0&&U&64&&H&&S.dynamicChildren?(q(S.dynamicChildren,H,A,y,v,E,$),(l.key!=null||y&&l===y.subTree)&&Or(S,l,!0)):ee(S,l,A,J,y,v,E,$,z)},j=(S,l,A,T,y,v,E,$,z)=>{l.slotScopeIds=$,S==null?l.shapeFlag&512?y.ctx.activate(l,A,T,E,z):p(l,A,T,y,v,E,z):R(S,l,z)},p=(S,l,A,T,y,v,E)=>{const $=S.component=vc(S,T,y);if(ws(S)&&($.ctx.renderer=se),_c($,!1,E),$.asyncDep){if(y&&y.registerDep($,Y,E),!S.el){const z=$.subTree=_e(Ze);b(null,z,l,A),S.placeholder=z.el}}else Y($,S,l,A,y,v,E)},R=(S,l,A)=>{const T=l.component=S.component;if(lc(S,l,A))if(T.asyncDep&&!T.asyncResolved){D(T,l,A);return}else T.next=l,T.update();else l.el=S.el,T.vnode=l},Y=(S,l,A,T,y,v,E)=>{const $=()=>{if(S.isMounted){let{next:U,bu:H,u:Q,parent:ce,vnode:ue}=S;{const ye=Ui(S);if(ye){U&&(U.el=ue.el,D(S,U,E)),ye.asyncDep.then(()=>{S.isUnmounted||$()});return}}let me=U,Re;Ut(S,!1),U?(U.el=ue.el,D(S,U,E)):U=ue,H&&Yn(H),(Re=U.props&&U.props.onVnodeBeforeUpdate)&&dt(Re,ce,U,ue),Ut(S,!0);const Ae=Ls(S),Fe=S.subTree;S.subTree=Ae,m(Fe,Ae,f(Fe.el),W(Fe),S,y,v),U.el=Ae.el,me===null&&cc(S,Ae.el),Q&&Ve(Q,y),(Re=U.props&&U.props.onVnodeUpdated)&&Ve(()=>dt(Re,ce,U,ue),y)}else{let U;const{el:H,props:Q}=l,{bm:ce,m:ue,parent:me,root:Re,type:Ae}=S,Fe=Sn(l);if(Ut(S,!1),ce&&Yn(ce),!Fe&&(U=Q&&Q.onVnodeBeforeMount)&&dt(U,me,l),Ut(S,!0),H&&ve){const ye=()=>{S.subTree=Ls(S),ve(H,S.subTree,S,y,null)};Fe&&Ae.__asyncHydrate?Ae.__asyncHydrate(H,S,ye):ye()}else{Re.ce&&Re.ce._def.shadowRoot!==!1&&Re.ce._injectChildStyle(Ae);const ye=S.subTree=Ls(S);m(null,ye,A,T,S,y,v),l.el=ye.el}if(ue&&Ve(ue,y),!Fe&&(U=Q&&Q.onVnodeMounted)){const ye=l;Ve(()=>dt(U,me,ye),y)}(l.shapeFlag&256||me&&Sn(me.vnode)&&me.vnode.shapeFlag&256)&&S.a&&Ve(S.a,y),S.isMounted=!0,l=A=T=null}};S.scope.on();const z=S.effect=new qo($);S.scope.off();const I=S.update=z.run.bind(z),J=S.job=z.runIfDirty.bind(z);J.i=S,J.id=S.uid,z.scheduler=()=>Sr(J),Ut(S,!0),I()},D=(S,l,A)=>{l.component=S;const T=S.vnode.props;S.vnode=l,S.next=null,Wl(S,l.props,T,A),Zl(S,l.children,A),Ct(),Fr(S),At()},ee=(S,l,A,T,y,v,E,$,z=!1)=>{const I=S&&S.children,J=S?S.shapeFlag:0,U=l.children,{patchFlag:H,shapeFlag:Q}=l;if(H>0){if(H&128){oe(I,U,A,T,y,v,E,$,z);return}else if(H&256){K(I,U,A,T,y,v,E,$,z);return}}Q&8?(J&16&&ge(I,y,v),U!==I&&u(A,U)):J&16?Q&16?oe(I,U,A,T,y,v,E,$,z):ge(I,y,v,!0):(J&8&&u(A,""),Q&16&&Z(U,A,T,y,v,E,$,z))},K=(S,l,A,T,y,v,E,$,z)=>{S=S||en,l=l||en;const I=S.length,J=l.length,U=Math.min(I,J);let H;for(H=0;H<U;H++){const Q=l[H]=z?Rt(l[H]):pt(l[H]);m(S[H],Q,A,null,y,v,E,$,z)}I>J?ge(S,y,v,!0,!1,U):Z(l,A,T,y,v,E,$,z,U)},oe=(S,l,A,T,y,v,E,$,z)=>{let I=0;const J=l.length;let U=S.length-1,H=J-1;for(;I<=U&&I<=H;){const Q=S[I],ce=l[I]=z?Rt(l[I]):pt(l[I]);if(Wt(Q,ce))m(Q,ce,A,null,y,v,E,$,z);else break;I++}for(;I<=U&&I<=H;){const Q=S[U],ce=l[H]=z?Rt(l[H]):pt(l[H]);if(Wt(Q,ce))m(Q,ce,A,null,y,v,E,$,z);else break;U--,H--}if(I>U){if(I<=H){const Q=H+1,ce=Q<J?l[Q].el:T;for(;I<=H;)m(null,l[I]=z?Rt(l[I]):pt(l[I]),A,ce,y,v,E,$,z),I++}}else if(I>H)for(;I<=U;)L(S[I],y,v,!0),I++;else{const Q=I,ce=I,ue=new Map;for(I=ce;I<=H;I++){const Te=l[I]=z?Rt(l[I]):pt(l[I]);Te.key!=null&&ue.set(Te.key,I)}let me,Re=0;const Ae=H-ce+1;let Fe=!1,ye=0;const ft=new Array(Ae);for(I=0;I<Ae;I++)ft[I]=0;for(I=Q;I<=U;I++){const Te=S[I];if(Re>=Ae){L(Te,y,v,!0);continue}let Xe;if(Te.key!=null)Xe=ue.get(Te.key);else for(me=ce;me<=H;me++)if(ft[me-ce]===0&&Wt(Te,l[me])){Xe=me;break}Xe===void 0?L(Te,y,v,!0):(ft[Xe-ce]=I+1,Xe>=ye?ye=Xe:Fe=!0,m(Te,l[Xe],A,null,y,v,E,$,z),Re++)}const Kt=Fe?ec(ft):en;for(me=Kt.length-1,I=Ae-1;I>=0;I--){const Te=ce+I,Xe=l[Te],jn=l[Te+1],Un=Te+1<J?jn.el||jn.placeholder:T;ft[I]===0?m(null,Xe,A,Un,y,v,E,$,z):Fe&&(me<0||I!==Kt[me]?F(Xe,A,Un,2):me--)}}},F=(S,l,A,T,y=null)=>{const{el:v,type:E,transition:$,children:z,shapeFlag:I}=S;if(I&6){F(S.component.subTree,l,A,T);return}if(I&128){S.suspense.move(l,A,T);return}if(I&64){E.move(S,l,A,se);return}if(E===De){s(v,l,A);for(let U=0;U<z.length;U++)F(z[U],l,A,T);s(S.anchor,l,A);return}if(E===es){k(S,l,A);return}if(T!==2&&I&1&&$)if(T===0)$.beforeEnter(v),s(v,l,A),Ve(()=>$.enter(v),y);else{const{leave:U,delayLeave:H,afterLeave:Q}=$,ce=()=>{S.ctx.isUnmounted?r(v):s(v,l,A)},ue=()=>{U(v,()=>{ce(),Q&&Q()})};H?H(v,ce,ue):ue()}else s(v,l,A)},L=(S,l,A,T=!1,y=!1)=>{const{type:v,props:E,ref:$,children:z,dynamicChildren:I,shapeFlag:J,patchFlag:U,dirs:H,cacheIndex:Q}=S;if(U===-2&&(y=!1),$!=null&&(Ct(),xn($,null,A,S,!0),At()),Q!=null&&(l.renderCache[Q]=void 0),J&256){l.ctx.deactivate(S);return}const ce=J&1&&H,ue=!Sn(S);let me;if(ue&&(me=E&&E.onVnodeBeforeUnmount)&&dt(me,l,S),J&6)te(S.component,A,T);else{if(J&128){S.suspense.unmount(A,T);return}ce&&jt(S,null,l,"beforeUnmount"),J&64?S.type.remove(S,l,A,se,T):I&&!I.hasOnce&&(v!==De||U>0&&U&64)?ge(I,l,A,!1,!0):(v===De&&U&384||!y&&J&16)&&ge(z,l,A),T&&le(S)}(ue&&(me=E&&E.onVnodeUnmounted)||ce)&&Ve(()=>{me&&dt(me,l,S),ce&&jt(S,null,l,"unmounted")},A)},le=S=>{const{type:l,el:A,anchor:T,transition:y}=S;if(l===De){ae(A,T);return}if(l===es){B(S);return}const v=()=>{r(A),y&&!y.persisted&&y.afterLeave&&y.afterLeave()};if(S.shapeFlag&1&&y&&!y.persisted){const{leave:E,delayLeave:$}=y,z=()=>E(A,v);$?$(S.el,v,z):z()}else v()},ae=(S,l)=>{let A;for(;S!==l;)A=g(S),r(S),S=A;r(l)},te=(S,l,A)=>{const{bum:T,scope:y,job:v,subTree:E,um:$,m:z,a:I,parent:J,slots:{__:U}}=S;Zr(z),Zr(I),T&&Yn(T),J&&he(U)&&U.forEach(H=>{J.renderCache[H]=void 0}),y.stop(),v&&(v.flags|=8,L(E,S,l,A)),$&&Ve($,l),Ve(()=>{S.isUnmounted=!0},l),l&&l.pendingBranch&&!l.isUnmounted&&S.asyncDep&&!S.asyncResolved&&S.suspenseId===l.pendingId&&(l.deps--,l.deps===0&&l.resolve())},ge=(S,l,A,T=!1,y=!1,v=0)=>{for(let E=v;E<S.length;E++)L(S[E],l,A,T,y)},W=S=>{if(S.shapeFlag&6)return W(S.component.subTree);if(S.shapeFlag&128)return S.suspense.next();const l=g(S.anchor||S.el),A=l&&l[_i];return A?g(A):l};let X=!1;const V=(S,l,A)=>{S==null?l._vnode&&L(l._vnode,null,null,!0):m(l._vnode||null,S,l,null,null,null,A),l._vnode=S,X||(X=!0,Fr(),pi(),X=!1)},se={p:m,um:L,m:F,r:le,mt:p,mc:Z,pc:ee,pbc:q,n:W,o:e};let fe,ve;return t&&([fe,ve]=t(se)),{render:V,hydrate:fe,createApp:Jl(V,fe)}}function Ms({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ut({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ql(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Or(e,t,n=!1){const s=e.children,r=t.children;if(he(s)&&he(r))for(let o=0;o<s.length;o++){const i=s[o];let a=r[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=r[o]=Rt(r[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Or(i,a)),a.type===ks&&(a.el=i.el),a.type===Ze&&!a.el&&(a.el=i.el)}}function ec(e){const t=e.slice(),n=[0];let s,r,o,i,a;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<d?o=a+1:i=a;d<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ui(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ui(t)}function Zr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const tc=Symbol.for("v-scx"),nc=()=>at(tc);function on(e,t,n){return Gi(e,t,n)}function Gi(e,t,n=Oe){const{immediate:s,deep:r,flush:o,once:i}=n,a=Me({},n),c=t&&s||!t&&o!=="post";let d;if(Rn){if(o==="sync"){const h=nc();d=h.__watcherHandles||(h.__watcherHandles=[])}else if(!c){const h=()=>{};return h.stop=ot,h.resume=ot,h.pause=ot,h}}const u=Ge;a.call=(h,_,m)=>lt(h,u,_,m);let f=!1;o==="post"?a.scheduler=h=>{Ve(h,u&&u.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(h,_)=>{_?h():Sr(h)}),a.augmentJob=h=>{t&&(h.flags|=4),f&&(h.flags|=2,u&&(h.id=u.uid,h.i=u))};const g=vl(e,t,a);return Rn&&(d?d.push(g):c&&g()),g}function sc(e,t,n){const s=this.proxy,r=Pe(e)?e.includes(".")?Ji(s,e):()=>s[e]:e.bind(s,s);let o;pe(t)?o=t:(o=t.handler,n=t);const i=Ln(this),a=Gi(r,o.bind(s),n);return i(),a}function Ji(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const rc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${rt(t)}Modifiers`]||e[`${Nt(t)}Modifiers`];function oc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Oe;let r=n;const o=t.startsWith("update:"),i=o&&rc(s,t.slice(7));i&&(i.trim&&(r=n.map(u=>Pe(u)?u.trim():u)),i.number&&(r=n.map(Vs)));let a,c=s[a=Bs(t)]||s[a=Bs(rt(t))];!c&&o&&(c=s[a=Bs(Nt(t))]),c&&lt(c,e,6,r);const d=s[a+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,lt(d,e,6,r)}}function Hi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},a=!1;if(!pe(e)){const c=d=>{const u=Hi(d,t,!0);u&&(a=!0,Me(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!a?(Be(e)&&s.set(e,null),null):(he(o)?o.forEach(c=>i[c]=null):Me(i,o),Be(e)&&s.set(e,i),i)}function Ss(e,t){return!e||!ds(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ce(e,t[0].toLowerCase()+t.slice(1))||Ce(e,Nt(t))||Ce(e,t))}function Ls(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:a,emit:c,render:d,renderCache:u,props:f,data:g,setupState:h,ctx:_,inheritAttrs:m}=e,w=is(e);let b,x;try{if(n.shapeFlag&4){const B=r||s,P=B;b=pt(d.call(P,B,u,f,h,g,_)),x=a}else{const B=t;b=pt(B.length>1?B(f,{attrs:a,slots:i,emit:c}):B(f,null)),x=t.props?a:ic(a)}}catch(B){En.length=0,bs(B,e,1),b=_e(Ze)}let k=b;if(x&&m!==!1){const B=Object.keys(x),{shapeFlag:P}=k;B.length&&P&7&&(o&&B.some(dr)&&(x=ac(x,o)),k=Lt(k,x,!1,!0))}return n.dirs&&(k=Lt(k,null,!1,!0),k.dirs=k.dirs?k.dirs.concat(n.dirs):n.dirs),n.transition&&Tn(k,n.transition),b=k,is(w),b}const ic=e=>{let t;for(const n in e)(n==="class"||n==="style"||ds(n))&&((t||(t={}))[n]=e[n]);return t},ac=(e,t)=>{const n={};for(const s in e)(!dr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function lc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:a,patchFlag:c}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Yr(s,i,d):!!i;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const g=u[f];if(i[g]!==s[g]&&!Ss(d,g))return!0}}}else return(r||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?Yr(s,i,d):!0:!!i;return!1}function Yr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Ss(n,o))return!0}return!1}function cc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Wi=e=>e.__isSuspense;function uc(e,t){t&&t.pendingBranch?he(e)?t.effects.push(...e):t.effects.push(e):bl(e)}const De=Symbol.for("v-fgt"),ks=Symbol.for("v-txt"),Ze=Symbol.for("v-cmt"),es=Symbol.for("v-stc"),En=[];let et=null;function ne(e=!1){En.push(et=e?null:[])}function fc(){En.pop(),et=En[En.length-1]||null}let Pn=1;function Xr(e,t=!1){Pn+=e,e<0&&et&&t&&(et.hasOnce=!0)}function Vi(e){return e.dynamicChildren=Pn>0?et||en:null,fc(),Pn>0&&et&&et.push(e),e}function re(e,t,n,s,r,o){return Vi(O(e,t,n,s,r,o,!0))}function rr(e,t,n,s,r){return Vi(_e(e,t,n,s,r,!0))}function cs(e){return e?e.__v_isVNode===!0:!1}function Wt(e,t){return e.type===t.type&&e.key===t.key}const qi=({key:e})=>e??null,ts=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Pe(e)||He(e)||pe(e)?{i:Qe,r:e,k:t,f:!!n}:e:null);function O(e,t=null,n=null,s=0,r=null,o=e===De?0:1,i=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&qi(t),ref:t&&ts(t),scopeId:gi,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Qe};return a?(Ir(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=Pe(n)?8:16),Pn>0&&!i&&et&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&et.push(c),c}const _e=dc;function dc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Bi)&&(e=Ze),cs(e)){const a=Lt(e,t,!0);return n&&Ir(a,n),Pn>0&&!o&&et&&(a.shapeFlag&6?et[et.indexOf(e)]=a:et.push(a)),a.patchFlag=-2,a}if(Sc(e)&&(e=e.__vccOpts),t){t=hc(t);let{class:a,style:c}=t;a&&!Pe(a)&&(t.class=Le(a)),Be(c)&&(wr(c)&&!he(c)&&(c=Me({},c)),t.style=vs(c))}const i=Pe(e)?1:Wi(e)?128:yi(e)?64:Be(e)?4:pe(e)?2:0;return O(e,t,n,s,r,i,o,!0)}function hc(e){return e?wr(e)||Di(e)?Me({},e):e:null}function Lt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:a,transition:c}=e,d=t?pc(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&qi(d),ref:t&&t.ref?n&&o?he(o)?o.concat(ts(t)):[o,ts(t)]:ts(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==De?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Lt(e.ssContent),ssFallback:e.ssFallback&&Lt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Tn(u,c.clone(u)),u}function we(e=" ",t=0){return _e(ks,null,e,t)}function an(e,t){const n=_e(es,null,e);return n.staticCount=t,n}function Se(e="",t=!1){return t?(ne(),rr(Ze,null,e)):_e(Ze,null,e)}function pt(e){return e==null||typeof e=="boolean"?_e(Ze):he(e)?_e(De,null,e.slice()):cs(e)?Rt(e):_e(ks,null,String(e))}function Rt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Lt(e)}function Ir(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(he(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Ir(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Di(t)?t._ctx=Qe:r===3&&Qe&&(Qe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else pe(t)?(t={default:t,_ctx:Qe},n=32):(t=String(t),s&64?(n=16,t=[we(t)]):n=8);e.children=t,e.shapeFlag|=n}function pc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Le([t.class,s.class]));else if(r==="style")t.style=vs([t.style,s.style]);else if(ds(r)){const o=t[r],i=s[r];i&&o!==i&&!(he(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function dt(e,t,n,s=null){lt(e,t,7,[n,s])}const mc=Ri();let gc=0;function vc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||mc,o={uid:gc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ja(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Li(s,r),emitsOptions:Hi(s,r),emit:null,emitted:null,propsDefaults:Oe,inheritAttrs:s.inheritAttrs,ctx:Oe,data:Oe,props:Oe,attrs:Oe,slots:Oe,refs:Oe,setupState:Oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=oc.bind(null,o),e.ce&&e.ce(o),o}let Ge=null;const Ki=()=>Ge||Qe;let us,or;{const e=gs(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};us=t("__VUE_INSTANCE_SETTERS__",n=>Ge=n),or=t("__VUE_SSR_SETTERS__",n=>Rn=n)}const Ln=e=>{const t=Ge;return us(e),e.scope.on(),()=>{e.scope.off(),us(t)}},Qr=()=>{Ge&&Ge.scope.off(),us(null)};function Zi(e){return e.vnode.shapeFlag&4}let Rn=!1;function _c(e,t=!1,n=!1){t&&or(t);const{props:s,children:r}=e.vnode,o=Zi(e);Hl(e,s,o,t),Kl(e,r,n||t);const i=o?yc(e,t):void 0;return t&&or(!1),i}function yc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ml);const{setup:s}=n;if(s){Ct();const r=e.setupContext=s.length>1?wc(e):null,o=Ln(e),i=Mn(s,e,0,[e.props,r]),a=Uo(i);if(At(),o(),(a||e.sp)&&!Sn(e)&&Ai(e),a){if(i.then(Qr,Qr),t)return i.then(c=>{eo(e,c,t)}).catch(c=>{bs(c,e,0)});e.asyncDep=i}else eo(e,i,t)}else Yi(e,t)}function eo(e,t,n){pe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Be(t)&&(e.setupState=fi(t)),Yi(e,n)}let to;function Yi(e,t,n){const s=e.type;if(!e.render){if(!t&&to&&!s.render){const r=s.template||Er(e).template;if(r){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:c}=s,d=Me(Me({isCustomElement:o,delimiters:a},i),c);s.render=to(r,d)}}e.render=s.render||ot}{const r=Ln(e);Ct();try{Ll(e)}finally{At(),r()}}}const bc={get(e,t){return Ue(e,"get",""),e[t]}};function wc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,bc),slots:e.slots,emit:e.emit,expose:t}}function Es(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fi(ul(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in kn)return kn[n](e)},has(t,n){return n in t||n in kn}})):e.proxy}function xc(e,t=!0){return pe(e)?e.displayName||e.name:e.name||t&&e.__name}function Sc(e){return pe(e)&&"__vccOpts"in e}const Je=(e,t)=>ml(e,t,Rn);function nt(e,t,n){const s=arguments.length;return s===2?Be(t)&&!he(t)?cs(t)?_e(e,null,[t]):_e(e,t):_e(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&cs(n)&&(n=[n]),_e(e,t,n))}const kc="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ir;const no=typeof window<"u"&&window.trustedTypes;if(no)try{ir=no.createPolicy("vue",{createHTML:e=>e})}catch{}const Xi=ir?e=>ir.createHTML(e):e=>e,Ec="http://www.w3.org/2000/svg",Cc="http://www.w3.org/1998/Math/MathML",wt=typeof document<"u"?document:null,so=wt&&wt.createElement("template"),Ac={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?wt.createElementNS(Ec,e):t==="mathml"?wt.createElementNS(Cc,e):n?wt.createElement(e,{is:n}):wt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>wt.createTextNode(e),createComment:e=>wt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>wt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{so.innerHTML=Xi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=so.content;if(s==="svg"||s==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},It="transition",hn="animation",$n=Symbol("_vtc"),Qi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Oc=Me({},xi,Qi),Ic=e=>(e.displayName="Transition",e.props=Oc,e),Bc=Ic((e,{slots:t})=>nt(El,Tc(e),t)),Gt=(e,t=[])=>{he(e)?e.forEach(n=>n(...t)):e&&e(...t)},ro=e=>e?he(e)?e.some(t=>t.length>1):e.length>1:!1;function Tc(e){const t={};for(const C in e)C in Qi||(t[C]=e[C]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:d=i,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:g=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,_=Pc(r),m=_&&_[0],w=_&&_[1],{onBeforeEnter:b,onEnter:x,onEnterCancelled:k,onLeave:B,onLeaveCancelled:P,onBeforeAppear:G=b,onAppear:N=x,onAppearCancelled:Z=k}=t,M=(C,j,p,R)=>{C._enterCancelled=R,Jt(C,j?u:a),Jt(C,j?d:i),p&&p()},q=(C,j)=>{C._isLeaving=!1,Jt(C,f),Jt(C,h),Jt(C,g),j&&j()},ie=C=>(j,p)=>{const R=C?N:x,Y=()=>M(j,C,p);Gt(R,[j,Y]),oo(()=>{Jt(j,C?c:o),yt(j,C?u:a),ro(R)||io(j,s,m,Y)})};return Me(t,{onBeforeEnter(C){Gt(b,[C]),yt(C,o),yt(C,i)},onBeforeAppear(C){Gt(G,[C]),yt(C,c),yt(C,d)},onEnter:ie(!1),onAppear:ie(!0),onLeave(C,j){C._isLeaving=!0;const p=()=>q(C,j);yt(C,f),C._enterCancelled?(yt(C,g),co()):(co(),yt(C,g)),oo(()=>{C._isLeaving&&(Jt(C,f),yt(C,h),ro(B)||io(C,s,w,p))}),Gt(B,[C,p])},onEnterCancelled(C){M(C,!1,void 0,!0),Gt(k,[C])},onAppearCancelled(C){M(C,!0,void 0,!0),Gt(Z,[C])},onLeaveCancelled(C){q(C),Gt(P,[C])}})}function Pc(e){if(e==null)return null;if(Be(e))return[Fs(e.enter),Fs(e.leave)];{const t=Fs(e);return[t,t]}}function Fs(e){return Ma(e)}function yt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[$n]||(e[$n]=new Set)).add(t)}function Jt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[$n];n&&(n.delete(t),n.size||(e[$n]=void 0))}function oo(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Rc=0;function io(e,t,n,s){const r=e._endId=++Rc,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:c}=$c(e,t);if(!i)return s();const d=i+"end";let u=0;const f=()=>{e.removeEventListener(d,g),o()},g=h=>{h.target===e&&++u>=c&&f()};setTimeout(()=>{u<c&&f()},a+1),e.addEventListener(d,g)}function $c(e,t){const n=window.getComputedStyle(e),s=_=>(n[_]||"").split(", "),r=s(`${It}Delay`),o=s(`${It}Duration`),i=ao(r,o),a=s(`${hn}Delay`),c=s(`${hn}Duration`),d=ao(a,c);let u=null,f=0,g=0;t===It?i>0&&(u=It,f=i,g=o.length):t===hn?d>0&&(u=hn,f=d,g=c.length):(f=Math.max(i,d),u=f>0?i>d?It:hn:null,g=u?u===It?o.length:c.length:0);const h=u===It&&/\b(transform|all)(,|$)/.test(s(`${It}Property`).toString());return{type:u,timeout:f,propCount:g,hasTransform:h}}function ao(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>lo(n)+lo(e[s])))}function lo(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function co(){return document.body.offsetHeight}function zc(e,t,n){const s=e[$n];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const fs=Symbol("_vod"),ea=Symbol("_vsh"),Dc={beforeMount(e,{value:t},{transition:n}){e[fs]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):pn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),pn(e,!0),s.enter(e)):s.leave(e,()=>{pn(e,!1)}):pn(e,t))},beforeUnmount(e,{value:t}){pn(e,t)}};function pn(e,t){e.style.display=t?e[fs]:"none",e[ea]=!t}const Mc=Symbol(""),Lc=/(^|;)\s*display\s*:/;function Fc(e,t,n){const s=e.style,r=Pe(n);let o=!1;if(n&&!r){if(t)if(Pe(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&ns(s,a,"")}else for(const i in t)n[i]==null&&ns(s,i,"");for(const i in n)i==="display"&&(o=!0),ns(s,i,n[i])}else if(r){if(t!==n){const i=s[Mc];i&&(n+=";"+i),s.cssText=n,o=Lc.test(n)}}else t&&e.removeAttribute("style");fs in e&&(e[fs]=o?s.display:"",e[ea]&&(s.display="none"))}const uo=/\s*!important$/;function ns(e,t,n){if(he(n))n.forEach(s=>ns(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Nc(e,t);uo.test(n)?e.setProperty(Nt(s),n.replace(uo,""),"important"):e[s]=n}}const fo=["Webkit","Moz","ms"],Ns={};function Nc(e,t){const n=Ns[t];if(n)return n;let s=rt(t);if(s!=="filter"&&s in e)return Ns[t]=s;s=ms(s);for(let r=0;r<fo.length;r++){const o=fo[r]+s;if(o in e)return Ns[t]=o}return t}const ho="http://www.w3.org/1999/xlink";function po(e,t,n,s,r,o=Ga(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ho,t.slice(6,t.length)):e.setAttributeNS(ho,t,n):n==null||o&&!Ho(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Ft(n)?String(n):n)}function mo(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Xi(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(a!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Ho(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Xt(e,t,n,s){e.addEventListener(t,n,s)}function jc(e,t,n,s){e.removeEventListener(t,n,s)}const go=Symbol("_vei");function Uc(e,t,n,s,r=null){const o=e[go]||(e[go]={}),i=o[t];if(s&&i)i.value=s;else{const[a,c]=Gc(t);if(s){const d=o[t]=Wc(s,r);Xt(e,a,d,c)}else i&&(jc(e,a,i,c),o[t]=void 0)}}const vo=/(?:Once|Passive|Capture)$/;function Gc(e){let t;if(vo.test(e)){t={};let s;for(;s=e.match(vo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Nt(e.slice(2)),t]}let js=0;const Jc=Promise.resolve(),Hc=()=>js||(Jc.then(()=>js=0),js=Date.now());function Wc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;lt(Vc(s,n.value),t,5,[s])};return n.value=e,n.attached=Hc(),n}function Vc(e,t){if(he(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const _o=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,qc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?zc(e,s,i):t==="style"?Fc(e,n,s):ds(t)?dr(t)||Uc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Kc(e,t,s,i))?(mo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&po(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Pe(s))?mo(e,rt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),po(e,t,s,i))};function Kc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&_o(t)&&pe(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return _o(t)&&Pe(n)?!1:t in e}const yo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return he(t)?n=>Yn(t,n):t};function Zc(e){e.target.composing=!0}function bo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Us=Symbol("_assign"),Yc={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Us]=yo(r);const o=s||r.props&&r.props.type==="number";Xt(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=Vs(a)),e[Us](a)}),n&&Xt(e,"change",()=>{e.value=e.value.trim()}),t||(Xt(e,"compositionstart",Zc),Xt(e,"compositionend",bo),Xt(e,"change",bo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Us]=yo(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?Vs(e.value):e.value,c=t??"";a!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Xc=["ctrl","shift","alt","meta"],Qc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Xc.some(n=>e[`${n}Key`]&&!t.includes(n))},Et=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const a=Qc[t[i]];if(a&&a(r,t))return}return e(r,...o)})},eu={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},wo=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Nt(r.key);if(t.some(i=>i===o||eu[i]===o))return e(r)})},tu=Me({patchProp:qc},Ac);let xo;function nu(){return xo||(xo=Yl(tu))}const ta=(...e)=>{const t=nu().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=ru(s);if(!r)return;const o=t._component;!pe(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,su(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function su(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ru(e){return Pe(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Qt=typeof document<"u";function na(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ou(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&na(e.default)}const Ee=Object.assign;function Gs(e,t){const n={};for(const s in t){const r=t[s];n[s]=ct(r)?r.map(e):e(r)}return n}const Cn=()=>{},ct=Array.isArray,sa=/#/g,iu=/&/g,au=/\//g,lu=/=/g,cu=/\?/g,ra=/\+/g,uu=/%5B/g,fu=/%5D/g,oa=/%5E/g,du=/%60/g,ia=/%7B/g,hu=/%7C/g,aa=/%7D/g,pu=/%20/g;function Br(e){return encodeURI(""+e).replace(hu,"|").replace(uu,"[").replace(fu,"]")}function mu(e){return Br(e).replace(ia,"{").replace(aa,"}").replace(oa,"^")}function ar(e){return Br(e).replace(ra,"%2B").replace(pu,"+").replace(sa,"%23").replace(iu,"%26").replace(du,"`").replace(ia,"{").replace(aa,"}").replace(oa,"^")}function gu(e){return ar(e).replace(lu,"%3D")}function vu(e){return Br(e).replace(sa,"%23").replace(cu,"%3F")}function _u(e){return e==null?"":vu(e).replace(au,"%2F")}function zn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const yu=/\/$/,bu=e=>e.replace(yu,"");function Js(e,t,n="/"){let s,r={},o="",i="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,a>-1?a:t.length),r=e(o)),a>-1&&(s=s||t.slice(0,a),i=t.slice(a,t.length)),s=ku(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:zn(i)}}function wu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function So(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function xu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&ln(t.matched[s],n.matched[r])&&la(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function ln(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function la(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Su(e[n],t[n]))return!1;return!0}function Su(e,t){return ct(e)?ko(e,t):ct(t)?ko(t,e):e===t}function ko(e,t){return ct(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function ku(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,a;for(i=0;i<s.length;i++)if(a=s[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Bt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Dn;(function(e){e.pop="pop",e.push="push"})(Dn||(Dn={}));var An;(function(e){e.back="back",e.forward="forward",e.unknown=""})(An||(An={}));function Eu(e){if(!e)if(Qt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),bu(e)}const Cu=/^[^#]+#/;function Au(e,t){return e.replace(Cu,"#")+t}function Ou(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Cs=()=>({left:window.scrollX,top:window.scrollY});function Iu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Ou(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Eo(e,t){return(history.state?history.state.position-t:-1)+e}const lr=new Map;function Bu(e,t){lr.set(e,t)}function Tu(e){const t=lr.get(e);return lr.delete(e),t}let Pu=()=>location.protocol+"//"+location.host;function ca(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let a=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(a);return c[0]!=="/"&&(c="/"+c),So(c,"")}return So(n,e)+s+r}function Ru(e,t,n,s){let r=[],o=[],i=null;const a=({state:g})=>{const h=ca(e,location),_=n.value,m=t.value;let w=0;if(g){if(n.value=h,t.value=g,i&&i===_){i=null;return}w=m?g.position-m.position:0}else s(h);r.forEach(b=>{b(n.value,_,{delta:w,type:Dn.pop,direction:w?w>0?An.forward:An.back:An.unknown})})};function c(){i=n.value}function d(g){r.push(g);const h=()=>{const _=r.indexOf(g);_>-1&&r.splice(_,1)};return o.push(h),h}function u(){const{history:g}=window;g.state&&g.replaceState(Ee({},g.state,{scroll:Cs()}),"")}function f(){for(const g of o)g();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:d,destroy:f}}function Co(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Cs():null}}function $u(e){const{history:t,location:n}=window,s={value:ca(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,d,u){const f=e.indexOf("#"),g=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+c:Pu()+e+c;try{t[u?"replaceState":"pushState"](d,"",g),r.value=d}catch(h){console.error(h),n[u?"replace":"assign"](g)}}function i(c,d){const u=Ee({},t.state,Co(r.value.back,c,r.value.forward,!0),d,{position:r.value.position});o(c,u,!0),s.value=c}function a(c,d){const u=Ee({},r.value,t.state,{forward:c,scroll:Cs()});o(u.current,u,!0);const f=Ee({},Co(s.value,c,null),{position:u.position+1},d);o(c,f,!1),s.value=c}return{location:s,state:r,push:a,replace:i}}function zu(e){e=Eu(e);const t=$u(e),n=Ru(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Ee({location:"",base:e,go:s,createHref:Au.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Du(e){return typeof e=="string"||e&&typeof e=="object"}function ua(e){return typeof e=="string"||typeof e=="symbol"}const fa=Symbol("");var Ao;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ao||(Ao={}));function cn(e,t){return Ee(new Error,{type:e,[fa]:!0},t)}function bt(e,t){return e instanceof Error&&fa in e&&(t==null||!!(e.type&t))}const Oo="[^/]+?",Mu={sensitive:!1,strict:!1,start:!0,end:!0},Lu=/[.+*?^${}()[\]/\\]/g;function Fu(e,t){const n=Ee({},Mu,t),s=[];let r=n.start?"^":"";const o=[];for(const d of e){const u=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let f=0;f<d.length;f++){const g=d[f];let h=40+(n.sensitive?.25:0);if(g.type===0)f||(r+="/"),r+=g.value.replace(Lu,"\\$&"),h+=40;else if(g.type===1){const{value:_,repeatable:m,optional:w,regexp:b}=g;o.push({name:_,repeatable:m,optional:w});const x=b||Oo;if(x!==Oo){h+=10;try{new RegExp(`(${x})`)}catch(B){throw new Error(`Invalid custom RegExp for param "${_}" (${x}): `+B.message)}}let k=m?`((?:${x})(?:/(?:${x}))*)`:`(${x})`;f||(k=w&&d.length<2?`(?:/${k})`:"/"+k),w&&(k+="?"),r+=k,h+=20,w&&(h+=-8),m&&(h+=-20),x===".*"&&(h+=-50)}u.push(h)}s.push(u)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function a(d){const u=d.match(i),f={};if(!u)return null;for(let g=1;g<u.length;g++){const h=u[g]||"",_=o[g-1];f[_.name]=h&&_.repeatable?h.split("/"):h}return f}function c(d){let u="",f=!1;for(const g of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const h of g)if(h.type===0)u+=h.value;else if(h.type===1){const{value:_,repeatable:m,optional:w}=h,b=_ in d?d[_]:"";if(ct(b)&&!m)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const x=ct(b)?b.join("/"):b;if(!x)if(w)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${_}"`);u+=x}}return u||"/"}return{re:i,score:s,keys:o,parse:a,stringify:c}}function Nu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function da(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Nu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Io(s))return 1;if(Io(r))return-1}return r.length-s.length}function Io(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ju={type:0,value:""},Uu=/[a-zA-Z0-9_]/;function Gu(e){if(!e)return[[]];if(e==="/")return[[ju]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${d}": ${h}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let a=0,c,d="",u="";function f(){d&&(n===0?o.push({type:0,value:d}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:d,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(d&&f(),i()):c===":"?(f(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:Uu.test(c)?g():(f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),i(),r}function Ju(e,t,n){const s=Fu(Gu(e.path),n),r=Ee(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Hu(e,t){const n=[],s=new Map;t=Ro({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,g,h){const _=!h,m=To(f);m.aliasOf=h&&h.record;const w=Ro(t,f),b=[m];if("alias"in f){const B=typeof f.alias=="string"?[f.alias]:f.alias;for(const P of B)b.push(To(Ee({},m,{components:h?h.record.components:m.components,path:P,aliasOf:h?h.record:m})))}let x,k;for(const B of b){const{path:P}=B;if(g&&P[0]!=="/"){const G=g.record.path,N=G[G.length-1]==="/"?"":"/";B.path=g.record.path+(P&&N+P)}if(x=Ju(B,g,w),h?h.alias.push(x):(k=k||x,k!==x&&k.alias.push(x),_&&f.name&&!Po(x)&&i(f.name)),ha(x)&&c(x),m.children){const G=m.children;for(let N=0;N<G.length;N++)o(G[N],x,h&&h.children[N])}h=h||x}return k?()=>{i(k)}:Cn}function i(f){if(ua(f)){const g=s.get(f);g&&(s.delete(f),n.splice(n.indexOf(g),1),g.children.forEach(i),g.alias.forEach(i))}else{const g=n.indexOf(f);g>-1&&(n.splice(g,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function c(f){const g=qu(f,n);n.splice(g,0,f),f.record.name&&!Po(f)&&s.set(f.record.name,f)}function d(f,g){let h,_={},m,w;if("name"in f&&f.name){if(h=s.get(f.name),!h)throw cn(1,{location:f});w=h.record.name,_=Ee(Bo(g.params,h.keys.filter(k=>!k.optional).concat(h.parent?h.parent.keys.filter(k=>k.optional):[]).map(k=>k.name)),f.params&&Bo(f.params,h.keys.map(k=>k.name))),m=h.stringify(_)}else if(f.path!=null)m=f.path,h=n.find(k=>k.re.test(m)),h&&(_=h.parse(m),w=h.record.name);else{if(h=g.name?s.get(g.name):n.find(k=>k.re.test(g.path)),!h)throw cn(1,{location:f,currentLocation:g});w=h.record.name,_=Ee({},g.params,f.params),m=h.stringify(_)}const b=[];let x=h;for(;x;)b.unshift(x.record),x=x.parent;return{name:w,path:m,params:_,matched:b,meta:Vu(b)}}e.forEach(f=>o(f));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:d,removeRoute:i,clearRoutes:u,getRoutes:a,getRecordMatcher:r}}function Bo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function To(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Wu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Wu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Po(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Vu(e){return e.reduce((t,n)=>Ee(t,n.meta),{})}function Ro(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function qu(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;da(e,t[o])<0?s=o:n=o+1}const r=Ku(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Ku(e){let t=e;for(;t=t.parent;)if(ha(t)&&da(e,t)===0)return t}function ha({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Zu(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(ra," "),i=o.indexOf("="),a=zn(i<0?o:o.slice(0,i)),c=i<0?null:zn(o.slice(i+1));if(a in t){let d=t[a];ct(d)||(d=t[a]=[d]),d.push(c)}else t[a]=c}return t}function $o(e){let t="";for(let n in e){const s=e[n];if(n=gu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ct(s)?s.map(o=>o&&ar(o)):[s&&ar(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Yu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ct(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Xu=Symbol(""),zo=Symbol(""),As=Symbol(""),Tr=Symbol(""),cr=Symbol("");function mn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function $t(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((a,c)=>{const d=g=>{g===!1?c(cn(4,{from:n,to:t})):g instanceof Error?c(g):Du(g)?c(cn(2,{from:t,to:g})):(i&&s.enterCallbacks[r]===i&&typeof g=="function"&&i.push(g),a())},u=o(()=>e.call(s&&s.instances[r],t,n,d));let f=Promise.resolve(u);e.length<3&&(f=f.then(d)),f.catch(g=>c(g))})}function Hs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const a in i.components){let c=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(na(c)){const u=(c.__vccOpts||c)[t];u&&o.push($t(u,n,s,i,a,r))}else{let d=c();o.push(()=>d.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=ou(u)?u.default:u;i.mods[a]=u,i.components[a]=f;const h=(f.__vccOpts||f)[t];return h&&$t(h,n,s,i,a,r)()}))}}return o}function Do(e){const t=at(As),n=at(Tr),s=Je(()=>{const c=Dt(e.to);return t.resolve(c)}),r=Je(()=>{const{matched:c}=s.value,{length:d}=c,u=c[d-1],f=n.matched;if(!u||!f.length)return-1;const g=f.findIndex(ln.bind(null,u));if(g>-1)return g;const h=Mo(c[d-2]);return d>1&&Mo(u)===h&&f[f.length-1].path!==h?f.findIndex(ln.bind(null,c[d-2])):g}),o=Je(()=>r.value>-1&&sf(n.params,s.value.params)),i=Je(()=>r.value>-1&&r.value===n.matched.length-1&&la(n.params,s.value.params));function a(c={}){if(nf(c)){const d=t[Dt(e.replace)?"replace":"push"](Dt(e.to)).catch(Cn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Je(()=>s.value.href),isActive:o,isExactActive:i,navigate:a}}function Qu(e){return e.length===1?e[0]:e}const ef=je({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Do,setup(e,{slots:t}){const n=ys(Do(e)),{options:s}=at(As),r=Je(()=>({[Lo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Lo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Qu(t.default(n));return e.custom?o:nt("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),tf=ef;function nf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function sf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!ct(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Mo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Lo=(e,t,n)=>e??t??n,rf=je({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=at(cr),r=Je(()=>e.route||s.value),o=at(zo,0),i=Je(()=>{let d=Dt(o);const{matched:u}=r.value;let f;for(;(f=u[d])&&!f.components;)d++;return d}),a=Je(()=>r.value.matched[i.value]);Qn(zo,Je(()=>i.value+1)),Qn(Xu,a),Qn(cr,r);const c=be();return on(()=>[c.value,a.value,e.name],([d,u,f],[g,h,_])=>{u&&(u.instances[f]=d,h&&h!==u&&d&&d===g&&(u.leaveGuards.size||(u.leaveGuards=h.leaveGuards),u.updateGuards.size||(u.updateGuards=h.updateGuards))),d&&u&&(!h||!ln(u,h)||!g)&&(u.enterCallbacks[f]||[]).forEach(m=>m(d))},{flush:"post"}),()=>{const d=r.value,u=e.name,f=a.value,g=f&&f.components[u];if(!g)return Fo(n.default,{Component:g,route:d});const h=f.props[u],_=h?h===!0?d.params:typeof h=="function"?h(d):h:null,w=nt(g,Ee({},_,t,{onVnodeUnmounted:b=>{b.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return Fo(n.default,{Component:w,route:d})||w}}});function Fo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const of=rf;function af(e){const t=Hu(e.routes,e),n=e.parseQuery||Zu,s=e.stringifyQuery||$o,r=e.history,o=mn(),i=mn(),a=mn(),c=fl(Bt);let d=Bt;Qt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Gs.bind(null,W=>""+W),f=Gs.bind(null,_u),g=Gs.bind(null,zn);function h(W,X){let V,se;return ua(W)?(V=t.getRecordMatcher(W),se=X):se=W,t.addRoute(se,V)}function _(W){const X=t.getRecordMatcher(W);X&&t.removeRoute(X)}function m(){return t.getRoutes().map(W=>W.record)}function w(W){return!!t.getRecordMatcher(W)}function b(W,X){if(X=Ee({},X||c.value),typeof W=="string"){const l=Js(n,W,X.path),A=t.resolve({path:l.path},X),T=r.createHref(l.fullPath);return Ee(l,A,{params:g(A.params),hash:zn(l.hash),redirectedFrom:void 0,href:T})}let V;if(W.path!=null)V=Ee({},W,{path:Js(n,W.path,X.path).path});else{const l=Ee({},W.params);for(const A in l)l[A]==null&&delete l[A];V=Ee({},W,{params:f(l)}),X.params=f(X.params)}const se=t.resolve(V,X),fe=W.hash||"";se.params=u(g(se.params));const ve=wu(s,Ee({},W,{hash:mu(fe),path:se.path})),S=r.createHref(ve);return Ee({fullPath:ve,hash:fe,query:s===$o?Yu(W.query):W.query||{}},se,{redirectedFrom:void 0,href:S})}function x(W){return typeof W=="string"?Js(n,W,c.value.path):Ee({},W)}function k(W,X){if(d!==W)return cn(8,{from:X,to:W})}function B(W){return N(W)}function P(W){return B(Ee(x(W),{replace:!0}))}function G(W){const X=W.matched[W.matched.length-1];if(X&&X.redirect){const{redirect:V}=X;let se=typeof V=="function"?V(W):V;return typeof se=="string"&&(se=se.includes("?")||se.includes("#")?se=x(se):{path:se},se.params={}),Ee({query:W.query,hash:W.hash,params:se.path!=null?{}:W.params},se)}}function N(W,X){const V=d=b(W),se=c.value,fe=W.state,ve=W.force,S=W.replace===!0,l=G(V);if(l)return N(Ee(x(l),{state:typeof l=="object"?Ee({},fe,l.state):fe,force:ve,replace:S}),X||V);const A=V;A.redirectedFrom=X;let T;return!ve&&xu(s,se,V)&&(T=cn(16,{to:A,from:se}),F(se,se,!0,!1)),(T?Promise.resolve(T):q(A,se)).catch(y=>bt(y)?bt(y,2)?y:oe(y):ee(y,A,se)).then(y=>{if(y){if(bt(y,2))return N(Ee({replace:S},x(y.to),{state:typeof y.to=="object"?Ee({},fe,y.to.state):fe,force:ve}),X||A)}else y=C(A,se,!0,S,fe);return ie(A,se,y),y})}function Z(W,X){const V=k(W,X);return V?Promise.reject(V):Promise.resolve()}function M(W){const X=ae.values().next().value;return X&&typeof X.runWithContext=="function"?X.runWithContext(W):W()}function q(W,X){let V;const[se,fe,ve]=lf(W,X);V=Hs(se.reverse(),"beforeRouteLeave",W,X);for(const l of se)l.leaveGuards.forEach(A=>{V.push($t(A,W,X))});const S=Z.bind(null,W,X);return V.push(S),ge(V).then(()=>{V=[];for(const l of o.list())V.push($t(l,W,X));return V.push(S),ge(V)}).then(()=>{V=Hs(fe,"beforeRouteUpdate",W,X);for(const l of fe)l.updateGuards.forEach(A=>{V.push($t(A,W,X))});return V.push(S),ge(V)}).then(()=>{V=[];for(const l of ve)if(l.beforeEnter)if(ct(l.beforeEnter))for(const A of l.beforeEnter)V.push($t(A,W,X));else V.push($t(l.beforeEnter,W,X));return V.push(S),ge(V)}).then(()=>(W.matched.forEach(l=>l.enterCallbacks={}),V=Hs(ve,"beforeRouteEnter",W,X,M),V.push(S),ge(V))).then(()=>{V=[];for(const l of i.list())V.push($t(l,W,X));return V.push(S),ge(V)}).catch(l=>bt(l,8)?l:Promise.reject(l))}function ie(W,X,V){a.list().forEach(se=>M(()=>se(W,X,V)))}function C(W,X,V,se,fe){const ve=k(W,X);if(ve)return ve;const S=X===Bt,l=Qt?history.state:{};V&&(se||S?r.replace(W.fullPath,Ee({scroll:S&&l&&l.scroll},fe)):r.push(W.fullPath,fe)),c.value=W,F(W,X,V,S),oe()}let j;function p(){j||(j=r.listen((W,X,V)=>{if(!te.listening)return;const se=b(W),fe=G(se);if(fe){N(Ee(fe,{replace:!0,force:!0}),se).catch(Cn);return}d=se;const ve=c.value;Qt&&Bu(Eo(ve.fullPath,V.delta),Cs()),q(se,ve).catch(S=>bt(S,12)?S:bt(S,2)?(N(Ee(x(S.to),{force:!0}),se).then(l=>{bt(l,20)&&!V.delta&&V.type===Dn.pop&&r.go(-1,!1)}).catch(Cn),Promise.reject()):(V.delta&&r.go(-V.delta,!1),ee(S,se,ve))).then(S=>{S=S||C(se,ve,!1),S&&(V.delta&&!bt(S,8)?r.go(-V.delta,!1):V.type===Dn.pop&&bt(S,20)&&r.go(-1,!1)),ie(se,ve,S)}).catch(Cn)}))}let R=mn(),Y=mn(),D;function ee(W,X,V){oe(W);const se=Y.list();return se.length?se.forEach(fe=>fe(W,X,V)):console.error(W),Promise.reject(W)}function K(){return D&&c.value!==Bt?Promise.resolve():new Promise((W,X)=>{R.add([W,X])})}function oe(W){return D||(D=!W,p(),R.list().forEach(([X,V])=>W?V(W):X()),R.reset()),W}function F(W,X,V,se){const{scrollBehavior:fe}=e;if(!Qt||!fe)return Promise.resolve();const ve=!V&&Tu(Eo(W.fullPath,0))||(se||!V)&&history.state&&history.state.scroll||null;return xr().then(()=>fe(W,X,ve)).then(S=>S&&Iu(S)).catch(S=>ee(S,W,X))}const L=W=>r.go(W);let le;const ae=new Set,te={currentRoute:c,listening:!0,addRoute:h,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:m,resolve:b,options:e,push:B,replace:P,go:L,back:()=>L(-1),forward:()=>L(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:Y.add,isReady:K,install(W){const X=this;W.component("RouterLink",tf),W.component("RouterView",of),W.config.globalProperties.$router=X,Object.defineProperty(W.config.globalProperties,"$route",{enumerable:!0,get:()=>Dt(c)}),Qt&&!le&&c.value===Bt&&(le=!0,B(r.location).catch(fe=>{}));const V={};for(const fe in Bt)Object.defineProperty(V,fe,{get:()=>c.value[fe],enumerable:!0});W.provide(As,X),W.provide(Tr,li(V)),W.provide(cr,c);const se=W.unmount;ae.add(W),W.unmount=function(){ae.delete(W),ae.size<1&&(d=Bt,j&&j(),j=null,c.value=Bt,le=!1,D=!1),se()}}};function ge(W){return W.reduce((X,V)=>X.then(()=>M(V)),Promise.resolve())}return te}function lf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(d=>ln(d,a))?s.push(a):n.push(a));const c=e.matched[i];c&&(t.matched.find(d=>ln(d,c))||r.push(c))}return[n,s,r]}function Pr(){return at(As)}function cf(e){return at(Tr)}const uf={class:"glass-effect border-b border-white/10 sticky top-0 z-40"},ff={class:"max-w-6xl mx-auto px-4 py-3"},df={class:"flex items-center justify-between"},hf={class:"hidden md:flex items-center gap-6"},pf={key:0,class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},mf={key:1,class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gf={key:0,class:"md:hidden mt-4 pb-4 border-t border-white/10 pt-4"},vf={class:"flex flex-col gap-2"},_f=je({__name:"AppNavigation",setup(e){const t=cf(),n=be(!1),s=Je(()=>t.name==="GamePlay"?!!sessionStorage.getItem("selectedGameId"):!1);function r(){n.value=!n.value}function o(){n.value=!1}return(i,a)=>{const c=un("router-link");return vi((ne(),re("nav",uf,[O("div",ff,[O("div",df,[_e(c,{to:"/",class:"flex items-center gap-2 text-xl font-bold text-gradient hover:scale-105 transition-transform"},{default:qe(()=>a[0]||(a[0]=[O("span",{class:"text-2xl"},"🎮",-1),we(" 游戏模拟器 ",-1)])),_:1,__:[0]}),O("div",hf,[_e(c,{to:"/",class:Le(["nav-link",{"nav-link-active":i.$route.name==="Home"}])},{default:qe(()=>a[1]||(a[1]=[we(" 首页 ",-1)])),_:1,__:[1]},8,["class"]),_e(c,{to:"/library",class:Le(["nav-link",{"nav-link-active":i.$route.name==="GameLibrary"}])},{default:qe(()=>a[2]||(a[2]=[we(" 游戏库 ",-1)])),_:1,__:[2]},8,["class"]),_e(c,{to:"/about",class:Le(["nav-link",{"nav-link-active":i.$route.name==="About"}])},{default:qe(()=>a[3]||(a[3]=[we(" 关于 ",-1)])),_:1,__:[3]},8,["class"])]),O("button",{onClick:r,class:"md:hidden p-2 rounded-lg hover:bg-white/10 transition-colors"},[n.value?(ne(),re("svg",mf,a[5]||(a[5]=[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))):(ne(),re("svg",pf,a[4]||(a[4]=[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},null,-1)])))])]),n.value?(ne(),re("div",gf,[O("div",vf,[_e(c,{to:"/",class:Le(["mobile-nav-link",{"mobile-nav-link-active":i.$route.name==="Home"}]),onClick:o},{default:qe(()=>a[6]||(a[6]=[we(" 🏠 首页 ",-1)])),_:1,__:[6]},8,["class"]),_e(c,{to:"/library",class:Le(["mobile-nav-link",{"mobile-nav-link-active":i.$route.name==="GameLibrary"}]),onClick:o},{default:qe(()=>a[7]||(a[7]=[we(" 🎮 游戏库 ",-1)])),_:1,__:[7]},8,["class"]),_e(c,{to:"/about",class:Le(["mobile-nav-link",{"mobile-nav-link-active":i.$route.name==="About"}]),onClick:o},{default:qe(()=>a[8]||(a[8]=[we(" 📖 关于 ",-1)])),_:1,__:[8]},8,["class"])])])):Se("",!0)])],512)),[[Dc,!s.value]])}}});const Fn=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},yf=Fn(_f,[["__scopeId","data-v-2df46798"]]),bf={class:"min-h-screen flex flex-col mobile-safe-area"},wf={class:"flex-1"},xf=je({__name:"App",setup(e){return(t,n)=>{const s=un("router-view");return ne(),re("div",bf,[_e(yf),O("main",wf,[_e(s)])])}}});const Sf={class:"flex flex-col min-h-screen"},kf={class:"flex flex-col items-center justify-center flex-1 px-4 py-16 text-center"},Ef={class:"max-w-4xl mx-auto space-y-8"},Cf={class:"flex flex-col items-center justify-center gap-4 mt-12 sm:flex-row"},Af=je({__name:"Home",setup(e){return(t,n)=>{const s=un("router-link");return ne(),re("div",Sf,[O("section",kf,[O("div",Ef,[n[2]||(n[2]=an('<h1 class="flex items-center justify-center gap-4 text-5xl font-bold md:text-7xl text-gradient animate-float" data-v-a9c67438><span data-v-a9c67438>🎮</span> 游戏模拟器 </h1><p class="max-w-2xl mx-auto text-xl leading-relaxed text-gray-300 md:text-2xl" data-v-a9c67438> 在浏览器中畅玩经典游戏！支持 Game Boy、GBA、NES、SNES 等多种游戏机 </p><div class="grid grid-cols-1 gap-6 mt-12 md:grid-cols-3" data-v-a9c67438><div class="p-6 text-center card" data-v-a9c67438><div class="mb-4 text-4xl" data-v-a9c67438>🚀</div><h3 class="mb-2 text-lg font-semibold" data-v-a9c67438>即开即玩</h3><p class="text-sm text-gray-400" data-v-a9c67438> 无需下载安装，直接在浏览器中运行 </p></div><div class="p-6 text-center card" data-v-a9c67438><div class="mb-4 text-4xl" data-v-a9c67438>🎯</div><h3 class="mb-2 text-lg font-semibold" data-v-a9c67438>多平台支持</h3><p class="text-sm text-gray-400" data-v-a9c67438>支持多种经典游戏机平台和格式</p></div><div class="p-6 text-center card" data-v-a9c67438><div class="mb-4 text-4xl" data-v-a9c67438>📱</div><h3 class="mb-2 text-lg font-semibold" data-v-a9c67438>移动端优化</h3><p class="text-sm text-gray-400" data-v-a9c67438>完美适配手机和平板设备</p></div></div>',3)),O("div",Cf,[_e(s,{to:"/library",class:"px-8 py-4 text-lg btn-primary animate-bounce-slow hover:animate-none"},{default:qe(()=>n[0]||(n[0]=[O("span",null,"🎮",-1),we(" 开始游戏 ",-1)])),_:1,__:[0]}),_e(s,{to:"/about",class:"px-8 py-4 text-lg btn-secondary"},{default:qe(()=>n[1]||(n[1]=[we(" 了解更多 ",-1)])),_:1,__:[1]})])])]),n[3]||(n[3]=an('<section class="py-8 border-t bg-black/20 backdrop-blur-sm border-white/10" data-v-a9c67438><div class="max-w-4xl px-4 mx-auto text-center" data-v-a9c67438><h2 class="mb-4 text-2xl font-bold" data-v-a9c67438>快速开始</h2><p class="mb-6 text-gray-300" data-v-a9c67438> 上传你的游戏文件或选择内置游戏，立即开始游戏体验 </p><div class="flex flex-wrap justify-center gap-4 text-sm" data-v-a9c67438><span class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300" data-v-a9c67438> .gb / .gbc </span><span class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300" data-v-a9c67438> .gba </span><span class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300" data-v-a9c67438> .nes </span><span class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300" data-v-a9c67438> .snes / .smc </span><span class="px-3 py-1 rounded-full bg-primary-500/20 text-primary-300" data-v-a9c67438> .zip </span></div></div></section>',1))])}}});const Of=Fn(Af,[["__scopeId","data-v-a9c67438"]]),If="/images/game-icons/mario.png",Bf="modulepreload",Tf=function(e){return"/"+e},No={},pa=function(t,n,s){if(!n||n.length===0)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=Tf(o),o in No)return;No[o]=!0;const i=o.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!s)for(let u=r.length-1;u>=0;u--){const f=r[u];if(f.href===o&&(!i||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${a}`))return;const d=document.createElement("link");if(d.rel=i?"stylesheet":Bf,i||(d.as="script",d.crossOrigin=""),d.href=o,document.head.appendChild(d),i)return new Promise((u,f)=>{d.addEventListener("load",u),d.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t()).catch(o=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=o,window.dispatchEvent(i),!i.defaultPrevented)throw o})},Os=[{id:"gambatte",name:"Game Boy / Game Boy Color",extensions:["gb","gbc"],description:"任天堂Game Boy和Game Boy Color模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/gambatte-wasm.data"},{id:"mgba",name:"Game Boy Advance",extensions:["gba"],description:"任天堂Game Boy Advance模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/mgba-wasm.data"},{id:"snes9x",name:"Super Nintendo",extensions:["smc","sfc","swc"],description:"超级任天堂娱乐系统模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/snes9x-wasm.data"},{id:"fceumm",name:"Nintendo Entertainment System",extensions:["nes"],description:"任天堂红白机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/fceumm-wasm.data"},{id:"genesis_plus_gx",name:"Sega Genesis / Mega Drive",extensions:["gen","bin"],description:"世嘉Genesis和Mega Drive模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/genesis_plus_gx-wasm.data"},{id:"smsplus",name:"Sega Master System",extensions:["sms"],description:"世嘉Master System模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/smsplus-wasm.data"},{id:"stella2014",name:"Atari 2600",extensions:["a26","bin"],description:"Atari 2600游戏机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/stella2014-wasm.data"},{id:"a5200",name:"Atari 5200",extensions:["a52","bin"],description:"Atari 5200游戏机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/a5200-wasm.data"},{id:"desmume2015",name:"Nintendo DS",extensions:["nds"],description:"任天堂DS掌机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/desmume2015-wasm.data"},{id:"mupen64plus_next",name:"Nintendo 64",extensions:["n64","v64","z64"],description:"任天堂64游戏机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/mupen64plus_next-wasm.data"},{id:"mednafen_psx_hw",name:"Sony PlayStation",extensions:["bin","cue","img","mdf","pbp","toc","cbn","m3u"],description:"索尼PlayStation游戏机模拟器",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/mednafen_psx_hw-wasm.data"},{id:"melonds",name:"Nintendo DS (MelonDS)",extensions:["nds"],description:"Nintendo DS模拟器 - MelonDS核心",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/melonds-wasm.data"},{id:"desmume",name:"Nintendo DS (DeSmuME)",extensions:["nds"],description:"Nintendo DS模拟器 - DeSmuME核心",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/desmume-wasm.data"},{id:"fbneo",name:"Arcade (FBNeo)",extensions:["zip","neo","mvs"],description:"FinalBurn Neo街机模拟器 - 支持拳皇97等经典街机游戏",cdnUrl:"https://cdn.emulatorjs.org/latest/data/cores/fbneo-legacy-wasm.data"}];function Pf(e){const t=e.toLowerCase().replace(".","");return Os.find(n=>n.extensions.includes(t))||null}function Rf(e){const n={nds:["melonds","desmume"],gb:["gambatte"],gbc:["gambatte"],gba:["mgba"],nes:["fceumm"],snes:["snes9x"],md:["genesis_plus_gx"],n64:["mupen64plus_next"],psx:["mednafen_psx_hw"],arcade:["fbneo"],neogeo:["fbneo"]}[e.toLowerCase()];return n&&n.length>0&&Os.find(s=>s.id===n[0])||null}function ma(){return Os.flatMap(e=>e.extensions)}var Kn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function $f(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Zn(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var ga={exports:{}};/*!

JSZip v3.10.1 - A JavaScript class for generating and reading zip files
<http://stuartk.com/jszip>

(c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.

JSZip uses the library pako released under the MIT license :
https://github.com/nodeca/pako/blob/main/LICENSE
*/(function(e,t){(function(n){e.exports=n()})(function(){return function n(s,r,o){function i(d,u){if(!r[d]){if(!s[d]){var f=typeof Zn=="function"&&Zn;if(!u&&f)return f(d,!0);if(a)return a(d,!0);var g=new Error("Cannot find module '"+d+"'");throw g.code="MODULE_NOT_FOUND",g}var h=r[d]={exports:{}};s[d][0].call(h.exports,function(_){var m=s[d][1][_];return i(m||_)},h,h.exports,n,s,r,o)}return r[d].exports}for(var a=typeof Zn=="function"&&Zn,c=0;c<o.length;c++)i(o[c]);return i}({1:[function(n,s,r){var o=n("./utils"),i=n("./support"),a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(c){for(var d,u,f,g,h,_,m,w=[],b=0,x=c.length,k=x,B=o.getTypeOf(c)!=="string";b<c.length;)k=x-b,f=B?(d=c[b++],u=b<x?c[b++]:0,b<x?c[b++]:0):(d=c.charCodeAt(b++),u=b<x?c.charCodeAt(b++):0,b<x?c.charCodeAt(b++):0),g=d>>2,h=(3&d)<<4|u>>4,_=1<k?(15&u)<<2|f>>6:64,m=2<k?63&f:64,w.push(a.charAt(g)+a.charAt(h)+a.charAt(_)+a.charAt(m));return w.join("")},r.decode=function(c){var d,u,f,g,h,_,m=0,w=0,b="data:";if(c.substr(0,b.length)===b)throw new Error("Invalid base64 input, it looks like a data url.");var x,k=3*(c=c.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(c.charAt(c.length-1)===a.charAt(64)&&k--,c.charAt(c.length-2)===a.charAt(64)&&k--,k%1!=0)throw new Error("Invalid base64 input, bad content length.");for(x=i.uint8array?new Uint8Array(0|k):new Array(0|k);m<c.length;)d=a.indexOf(c.charAt(m++))<<2|(g=a.indexOf(c.charAt(m++)))>>4,u=(15&g)<<4|(h=a.indexOf(c.charAt(m++)))>>2,f=(3&h)<<6|(_=a.indexOf(c.charAt(m++))),x[w++]=d,h!==64&&(x[w++]=u),_!==64&&(x[w++]=f);return x}},{"./support":30,"./utils":32}],2:[function(n,s,r){var o=n("./external"),i=n("./stream/DataWorker"),a=n("./stream/Crc32Probe"),c=n("./stream/DataLengthProbe");function d(u,f,g,h,_){this.compressedSize=u,this.uncompressedSize=f,this.crc32=g,this.compression=h,this.compressedContent=_}d.prototype={getContentWorker:function(){var u=new i(o.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new c("data_length")),f=this;return u.on("end",function(){if(this.streamInfo.data_length!==f.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),u},getCompressedWorker:function(){return new i(o.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},d.createWorkerFrom=function(u,f,g){return u.pipe(new a).pipe(new c("uncompressedSize")).pipe(f.compressWorker(g)).pipe(new c("compressedSize")).withStreamInfo("compression",f)},s.exports=d},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(n,s,r){var o=n("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(){return new o("STORE compression")},uncompressWorker:function(){return new o("STORE decompression")}},r.DEFLATE=n("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(n,s,r){var o=n("./utils"),i=function(){for(var a,c=[],d=0;d<256;d++){a=d;for(var u=0;u<8;u++)a=1&a?3988292384^a>>>1:a>>>1;c[d]=a}return c}();s.exports=function(a,c){return a!==void 0&&a.length?o.getTypeOf(a)!=="string"?function(d,u,f,g){var h=i,_=g+f;d^=-1;for(var m=g;m<_;m++)d=d>>>8^h[255&(d^u[m])];return-1^d}(0|c,a,a.length,0):function(d,u,f,g){var h=i,_=g+f;d^=-1;for(var m=g;m<_;m++)d=d>>>8^h[255&(d^u.charCodeAt(m))];return-1^d}(0|c,a,a.length,0):0}},{"./utils":32}],5:[function(n,s,r){r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(n,s,r){var o=null;o=typeof Promise<"u"?Promise:n("lie"),s.exports={Promise:o}},{lie:37}],7:[function(n,s,r){var o=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Uint32Array<"u",i=n("pako"),a=n("./utils"),c=n("./stream/GenericWorker"),d=o?"uint8array":"array";function u(f,g){c.call(this,"FlateWorker/"+f),this._pako=null,this._pakoAction=f,this._pakoOptions=g,this.meta={}}r.magic="\b\0",a.inherits(u,c),u.prototype.processChunk=function(f){this.meta=f.meta,this._pako===null&&this._createPako(),this._pako.push(a.transformTo(d,f.data),!1)},u.prototype.flush=function(){c.prototype.flush.call(this),this._pako===null&&this._createPako(),this._pako.push([],!0)},u.prototype.cleanUp=function(){c.prototype.cleanUp.call(this),this._pako=null},u.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var f=this;this._pako.onData=function(g){f.push({data:g,meta:f.meta})}},r.compressWorker=function(f){return new u("Deflate",f)},r.uncompressWorker=function(){return new u("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(n,s,r){function o(h,_){var m,w="";for(m=0;m<_;m++)w+=String.fromCharCode(255&h),h>>>=8;return w}function i(h,_,m,w,b,x){var k,B,P=h.file,G=h.compression,N=x!==d.utf8encode,Z=a.transformTo("string",x(P.name)),M=a.transformTo("string",d.utf8encode(P.name)),q=P.comment,ie=a.transformTo("string",x(q)),C=a.transformTo("string",d.utf8encode(q)),j=M.length!==P.name.length,p=C.length!==q.length,R="",Y="",D="",ee=P.dir,K=P.date,oe={crc32:0,compressedSize:0,uncompressedSize:0};_&&!m||(oe.crc32=h.crc32,oe.compressedSize=h.compressedSize,oe.uncompressedSize=h.uncompressedSize);var F=0;_&&(F|=8),N||!j&&!p||(F|=2048);var L=0,le=0;ee&&(L|=16),b==="UNIX"?(le=798,L|=function(te,ge){var W=te;return te||(W=ge?16893:33204),(65535&W)<<16}(P.unixPermissions,ee)):(le=20,L|=function(te){return 63&(te||0)}(P.dosPermissions)),k=K.getUTCHours(),k<<=6,k|=K.getUTCMinutes(),k<<=5,k|=K.getUTCSeconds()/2,B=K.getUTCFullYear()-1980,B<<=4,B|=K.getUTCMonth()+1,B<<=5,B|=K.getUTCDate(),j&&(Y=o(1,1)+o(u(Z),4)+M,R+="up"+o(Y.length,2)+Y),p&&(D=o(1,1)+o(u(ie),4)+C,R+="uc"+o(D.length,2)+D);var ae="";return ae+=`
\0`,ae+=o(F,2),ae+=G.magic,ae+=o(k,2),ae+=o(B,2),ae+=o(oe.crc32,4),ae+=o(oe.compressedSize,4),ae+=o(oe.uncompressedSize,4),ae+=o(Z.length,2),ae+=o(R.length,2),{fileRecord:f.LOCAL_FILE_HEADER+ae+Z+R,dirRecord:f.CENTRAL_FILE_HEADER+o(le,2)+ae+o(ie.length,2)+"\0\0\0\0"+o(L,4)+o(w,4)+Z+R+ie}}var a=n("../utils"),c=n("../stream/GenericWorker"),d=n("../utf8"),u=n("../crc32"),f=n("../signature");function g(h,_,m,w){c.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=_,this.zipPlatform=m,this.encodeFileName=w,this.streamFiles=h,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}a.inherits(g,c),g.prototype.push=function(h){var _=h.meta.percent||0,m=this.entriesCount,w=this._sources.length;this.accumulate?this.contentBuffer.push(h):(this.bytesWritten+=h.data.length,c.prototype.push.call(this,{data:h.data,meta:{currentFile:this.currentFile,percent:m?(_+100*(m-w-1))/m:100}}))},g.prototype.openedSource=function(h){this.currentSourceOffset=this.bytesWritten,this.currentFile=h.file.name;var _=this.streamFiles&&!h.file.dir;if(_){var m=i(h,_,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:m.fileRecord,meta:{percent:0}})}else this.accumulate=!0},g.prototype.closedSource=function(h){this.accumulate=!1;var _=this.streamFiles&&!h.file.dir,m=i(h,_,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(m.dirRecord),_)this.push({data:function(w){return f.DATA_DESCRIPTOR+o(w.crc32,4)+o(w.compressedSize,4)+o(w.uncompressedSize,4)}(h),meta:{percent:100}});else for(this.push({data:m.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},g.prototype.flush=function(){for(var h=this.bytesWritten,_=0;_<this.dirRecords.length;_++)this.push({data:this.dirRecords[_],meta:{percent:100}});var m=this.bytesWritten-h,w=function(b,x,k,B,P){var G=a.transformTo("string",P(B));return f.CENTRAL_DIRECTORY_END+"\0\0\0\0"+o(b,2)+o(b,2)+o(x,4)+o(k,4)+o(G.length,2)+G}(this.dirRecords.length,m,h,this.zipComment,this.encodeFileName);this.push({data:w,meta:{percent:100}})},g.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},g.prototype.registerPrevious=function(h){this._sources.push(h);var _=this;return h.on("data",function(m){_.processChunk(m)}),h.on("end",function(){_.closedSource(_.previous.streamInfo),_._sources.length?_.prepareNextSource():_.end()}),h.on("error",function(m){_.error(m)}),this},g.prototype.resume=function(){return!!c.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},g.prototype.error=function(h){var _=this._sources;if(!c.prototype.error.call(this,h))return!1;for(var m=0;m<_.length;m++)try{_[m].error(h)}catch{}return!0},g.prototype.lock=function(){c.prototype.lock.call(this);for(var h=this._sources,_=0;_<h.length;_++)h[_].lock()},s.exports=g},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(n,s,r){var o=n("../compressions"),i=n("./ZipFileWorker");r.generateWorker=function(a,c,d){var u=new i(c.streamFiles,d,c.platform,c.encodeFileName),f=0;try{a.forEach(function(g,h){f++;var _=function(x,k){var B=x||k,P=o[B];if(!P)throw new Error(B+" is not a valid compression method !");return P}(h.options.compression,c.compression),m=h.options.compressionOptions||c.compressionOptions||{},w=h.dir,b=h.date;h._compressWorker(_,m).withStreamInfo("file",{name:g,dir:w,date:b,comment:h.comment||"",unixPermissions:h.unixPermissions,dosPermissions:h.dosPermissions}).pipe(u)}),u.entriesCount=f}catch(g){u.error(g)}return u}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(n,s,r){function o(){if(!(this instanceof o))return new o;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var i=new o;for(var a in this)typeof this[a]!="function"&&(i[a]=this[a]);return i}}(o.prototype=n("./object")).loadAsync=n("./load"),o.support=n("./support"),o.defaults=n("./defaults"),o.version="3.10.1",o.loadAsync=function(i,a){return new o().loadAsync(i,a)},o.external=n("./external"),s.exports=o},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(n,s,r){var o=n("./utils"),i=n("./external"),a=n("./utf8"),c=n("./zipEntries"),d=n("./stream/Crc32Probe"),u=n("./nodejsUtils");function f(g){return new i.Promise(function(h,_){var m=g.decompressed.getContentWorker().pipe(new d);m.on("error",function(w){_(w)}).on("end",function(){m.streamInfo.crc32!==g.decompressed.crc32?_(new Error("Corrupted zip : CRC32 mismatch")):h()}).resume()})}s.exports=function(g,h){var _=this;return h=o.extend(h||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:a.utf8decode}),u.isNode&&u.isStream(g)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):o.prepareContent("the loaded zip file",g,!0,h.optimizedBinaryString,h.base64).then(function(m){var w=new c(h);return w.load(m),w}).then(function(m){var w=[i.Promise.resolve(m)],b=m.files;if(h.checkCRC32)for(var x=0;x<b.length;x++)w.push(f(b[x]));return i.Promise.all(w)}).then(function(m){for(var w=m.shift(),b=w.files,x=0;x<b.length;x++){var k=b[x],B=k.fileNameStr,P=o.resolve(k.fileNameStr);_.file(P,k.decompressed,{binary:!0,optimizedBinaryString:!0,date:k.date,dir:k.dir,comment:k.fileCommentStr.length?k.fileCommentStr:null,unixPermissions:k.unixPermissions,dosPermissions:k.dosPermissions,createFolders:h.createFolders}),k.dir||(_.file(P).unsafeOriginalName=B)}return w.zipComment.length&&(_.comment=w.zipComment),_})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(n,s,r){var o=n("../utils"),i=n("../stream/GenericWorker");function a(c,d){i.call(this,"Nodejs stream input adapter for "+c),this._upstreamEnded=!1,this._bindStream(d)}o.inherits(a,i),a.prototype._bindStream=function(c){var d=this;(this._stream=c).pause(),c.on("data",function(u){d.push({data:u,meta:{percent:0}})}).on("error",function(u){d.isPaused?this.generatedError=u:d.error(u)}).on("end",function(){d.isPaused?d._upstreamEnded=!0:d.end()})},a.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},s.exports=a},{"../stream/GenericWorker":28,"../utils":32}],13:[function(n,s,r){var o=n("readable-stream").Readable;function i(a,c,d){o.call(this,c),this._helper=a;var u=this;a.on("data",function(f,g){u.push(f)||u._helper.pause(),d&&d(g)}).on("error",function(f){u.emit("error",f)}).on("end",function(){u.push(null)})}n("../utils").inherits(i,o),i.prototype._read=function(){this._helper.resume()},s.exports=i},{"../utils":32,"readable-stream":16}],14:[function(n,s,r){s.exports={isNode:typeof Buffer<"u",newBufferFrom:function(o,i){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(o,i);if(typeof o=="number")throw new Error('The "data" argument must not be a number');return new Buffer(o,i)},allocBuffer:function(o){if(Buffer.alloc)return Buffer.alloc(o);var i=new Buffer(o);return i.fill(0),i},isBuffer:function(o){return Buffer.isBuffer(o)},isStream:function(o){return o&&typeof o.on=="function"&&typeof o.pause=="function"&&typeof o.resume=="function"}}},{}],15:[function(n,s,r){function o(P,G,N){var Z,M=a.getTypeOf(G),q=a.extend(N||{},u);q.date=q.date||new Date,q.compression!==null&&(q.compression=q.compression.toUpperCase()),typeof q.unixPermissions=="string"&&(q.unixPermissions=parseInt(q.unixPermissions,8)),q.unixPermissions&&16384&q.unixPermissions&&(q.dir=!0),q.dosPermissions&&16&q.dosPermissions&&(q.dir=!0),q.dir&&(P=b(P)),q.createFolders&&(Z=w(P))&&x.call(this,Z,!0);var ie=M==="string"&&q.binary===!1&&q.base64===!1;N&&N.binary!==void 0||(q.binary=!ie),(G instanceof f&&G.uncompressedSize===0||q.dir||!G||G.length===0)&&(q.base64=!1,q.binary=!0,G="",q.compression="STORE",M="string");var C=null;C=G instanceof f||G instanceof c?G:_.isNode&&_.isStream(G)?new m(P,G):a.prepareContent(P,G,q.binary,q.optimizedBinaryString,q.base64);var j=new g(P,C,q);this.files[P]=j}var i=n("./utf8"),a=n("./utils"),c=n("./stream/GenericWorker"),d=n("./stream/StreamHelper"),u=n("./defaults"),f=n("./compressedObject"),g=n("./zipObject"),h=n("./generate"),_=n("./nodejsUtils"),m=n("./nodejs/NodejsStreamInputAdapter"),w=function(P){P.slice(-1)==="/"&&(P=P.substring(0,P.length-1));var G=P.lastIndexOf("/");return 0<G?P.substring(0,G):""},b=function(P){return P.slice(-1)!=="/"&&(P+="/"),P},x=function(P,G){return G=G!==void 0?G:u.createFolders,P=b(P),this.files[P]||o.call(this,P,null,{dir:!0,createFolders:G}),this.files[P]};function k(P){return Object.prototype.toString.call(P)==="[object RegExp]"}var B={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(P){var G,N,Z;for(G in this.files)Z=this.files[G],(N=G.slice(this.root.length,G.length))&&G.slice(0,this.root.length)===this.root&&P(N,Z)},filter:function(P){var G=[];return this.forEach(function(N,Z){P(N,Z)&&G.push(Z)}),G},file:function(P,G,N){if(arguments.length!==1)return P=this.root+P,o.call(this,P,G,N),this;if(k(P)){var Z=P;return this.filter(function(q,ie){return!ie.dir&&Z.test(q)})}var M=this.files[this.root+P];return M&&!M.dir?M:null},folder:function(P){if(!P)return this;if(k(P))return this.filter(function(M,q){return q.dir&&P.test(M)});var G=this.root+P,N=x.call(this,G),Z=this.clone();return Z.root=N.name,Z},remove:function(P){P=this.root+P;var G=this.files[P];if(G||(P.slice(-1)!=="/"&&(P+="/"),G=this.files[P]),G&&!G.dir)delete this.files[P];else for(var N=this.filter(function(M,q){return q.name.slice(0,P.length)===P}),Z=0;Z<N.length;Z++)delete this.files[N[Z].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(P){var G,N={};try{if((N=a.extend(P||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:i.utf8encode})).type=N.type.toLowerCase(),N.compression=N.compression.toUpperCase(),N.type==="binarystring"&&(N.type="string"),!N.type)throw new Error("No output type specified.");a.checkSupport(N.type),N.platform!=="darwin"&&N.platform!=="freebsd"&&N.platform!=="linux"&&N.platform!=="sunos"||(N.platform="UNIX"),N.platform==="win32"&&(N.platform="DOS");var Z=N.comment||this.comment||"";G=h.generateWorker(this,N,Z)}catch(M){(G=new c("error")).error(M)}return new d(G,N.type||"string",N.mimeType)},generateAsync:function(P,G){return this.generateInternalStream(P).accumulate(G)},generateNodeStream:function(P,G){return(P=P||{}).type||(P.type="nodebuffer"),this.generateInternalStream(P).toNodejsStream(G)}};s.exports=B},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(n,s,r){s.exports=n("stream")},{stream:void 0}],17:[function(n,s,r){var o=n("./DataReader");function i(a){o.call(this,a);for(var c=0;c<this.data.length;c++)a[c]=255&a[c]}n("../utils").inherits(i,o),i.prototype.byteAt=function(a){return this.data[this.zero+a]},i.prototype.lastIndexOfSignature=function(a){for(var c=a.charCodeAt(0),d=a.charCodeAt(1),u=a.charCodeAt(2),f=a.charCodeAt(3),g=this.length-4;0<=g;--g)if(this.data[g]===c&&this.data[g+1]===d&&this.data[g+2]===u&&this.data[g+3]===f)return g-this.zero;return-1},i.prototype.readAndCheckSignature=function(a){var c=a.charCodeAt(0),d=a.charCodeAt(1),u=a.charCodeAt(2),f=a.charCodeAt(3),g=this.readData(4);return c===g[0]&&d===g[1]&&u===g[2]&&f===g[3]},i.prototype.readData=function(a){if(this.checkOffset(a),a===0)return[];var c=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,c},s.exports=i},{"../utils":32,"./DataReader":18}],18:[function(n,s,r){var o=n("../utils");function i(a){this.data=a,this.length=a.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(a){this.checkIndex(this.index+a)},checkIndex:function(a){if(this.length<this.zero+a||a<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+a+"). Corrupted zip ?")},setIndex:function(a){this.checkIndex(a),this.index=a},skip:function(a){this.setIndex(this.index+a)},byteAt:function(){},readInt:function(a){var c,d=0;for(this.checkOffset(a),c=this.index+a-1;c>=this.index;c--)d=(d<<8)+this.byteAt(c);return this.index+=a,d},readString:function(a){return o.transformTo("string",this.readData(a))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var a=this.readInt(4);return new Date(Date.UTC(1980+(a>>25&127),(a>>21&15)-1,a>>16&31,a>>11&31,a>>5&63,(31&a)<<1))}},s.exports=i},{"../utils":32}],19:[function(n,s,r){var o=n("./Uint8ArrayReader");function i(a){o.call(this,a)}n("../utils").inherits(i,o),i.prototype.readData=function(a){this.checkOffset(a);var c=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,c},s.exports=i},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(n,s,r){var o=n("./DataReader");function i(a){o.call(this,a)}n("../utils").inherits(i,o),i.prototype.byteAt=function(a){return this.data.charCodeAt(this.zero+a)},i.prototype.lastIndexOfSignature=function(a){return this.data.lastIndexOf(a)-this.zero},i.prototype.readAndCheckSignature=function(a){return a===this.readData(4)},i.prototype.readData=function(a){this.checkOffset(a);var c=this.data.slice(this.zero+this.index,this.zero+this.index+a);return this.index+=a,c},s.exports=i},{"../utils":32,"./DataReader":18}],21:[function(n,s,r){var o=n("./ArrayReader");function i(a){o.call(this,a)}n("../utils").inherits(i,o),i.prototype.readData=function(a){if(this.checkOffset(a),a===0)return new Uint8Array(0);var c=this.data.subarray(this.zero+this.index,this.zero+this.index+a);return this.index+=a,c},s.exports=i},{"../utils":32,"./ArrayReader":17}],22:[function(n,s,r){var o=n("../utils"),i=n("../support"),a=n("./ArrayReader"),c=n("./StringReader"),d=n("./NodeBufferReader"),u=n("./Uint8ArrayReader");s.exports=function(f){var g=o.getTypeOf(f);return o.checkSupport(g),g!=="string"||i.uint8array?g==="nodebuffer"?new d(f):i.uint8array?new u(o.transformTo("uint8array",f)):new a(o.transformTo("array",f)):new c(f)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(n,s,r){r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x07",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\x07\b"},{}],24:[function(n,s,r){var o=n("./GenericWorker"),i=n("../utils");function a(c){o.call(this,"ConvertWorker to "+c),this.destType=c}i.inherits(a,o),a.prototype.processChunk=function(c){this.push({data:i.transformTo(this.destType,c.data),meta:c.meta})},s.exports=a},{"../utils":32,"./GenericWorker":28}],25:[function(n,s,r){var o=n("./GenericWorker"),i=n("../crc32");function a(){o.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}n("../utils").inherits(a,o),a.prototype.processChunk=function(c){this.streamInfo.crc32=i(c.data,this.streamInfo.crc32||0),this.push(c)},s.exports=a},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(n,s,r){var o=n("../utils"),i=n("./GenericWorker");function a(c){i.call(this,"DataLengthProbe for "+c),this.propName=c,this.withStreamInfo(c,0)}o.inherits(a,i),a.prototype.processChunk=function(c){if(c){var d=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=d+c.data.length}i.prototype.processChunk.call(this,c)},s.exports=a},{"../utils":32,"./GenericWorker":28}],27:[function(n,s,r){var o=n("../utils"),i=n("./GenericWorker");function a(c){i.call(this,"DataWorker");var d=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,c.then(function(u){d.dataIsReady=!0,d.data=u,d.max=u&&u.length||0,d.type=o.getTypeOf(u),d.isPaused||d._tickAndRepeat()},function(u){d.error(u)})}o.inherits(a,i),a.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,o.delay(this._tickAndRepeat,[],this)),!0)},a.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(o.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},a.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var c=null,d=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":c=this.data.substring(this.index,d);break;case"uint8array":c=this.data.subarray(this.index,d);break;case"array":case"nodebuffer":c=this.data.slice(this.index,d)}return this.index=d,this.push({data:c,meta:{percent:this.max?this.index/this.max*100:0}})},s.exports=a},{"../utils":32,"./GenericWorker":28}],28:[function(n,s,r){function o(i){this.name=i||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}o.prototype={push:function(i){this.emit("data",i)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(i){this.emit("error",i)}return!0},error:function(i){return!this.isFinished&&(this.isPaused?this.generatedError=i:(this.isFinished=!0,this.emit("error",i),this.previous&&this.previous.error(i),this.cleanUp()),!0)},on:function(i,a){return this._listeners[i].push(a),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(i,a){if(this._listeners[i])for(var c=0;c<this._listeners[i].length;c++)this._listeners[i][c].call(this,a)},pipe:function(i){return i.registerPrevious(this)},registerPrevious:function(i){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=i.streamInfo,this.mergeStreamInfo(),this.previous=i;var a=this;return i.on("data",function(c){a.processChunk(c)}),i.on("end",function(){a.end()}),i.on("error",function(c){a.error(c)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var i=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),i=!0),this.previous&&this.previous.resume(),!i},flush:function(){},processChunk:function(i){this.push(i)},withStreamInfo:function(i,a){return this.extraStreamInfo[i]=a,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var i in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,i)&&(this.streamInfo[i]=this.extraStreamInfo[i])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var i="Worker "+this.name;return this.previous?this.previous+" -> "+i:i}},s.exports=o},{}],29:[function(n,s,r){var o=n("../utils"),i=n("./ConvertWorker"),a=n("./GenericWorker"),c=n("../base64"),d=n("../support"),u=n("../external"),f=null;if(d.nodestream)try{f=n("../nodejs/NodejsStreamOutputAdapter")}catch{}function g(_,m){return new u.Promise(function(w,b){var x=[],k=_._internalType,B=_._outputType,P=_._mimeType;_.on("data",function(G,N){x.push(G),m&&m(N)}).on("error",function(G){x=[],b(G)}).on("end",function(){try{var G=function(N,Z,M){switch(N){case"blob":return o.newBlob(o.transformTo("arraybuffer",Z),M);case"base64":return c.encode(Z);default:return o.transformTo(N,Z)}}(B,function(N,Z){var M,q=0,ie=null,C=0;for(M=0;M<Z.length;M++)C+=Z[M].length;switch(N){case"string":return Z.join("");case"array":return Array.prototype.concat.apply([],Z);case"uint8array":for(ie=new Uint8Array(C),M=0;M<Z.length;M++)ie.set(Z[M],q),q+=Z[M].length;return ie;case"nodebuffer":return Buffer.concat(Z);default:throw new Error("concat : unsupported type '"+N+"'")}}(k,x),P);w(G)}catch(N){b(N)}x=[]}).resume()})}function h(_,m,w){var b=m;switch(m){case"blob":case"arraybuffer":b="uint8array";break;case"base64":b="string"}try{this._internalType=b,this._outputType=m,this._mimeType=w,o.checkSupport(b),this._worker=_.pipe(new i(b)),_.lock()}catch(x){this._worker=new a("error"),this._worker.error(x)}}h.prototype={accumulate:function(_){return g(this,_)},on:function(_,m){var w=this;return _==="data"?this._worker.on(_,function(b){m.call(w,b.data,b.meta)}):this._worker.on(_,function(){o.delay(m,arguments,w)}),this},resume:function(){return o.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(_){if(o.checkSupport("nodestream"),this._outputType!=="nodebuffer")throw new Error(this._outputType+" is not supported by this method");return new f(this,{objectMode:this._outputType!=="nodebuffer"},_)}},s.exports=h},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(n,s,r){if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u",r.nodebuffer=typeof Buffer<"u",r.uint8array=typeof Uint8Array<"u",typeof ArrayBuffer>"u")r.blob=!1;else{var o=new ArrayBuffer(0);try{r.blob=new Blob([o],{type:"application/zip"}).size===0}catch{try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(o),r.blob=i.getBlob("application/zip").size===0}catch{r.blob=!1}}}try{r.nodestream=!!n("readable-stream").Readable}catch{r.nodestream=!1}},{"readable-stream":16}],31:[function(n,s,r){for(var o=n("./utils"),i=n("./support"),a=n("./nodejsUtils"),c=n("./stream/GenericWorker"),d=new Array(256),u=0;u<256;u++)d[u]=252<=u?6:248<=u?5:240<=u?4:224<=u?3:192<=u?2:1;d[254]=d[254]=1;function f(){c.call(this,"utf-8 decode"),this.leftOver=null}function g(){c.call(this,"utf-8 encode")}r.utf8encode=function(h){return i.nodebuffer?a.newBufferFrom(h,"utf-8"):function(_){var m,w,b,x,k,B=_.length,P=0;for(x=0;x<B;x++)(64512&(w=_.charCodeAt(x)))==55296&&x+1<B&&(64512&(b=_.charCodeAt(x+1)))==56320&&(w=65536+(w-55296<<10)+(b-56320),x++),P+=w<128?1:w<2048?2:w<65536?3:4;for(m=i.uint8array?new Uint8Array(P):new Array(P),x=k=0;k<P;x++)(64512&(w=_.charCodeAt(x)))==55296&&x+1<B&&(64512&(b=_.charCodeAt(x+1)))==56320&&(w=65536+(w-55296<<10)+(b-56320),x++),w<128?m[k++]=w:(w<2048?m[k++]=192|w>>>6:(w<65536?m[k++]=224|w>>>12:(m[k++]=240|w>>>18,m[k++]=128|w>>>12&63),m[k++]=128|w>>>6&63),m[k++]=128|63&w);return m}(h)},r.utf8decode=function(h){return i.nodebuffer?o.transformTo("nodebuffer",h).toString("utf-8"):function(_){var m,w,b,x,k=_.length,B=new Array(2*k);for(m=w=0;m<k;)if((b=_[m++])<128)B[w++]=b;else if(4<(x=d[b]))B[w++]=65533,m+=x-1;else{for(b&=x===2?31:x===3?15:7;1<x&&m<k;)b=b<<6|63&_[m++],x--;1<x?B[w++]=65533:b<65536?B[w++]=b:(b-=65536,B[w++]=55296|b>>10&1023,B[w++]=56320|1023&b)}return B.length!==w&&(B.subarray?B=B.subarray(0,w):B.length=w),o.applyFromCharCode(B)}(h=o.transformTo(i.uint8array?"uint8array":"array",h))},o.inherits(f,c),f.prototype.processChunk=function(h){var _=o.transformTo(i.uint8array?"uint8array":"array",h.data);if(this.leftOver&&this.leftOver.length){if(i.uint8array){var m=_;(_=new Uint8Array(m.length+this.leftOver.length)).set(this.leftOver,0),_.set(m,this.leftOver.length)}else _=this.leftOver.concat(_);this.leftOver=null}var w=function(x,k){var B;for((k=k||x.length)>x.length&&(k=x.length),B=k-1;0<=B&&(192&x[B])==128;)B--;return B<0||B===0?k:B+d[x[B]]>k?B:k}(_),b=_;w!==_.length&&(i.uint8array?(b=_.subarray(0,w),this.leftOver=_.subarray(w,_.length)):(b=_.slice(0,w),this.leftOver=_.slice(w,_.length))),this.push({data:r.utf8decode(b),meta:h.meta})},f.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=f,o.inherits(g,c),g.prototype.processChunk=function(h){this.push({data:r.utf8encode(h.data),meta:h.meta})},r.Utf8EncodeWorker=g},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(n,s,r){var o=n("./support"),i=n("./base64"),a=n("./nodejsUtils"),c=n("./external");function d(m){return m}function u(m,w){for(var b=0;b<m.length;++b)w[b]=255&m.charCodeAt(b);return w}n("setimmediate"),r.newBlob=function(m,w){r.checkSupport("blob");try{return new Blob([m],{type:w})}catch{try{var b=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return b.append(m),b.getBlob(w)}catch{throw new Error("Bug : can't construct the Blob.")}}};var f={stringifyByChunk:function(m,w,b){var x=[],k=0,B=m.length;if(B<=b)return String.fromCharCode.apply(null,m);for(;k<B;)w==="array"||w==="nodebuffer"?x.push(String.fromCharCode.apply(null,m.slice(k,Math.min(k+b,B)))):x.push(String.fromCharCode.apply(null,m.subarray(k,Math.min(k+b,B)))),k+=b;return x.join("")},stringifyByChar:function(m){for(var w="",b=0;b<m.length;b++)w+=String.fromCharCode(m[b]);return w},applyCanBeUsed:{uint8array:function(){try{return o.uint8array&&String.fromCharCode.apply(null,new Uint8Array(1)).length===1}catch{return!1}}(),nodebuffer:function(){try{return o.nodebuffer&&String.fromCharCode.apply(null,a.allocBuffer(1)).length===1}catch{return!1}}()}};function g(m){var w=65536,b=r.getTypeOf(m),x=!0;if(b==="uint8array"?x=f.applyCanBeUsed.uint8array:b==="nodebuffer"&&(x=f.applyCanBeUsed.nodebuffer),x)for(;1<w;)try{return f.stringifyByChunk(m,b,w)}catch{w=Math.floor(w/2)}return f.stringifyByChar(m)}function h(m,w){for(var b=0;b<m.length;b++)w[b]=m[b];return w}r.applyFromCharCode=g;var _={};_.string={string:d,array:function(m){return u(m,new Array(m.length))},arraybuffer:function(m){return _.string.uint8array(m).buffer},uint8array:function(m){return u(m,new Uint8Array(m.length))},nodebuffer:function(m){return u(m,a.allocBuffer(m.length))}},_.array={string:g,array:d,arraybuffer:function(m){return new Uint8Array(m).buffer},uint8array:function(m){return new Uint8Array(m)},nodebuffer:function(m){return a.newBufferFrom(m)}},_.arraybuffer={string:function(m){return g(new Uint8Array(m))},array:function(m){return h(new Uint8Array(m),new Array(m.byteLength))},arraybuffer:d,uint8array:function(m){return new Uint8Array(m)},nodebuffer:function(m){return a.newBufferFrom(new Uint8Array(m))}},_.uint8array={string:g,array:function(m){return h(m,new Array(m.length))},arraybuffer:function(m){return m.buffer},uint8array:d,nodebuffer:function(m){return a.newBufferFrom(m)}},_.nodebuffer={string:g,array:function(m){return h(m,new Array(m.length))},arraybuffer:function(m){return _.nodebuffer.uint8array(m).buffer},uint8array:function(m){return h(m,new Uint8Array(m.length))},nodebuffer:d},r.transformTo=function(m,w){if(w=w||"",!m)return w;r.checkSupport(m);var b=r.getTypeOf(w);return _[b][m](w)},r.resolve=function(m){for(var w=m.split("/"),b=[],x=0;x<w.length;x++){var k=w[x];k==="."||k===""&&x!==0&&x!==w.length-1||(k===".."?b.pop():b.push(k))}return b.join("/")},r.getTypeOf=function(m){return typeof m=="string"?"string":Object.prototype.toString.call(m)==="[object Array]"?"array":o.nodebuffer&&a.isBuffer(m)?"nodebuffer":o.uint8array&&m instanceof Uint8Array?"uint8array":o.arraybuffer&&m instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(m){if(!o[m.toLowerCase()])throw new Error(m+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(m){var w,b,x="";for(b=0;b<(m||"").length;b++)x+="\\x"+((w=m.charCodeAt(b))<16?"0":"")+w.toString(16).toUpperCase();return x},r.delay=function(m,w,b){setImmediate(function(){m.apply(b||null,w||[])})},r.inherits=function(m,w){function b(){}b.prototype=w.prototype,m.prototype=new b},r.extend=function(){var m,w,b={};for(m=0;m<arguments.length;m++)for(w in arguments[m])Object.prototype.hasOwnProperty.call(arguments[m],w)&&b[w]===void 0&&(b[w]=arguments[m][w]);return b},r.prepareContent=function(m,w,b,x,k){return c.Promise.resolve(w).then(function(B){return o.blob&&(B instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(B))!==-1)&&typeof FileReader<"u"?new c.Promise(function(P,G){var N=new FileReader;N.onload=function(Z){P(Z.target.result)},N.onerror=function(Z){G(Z.target.error)},N.readAsArrayBuffer(B)}):B}).then(function(B){var P=r.getTypeOf(B);return P?(P==="arraybuffer"?B=r.transformTo("uint8array",B):P==="string"&&(k?B=i.decode(B):b&&x!==!0&&(B=function(G){return u(G,o.uint8array?new Uint8Array(G.length):new Array(G.length))}(B))),B):c.Promise.reject(new Error("Can't read the data of '"+m+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(n,s,r){var o=n("./reader/readerFor"),i=n("./utils"),a=n("./signature"),c=n("./zipEntry"),d=n("./support");function u(f){this.files=[],this.loadOptions=f}u.prototype={checkSignature:function(f){if(!this.reader.readAndCheckSignature(f)){this.reader.index-=4;var g=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(g)+", expected "+i.pretty(f)+")")}},isSignature:function(f,g){var h=this.reader.index;this.reader.setIndex(f);var _=this.reader.readString(4)===g;return this.reader.setIndex(h),_},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var f=this.reader.readData(this.zipCommentLength),g=d.uint8array?"uint8array":"array",h=i.transformTo(g,f);this.zipComment=this.loadOptions.decodeFileName(h)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var f,g,h,_=this.zip64EndOfCentralSize-44;0<_;)f=this.reader.readInt(2),g=this.reader.readInt(4),h=this.reader.readData(g),this.zip64ExtensibleData[f]={id:f,length:g,value:h}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var f,g;for(f=0;f<this.files.length;f++)g=this.files[f],this.reader.setIndex(g.localHeaderOffset),this.checkSignature(a.LOCAL_FILE_HEADER),g.readLocalPart(this.reader),g.handleUTF8(),g.processAttributes()},readCentralDir:function(){var f;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(a.CENTRAL_FILE_HEADER);)(f=new c({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(f);if(this.centralDirRecords!==this.files.length&&this.centralDirRecords!==0&&this.files.length===0)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var f=this.reader.lastIndexOfSignature(a.CENTRAL_DIRECTORY_END);if(f<0)throw this.isSignature(0,a.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(f);var g=f;if(this.checkSignature(a.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(f=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(f),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,a.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var h=this.centralDirOffset+this.centralDirSize;this.zip64&&(h+=20,h+=12+this.zip64EndOfCentralSize);var _=g-h;if(0<_)this.isSignature(g,a.CENTRAL_FILE_HEADER)||(this.reader.zero=_);else if(_<0)throw new Error("Corrupted zip: missing "+Math.abs(_)+" bytes.")},prepareReader:function(f){this.reader=o(f)},load:function(f){this.prepareReader(f),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},s.exports=u},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(n,s,r){var o=n("./reader/readerFor"),i=n("./utils"),a=n("./compressedObject"),c=n("./crc32"),d=n("./utf8"),u=n("./compressions"),f=n("./support");function g(h,_){this.options=h,this.loadOptions=_}g.prototype={isEncrypted:function(){return(1&this.bitFlag)==1},useUTF8:function(){return(2048&this.bitFlag)==2048},readLocalPart:function(h){var _,m;if(h.skip(22),this.fileNameLength=h.readInt(2),m=h.readInt(2),this.fileName=h.readData(this.fileNameLength),h.skip(m),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if((_=function(w){for(var b in u)if(Object.prototype.hasOwnProperty.call(u,b)&&u[b].magic===w)return u[b];return null}(this.compressionMethod))===null)throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new a(this.compressedSize,this.uncompressedSize,this.crc32,_,h.readData(this.compressedSize))},readCentralPart:function(h){this.versionMadeBy=h.readInt(2),h.skip(2),this.bitFlag=h.readInt(2),this.compressionMethod=h.readString(2),this.date=h.readDate(),this.crc32=h.readInt(4),this.compressedSize=h.readInt(4),this.uncompressedSize=h.readInt(4);var _=h.readInt(2);if(this.extraFieldsLength=h.readInt(2),this.fileCommentLength=h.readInt(2),this.diskNumberStart=h.readInt(2),this.internalFileAttributes=h.readInt(2),this.externalFileAttributes=h.readInt(4),this.localHeaderOffset=h.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");h.skip(_),this.readExtraFields(h),this.parseZIP64ExtraField(h),this.fileComment=h.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var h=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),h==0&&(this.dosPermissions=63&this.externalFileAttributes),h==3&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||this.fileNameStr.slice(-1)!=="/"||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var h=o(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=h.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=h.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=h.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=h.readInt(4))}},readExtraFields:function(h){var _,m,w,b=h.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});h.index+4<b;)_=h.readInt(2),m=h.readInt(2),w=h.readData(m),this.extraFields[_]={id:_,length:m,value:w};h.setIndex(b)},handleUTF8:function(){var h=f.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=d.utf8decode(this.fileName),this.fileCommentStr=d.utf8decode(this.fileComment);else{var _=this.findExtraFieldUnicodePath();if(_!==null)this.fileNameStr=_;else{var m=i.transformTo(h,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(m)}var w=this.findExtraFieldUnicodeComment();if(w!==null)this.fileCommentStr=w;else{var b=i.transformTo(h,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(b)}}},findExtraFieldUnicodePath:function(){var h=this.extraFields[28789];if(h){var _=o(h.value);return _.readInt(1)!==1||c(this.fileName)!==_.readInt(4)?null:d.utf8decode(_.readData(h.length-5))}return null},findExtraFieldUnicodeComment:function(){var h=this.extraFields[25461];if(h){var _=o(h.value);return _.readInt(1)!==1||c(this.fileComment)!==_.readInt(4)?null:d.utf8decode(_.readData(h.length-5))}return null}},s.exports=g},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(n,s,r){function o(_,m,w){this.name=_,this.dir=w.dir,this.date=w.date,this.comment=w.comment,this.unixPermissions=w.unixPermissions,this.dosPermissions=w.dosPermissions,this._data=m,this._dataBinary=w.binary,this.options={compression:w.compression,compressionOptions:w.compressionOptions}}var i=n("./stream/StreamHelper"),a=n("./stream/DataWorker"),c=n("./utf8"),d=n("./compressedObject"),u=n("./stream/GenericWorker");o.prototype={internalStream:function(_){var m=null,w="string";try{if(!_)throw new Error("No output type specified.");var b=(w=_.toLowerCase())==="string"||w==="text";w!=="binarystring"&&w!=="text"||(w="string"),m=this._decompressWorker();var x=!this._dataBinary;x&&!b&&(m=m.pipe(new c.Utf8EncodeWorker)),!x&&b&&(m=m.pipe(new c.Utf8DecodeWorker))}catch(k){(m=new u("error")).error(k)}return new i(m,w,"")},async:function(_,m){return this.internalStream(_).accumulate(m)},nodeStream:function(_,m){return this.internalStream(_||"nodebuffer").toNodejsStream(m)},_compressWorker:function(_,m){if(this._data instanceof d&&this._data.compression.magic===_.magic)return this._data.getCompressedWorker();var w=this._decompressWorker();return this._dataBinary||(w=w.pipe(new c.Utf8EncodeWorker)),d.createWorkerFrom(w,_,m)},_decompressWorker:function(){return this._data instanceof d?this._data.getContentWorker():this._data instanceof u?this._data:new a(this._data)}};for(var f=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],g=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},h=0;h<f.length;h++)o.prototype[f[h]]=g;s.exports=o},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(n,s,r){(function(o){var i,a,c=o.MutationObserver||o.WebKitMutationObserver;if(c){var d=0,u=new c(_),f=o.document.createTextNode("");u.observe(f,{characterData:!0}),i=function(){f.data=d=++d%2}}else if(o.setImmediate||o.MessageChannel===void 0)i="document"in o&&"onreadystatechange"in o.document.createElement("script")?function(){var m=o.document.createElement("script");m.onreadystatechange=function(){_(),m.onreadystatechange=null,m.parentNode.removeChild(m),m=null},o.document.documentElement.appendChild(m)}:function(){setTimeout(_,0)};else{var g=new o.MessageChannel;g.port1.onmessage=_,i=function(){g.port2.postMessage(0)}}var h=[];function _(){var m,w;a=!0;for(var b=h.length;b;){for(w=h,h=[],m=-1;++m<b;)w[m]();b=h.length}a=!1}s.exports=function(m){h.push(m)!==1||a||i()}}).call(this,typeof Kn<"u"?Kn:typeof self<"u"?self:typeof window<"u"?window:{})},{}],37:[function(n,s,r){var o=n("immediate");function i(){}var a={},c=["REJECTED"],d=["FULFILLED"],u=["PENDING"];function f(b){if(typeof b!="function")throw new TypeError("resolver must be a function");this.state=u,this.queue=[],this.outcome=void 0,b!==i&&m(this,b)}function g(b,x,k){this.promise=b,typeof x=="function"&&(this.onFulfilled=x,this.callFulfilled=this.otherCallFulfilled),typeof k=="function"&&(this.onRejected=k,this.callRejected=this.otherCallRejected)}function h(b,x,k){o(function(){var B;try{B=x(k)}catch(P){return a.reject(b,P)}B===b?a.reject(b,new TypeError("Cannot resolve promise with itself")):a.resolve(b,B)})}function _(b){var x=b&&b.then;if(b&&(typeof b=="object"||typeof b=="function")&&typeof x=="function")return function(){x.apply(b,arguments)}}function m(b,x){var k=!1;function B(N){k||(k=!0,a.reject(b,N))}function P(N){k||(k=!0,a.resolve(b,N))}var G=w(function(){x(P,B)});G.status==="error"&&B(G.value)}function w(b,x){var k={};try{k.value=b(x),k.status="success"}catch(B){k.status="error",k.value=B}return k}(s.exports=f).prototype.finally=function(b){if(typeof b!="function")return this;var x=this.constructor;return this.then(function(k){return x.resolve(b()).then(function(){return k})},function(k){return x.resolve(b()).then(function(){throw k})})},f.prototype.catch=function(b){return this.then(null,b)},f.prototype.then=function(b,x){if(typeof b!="function"&&this.state===d||typeof x!="function"&&this.state===c)return this;var k=new this.constructor(i);return this.state!==u?h(k,this.state===d?b:x,this.outcome):this.queue.push(new g(k,b,x)),k},g.prototype.callFulfilled=function(b){a.resolve(this.promise,b)},g.prototype.otherCallFulfilled=function(b){h(this.promise,this.onFulfilled,b)},g.prototype.callRejected=function(b){a.reject(this.promise,b)},g.prototype.otherCallRejected=function(b){h(this.promise,this.onRejected,b)},a.resolve=function(b,x){var k=w(_,x);if(k.status==="error")return a.reject(b,k.value);var B=k.value;if(B)m(b,B);else{b.state=d,b.outcome=x;for(var P=-1,G=b.queue.length;++P<G;)b.queue[P].callFulfilled(x)}return b},a.reject=function(b,x){b.state=c,b.outcome=x;for(var k=-1,B=b.queue.length;++k<B;)b.queue[k].callRejected(x);return b},f.resolve=function(b){return b instanceof this?b:a.resolve(new this(i),b)},f.reject=function(b){var x=new this(i);return a.reject(x,b)},f.all=function(b){var x=this;if(Object.prototype.toString.call(b)!=="[object Array]")return this.reject(new TypeError("must be an array"));var k=b.length,B=!1;if(!k)return this.resolve([]);for(var P=new Array(k),G=0,N=-1,Z=new this(i);++N<k;)M(b[N],N);return Z;function M(q,ie){x.resolve(q).then(function(C){P[ie]=C,++G!==k||B||(B=!0,a.resolve(Z,P))},function(C){B||(B=!0,a.reject(Z,C))})}},f.race=function(b){var x=this;if(Object.prototype.toString.call(b)!=="[object Array]")return this.reject(new TypeError("must be an array"));var k=b.length,B=!1;if(!k)return this.resolve([]);for(var P=-1,G=new this(i);++P<k;)N=b[P],x.resolve(N).then(function(Z){B||(B=!0,a.resolve(G,Z))},function(Z){B||(B=!0,a.reject(G,Z))});var N;return G}},{immediate:36}],38:[function(n,s,r){var o={};(0,n("./lib/utils/common").assign)(o,n("./lib/deflate"),n("./lib/inflate"),n("./lib/zlib/constants")),s.exports=o},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(n,s,r){var o=n("./zlib/deflate"),i=n("./utils/common"),a=n("./utils/strings"),c=n("./zlib/messages"),d=n("./zlib/zstream"),u=Object.prototype.toString,f=0,g=-1,h=0,_=8;function m(b){if(!(this instanceof m))return new m(b);this.options=i.assign({level:g,method:_,chunkSize:16384,windowBits:15,memLevel:8,strategy:h,to:""},b||{});var x=this.options;x.raw&&0<x.windowBits?x.windowBits=-x.windowBits:x.gzip&&0<x.windowBits&&x.windowBits<16&&(x.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new d,this.strm.avail_out=0;var k=o.deflateInit2(this.strm,x.level,x.method,x.windowBits,x.memLevel,x.strategy);if(k!==f)throw new Error(c[k]);if(x.header&&o.deflateSetHeader(this.strm,x.header),x.dictionary){var B;if(B=typeof x.dictionary=="string"?a.string2buf(x.dictionary):u.call(x.dictionary)==="[object ArrayBuffer]"?new Uint8Array(x.dictionary):x.dictionary,(k=o.deflateSetDictionary(this.strm,B))!==f)throw new Error(c[k]);this._dict_set=!0}}function w(b,x){var k=new m(x);if(k.push(b,!0),k.err)throw k.msg||c[k.err];return k.result}m.prototype.push=function(b,x){var k,B,P=this.strm,G=this.options.chunkSize;if(this.ended)return!1;B=x===~~x?x:x===!0?4:0,typeof b=="string"?P.input=a.string2buf(b):u.call(b)==="[object ArrayBuffer]"?P.input=new Uint8Array(b):P.input=b,P.next_in=0,P.avail_in=P.input.length;do{if(P.avail_out===0&&(P.output=new i.Buf8(G),P.next_out=0,P.avail_out=G),(k=o.deflate(P,B))!==1&&k!==f)return this.onEnd(k),!(this.ended=!0);P.avail_out!==0&&(P.avail_in!==0||B!==4&&B!==2)||(this.options.to==="string"?this.onData(a.buf2binstring(i.shrinkBuf(P.output,P.next_out))):this.onData(i.shrinkBuf(P.output,P.next_out)))}while((0<P.avail_in||P.avail_out===0)&&k!==1);return B===4?(k=o.deflateEnd(this.strm),this.onEnd(k),this.ended=!0,k===f):B!==2||(this.onEnd(f),!(P.avail_out=0))},m.prototype.onData=function(b){this.chunks.push(b)},m.prototype.onEnd=function(b){b===f&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=b,this.msg=this.strm.msg},r.Deflate=m,r.deflate=w,r.deflateRaw=function(b,x){return(x=x||{}).raw=!0,w(b,x)},r.gzip=function(b,x){return(x=x||{}).gzip=!0,w(b,x)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(n,s,r){var o=n("./zlib/inflate"),i=n("./utils/common"),a=n("./utils/strings"),c=n("./zlib/constants"),d=n("./zlib/messages"),u=n("./zlib/zstream"),f=n("./zlib/gzheader"),g=Object.prototype.toString;function h(m){if(!(this instanceof h))return new h(m);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},m||{});var w=this.options;w.raw&&0<=w.windowBits&&w.windowBits<16&&(w.windowBits=-w.windowBits,w.windowBits===0&&(w.windowBits=-15)),!(0<=w.windowBits&&w.windowBits<16)||m&&m.windowBits||(w.windowBits+=32),15<w.windowBits&&w.windowBits<48&&!(15&w.windowBits)&&(w.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new u,this.strm.avail_out=0;var b=o.inflateInit2(this.strm,w.windowBits);if(b!==c.Z_OK)throw new Error(d[b]);this.header=new f,o.inflateGetHeader(this.strm,this.header)}function _(m,w){var b=new h(w);if(b.push(m,!0),b.err)throw b.msg||d[b.err];return b.result}h.prototype.push=function(m,w){var b,x,k,B,P,G,N=this.strm,Z=this.options.chunkSize,M=this.options.dictionary,q=!1;if(this.ended)return!1;x=w===~~w?w:w===!0?c.Z_FINISH:c.Z_NO_FLUSH,typeof m=="string"?N.input=a.binstring2buf(m):g.call(m)==="[object ArrayBuffer]"?N.input=new Uint8Array(m):N.input=m,N.next_in=0,N.avail_in=N.input.length;do{if(N.avail_out===0&&(N.output=new i.Buf8(Z),N.next_out=0,N.avail_out=Z),(b=o.inflate(N,c.Z_NO_FLUSH))===c.Z_NEED_DICT&&M&&(G=typeof M=="string"?a.string2buf(M):g.call(M)==="[object ArrayBuffer]"?new Uint8Array(M):M,b=o.inflateSetDictionary(this.strm,G)),b===c.Z_BUF_ERROR&&q===!0&&(b=c.Z_OK,q=!1),b!==c.Z_STREAM_END&&b!==c.Z_OK)return this.onEnd(b),!(this.ended=!0);N.next_out&&(N.avail_out!==0&&b!==c.Z_STREAM_END&&(N.avail_in!==0||x!==c.Z_FINISH&&x!==c.Z_SYNC_FLUSH)||(this.options.to==="string"?(k=a.utf8border(N.output,N.next_out),B=N.next_out-k,P=a.buf2string(N.output,k),N.next_out=B,N.avail_out=Z-B,B&&i.arraySet(N.output,N.output,k,B,0),this.onData(P)):this.onData(i.shrinkBuf(N.output,N.next_out)))),N.avail_in===0&&N.avail_out===0&&(q=!0)}while((0<N.avail_in||N.avail_out===0)&&b!==c.Z_STREAM_END);return b===c.Z_STREAM_END&&(x=c.Z_FINISH),x===c.Z_FINISH?(b=o.inflateEnd(this.strm),this.onEnd(b),this.ended=!0,b===c.Z_OK):x!==c.Z_SYNC_FLUSH||(this.onEnd(c.Z_OK),!(N.avail_out=0))},h.prototype.onData=function(m){this.chunks.push(m)},h.prototype.onEnd=function(m){m===c.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=m,this.msg=this.strm.msg},r.Inflate=h,r.inflate=_,r.inflateRaw=function(m,w){return(w=w||{}).raw=!0,_(m,w)},r.ungzip=_},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(n,s,r){var o=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";r.assign=function(c){for(var d=Array.prototype.slice.call(arguments,1);d.length;){var u=d.shift();if(u){if(typeof u!="object")throw new TypeError(u+"must be non-object");for(var f in u)u.hasOwnProperty(f)&&(c[f]=u[f])}}return c},r.shrinkBuf=function(c,d){return c.length===d?c:c.subarray?c.subarray(0,d):(c.length=d,c)};var i={arraySet:function(c,d,u,f,g){if(d.subarray&&c.subarray)c.set(d.subarray(u,u+f),g);else for(var h=0;h<f;h++)c[g+h]=d[u+h]},flattenChunks:function(c){var d,u,f,g,h,_;for(d=f=0,u=c.length;d<u;d++)f+=c[d].length;for(_=new Uint8Array(f),d=g=0,u=c.length;d<u;d++)h=c[d],_.set(h,g),g+=h.length;return _}},a={arraySet:function(c,d,u,f,g){for(var h=0;h<f;h++)c[g+h]=d[u+h]},flattenChunks:function(c){return[].concat.apply([],c)}};r.setTyped=function(c){c?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(o)},{}],42:[function(n,s,r){var o=n("./common"),i=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch{i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{a=!1}for(var c=new o.Buf8(256),d=0;d<256;d++)c[d]=252<=d?6:248<=d?5:240<=d?4:224<=d?3:192<=d?2:1;function u(f,g){if(g<65537&&(f.subarray&&a||!f.subarray&&i))return String.fromCharCode.apply(null,o.shrinkBuf(f,g));for(var h="",_=0;_<g;_++)h+=String.fromCharCode(f[_]);return h}c[254]=c[254]=1,r.string2buf=function(f){var g,h,_,m,w,b=f.length,x=0;for(m=0;m<b;m++)(64512&(h=f.charCodeAt(m)))==55296&&m+1<b&&(64512&(_=f.charCodeAt(m+1)))==56320&&(h=65536+(h-55296<<10)+(_-56320),m++),x+=h<128?1:h<2048?2:h<65536?3:4;for(g=new o.Buf8(x),m=w=0;w<x;m++)(64512&(h=f.charCodeAt(m)))==55296&&m+1<b&&(64512&(_=f.charCodeAt(m+1)))==56320&&(h=65536+(h-55296<<10)+(_-56320),m++),h<128?g[w++]=h:(h<2048?g[w++]=192|h>>>6:(h<65536?g[w++]=224|h>>>12:(g[w++]=240|h>>>18,g[w++]=128|h>>>12&63),g[w++]=128|h>>>6&63),g[w++]=128|63&h);return g},r.buf2binstring=function(f){return u(f,f.length)},r.binstring2buf=function(f){for(var g=new o.Buf8(f.length),h=0,_=g.length;h<_;h++)g[h]=f.charCodeAt(h);return g},r.buf2string=function(f,g){var h,_,m,w,b=g||f.length,x=new Array(2*b);for(h=_=0;h<b;)if((m=f[h++])<128)x[_++]=m;else if(4<(w=c[m]))x[_++]=65533,h+=w-1;else{for(m&=w===2?31:w===3?15:7;1<w&&h<b;)m=m<<6|63&f[h++],w--;1<w?x[_++]=65533:m<65536?x[_++]=m:(m-=65536,x[_++]=55296|m>>10&1023,x[_++]=56320|1023&m)}return u(x,_)},r.utf8border=function(f,g){var h;for((g=g||f.length)>f.length&&(g=f.length),h=g-1;0<=h&&(192&f[h])==128;)h--;return h<0||h===0?g:h+c[f[h]]>g?h:g}},{"./common":41}],43:[function(n,s,r){s.exports=function(o,i,a,c){for(var d=65535&o|0,u=o>>>16&65535|0,f=0;a!==0;){for(a-=f=2e3<a?2e3:a;u=u+(d=d+i[c++]|0)|0,--f;);d%=65521,u%=65521}return d|u<<16|0}},{}],44:[function(n,s,r){s.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(n,s,r){var o=function(){for(var i,a=[],c=0;c<256;c++){i=c;for(var d=0;d<8;d++)i=1&i?3988292384^i>>>1:i>>>1;a[c]=i}return a}();s.exports=function(i,a,c,d){var u=o,f=d+c;i^=-1;for(var g=d;g<f;g++)i=i>>>8^u[255&(i^a[g])];return-1^i}},{}],46:[function(n,s,r){var o,i=n("../utils/common"),a=n("./trees"),c=n("./adler32"),d=n("./crc32"),u=n("./messages"),f=0,g=4,h=0,_=-2,m=-1,w=4,b=2,x=8,k=9,B=286,P=30,G=19,N=2*B+1,Z=15,M=3,q=258,ie=q+M+1,C=42,j=113,p=1,R=2,Y=3,D=4;function ee(l,A){return l.msg=u[A],A}function K(l){return(l<<1)-(4<l?9:0)}function oe(l){for(var A=l.length;0<=--A;)l[A]=0}function F(l){var A=l.state,T=A.pending;T>l.avail_out&&(T=l.avail_out),T!==0&&(i.arraySet(l.output,A.pending_buf,A.pending_out,T,l.next_out),l.next_out+=T,A.pending_out+=T,l.total_out+=T,l.avail_out-=T,A.pending-=T,A.pending===0&&(A.pending_out=0))}function L(l,A){a._tr_flush_block(l,0<=l.block_start?l.block_start:-1,l.strstart-l.block_start,A),l.block_start=l.strstart,F(l.strm)}function le(l,A){l.pending_buf[l.pending++]=A}function ae(l,A){l.pending_buf[l.pending++]=A>>>8&255,l.pending_buf[l.pending++]=255&A}function te(l,A){var T,y,v=l.max_chain_length,E=l.strstart,$=l.prev_length,z=l.nice_match,I=l.strstart>l.w_size-ie?l.strstart-(l.w_size-ie):0,J=l.window,U=l.w_mask,H=l.prev,Q=l.strstart+q,ce=J[E+$-1],ue=J[E+$];l.prev_length>=l.good_match&&(v>>=2),z>l.lookahead&&(z=l.lookahead);do if(J[(T=A)+$]===ue&&J[T+$-1]===ce&&J[T]===J[E]&&J[++T]===J[E+1]){E+=2,T++;do;while(J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&J[++E]===J[++T]&&E<Q);if(y=q-(Q-E),E=Q-q,$<y){if(l.match_start=A,z<=($=y))break;ce=J[E+$-1],ue=J[E+$]}}while((A=H[A&U])>I&&--v!=0);return $<=l.lookahead?$:l.lookahead}function ge(l){var A,T,y,v,E,$,z,I,J,U,H=l.w_size;do{if(v=l.window_size-l.lookahead-l.strstart,l.strstart>=H+(H-ie)){for(i.arraySet(l.window,l.window,H,H,0),l.match_start-=H,l.strstart-=H,l.block_start-=H,A=T=l.hash_size;y=l.head[--A],l.head[A]=H<=y?y-H:0,--T;);for(A=T=H;y=l.prev[--A],l.prev[A]=H<=y?y-H:0,--T;);v+=H}if(l.strm.avail_in===0)break;if($=l.strm,z=l.window,I=l.strstart+l.lookahead,J=v,U=void 0,U=$.avail_in,J<U&&(U=J),T=U===0?0:($.avail_in-=U,i.arraySet(z,$.input,$.next_in,U,I),$.state.wrap===1?$.adler=c($.adler,z,U,I):$.state.wrap===2&&($.adler=d($.adler,z,U,I)),$.next_in+=U,$.total_in+=U,U),l.lookahead+=T,l.lookahead+l.insert>=M)for(E=l.strstart-l.insert,l.ins_h=l.window[E],l.ins_h=(l.ins_h<<l.hash_shift^l.window[E+1])&l.hash_mask;l.insert&&(l.ins_h=(l.ins_h<<l.hash_shift^l.window[E+M-1])&l.hash_mask,l.prev[E&l.w_mask]=l.head[l.ins_h],l.head[l.ins_h]=E,E++,l.insert--,!(l.lookahead+l.insert<M)););}while(l.lookahead<ie&&l.strm.avail_in!==0)}function W(l,A){for(var T,y;;){if(l.lookahead<ie){if(ge(l),l.lookahead<ie&&A===f)return p;if(l.lookahead===0)break}if(T=0,l.lookahead>=M&&(l.ins_h=(l.ins_h<<l.hash_shift^l.window[l.strstart+M-1])&l.hash_mask,T=l.prev[l.strstart&l.w_mask]=l.head[l.ins_h],l.head[l.ins_h]=l.strstart),T!==0&&l.strstart-T<=l.w_size-ie&&(l.match_length=te(l,T)),l.match_length>=M)if(y=a._tr_tally(l,l.strstart-l.match_start,l.match_length-M),l.lookahead-=l.match_length,l.match_length<=l.max_lazy_match&&l.lookahead>=M){for(l.match_length--;l.strstart++,l.ins_h=(l.ins_h<<l.hash_shift^l.window[l.strstart+M-1])&l.hash_mask,T=l.prev[l.strstart&l.w_mask]=l.head[l.ins_h],l.head[l.ins_h]=l.strstart,--l.match_length!=0;);l.strstart++}else l.strstart+=l.match_length,l.match_length=0,l.ins_h=l.window[l.strstart],l.ins_h=(l.ins_h<<l.hash_shift^l.window[l.strstart+1])&l.hash_mask;else y=a._tr_tally(l,0,l.window[l.strstart]),l.lookahead--,l.strstart++;if(y&&(L(l,!1),l.strm.avail_out===0))return p}return l.insert=l.strstart<M-1?l.strstart:M-1,A===g?(L(l,!0),l.strm.avail_out===0?Y:D):l.last_lit&&(L(l,!1),l.strm.avail_out===0)?p:R}function X(l,A){for(var T,y,v;;){if(l.lookahead<ie){if(ge(l),l.lookahead<ie&&A===f)return p;if(l.lookahead===0)break}if(T=0,l.lookahead>=M&&(l.ins_h=(l.ins_h<<l.hash_shift^l.window[l.strstart+M-1])&l.hash_mask,T=l.prev[l.strstart&l.w_mask]=l.head[l.ins_h],l.head[l.ins_h]=l.strstart),l.prev_length=l.match_length,l.prev_match=l.match_start,l.match_length=M-1,T!==0&&l.prev_length<l.max_lazy_match&&l.strstart-T<=l.w_size-ie&&(l.match_length=te(l,T),l.match_length<=5&&(l.strategy===1||l.match_length===M&&4096<l.strstart-l.match_start)&&(l.match_length=M-1)),l.prev_length>=M&&l.match_length<=l.prev_length){for(v=l.strstart+l.lookahead-M,y=a._tr_tally(l,l.strstart-1-l.prev_match,l.prev_length-M),l.lookahead-=l.prev_length-1,l.prev_length-=2;++l.strstart<=v&&(l.ins_h=(l.ins_h<<l.hash_shift^l.window[l.strstart+M-1])&l.hash_mask,T=l.prev[l.strstart&l.w_mask]=l.head[l.ins_h],l.head[l.ins_h]=l.strstart),--l.prev_length!=0;);if(l.match_available=0,l.match_length=M-1,l.strstart++,y&&(L(l,!1),l.strm.avail_out===0))return p}else if(l.match_available){if((y=a._tr_tally(l,0,l.window[l.strstart-1]))&&L(l,!1),l.strstart++,l.lookahead--,l.strm.avail_out===0)return p}else l.match_available=1,l.strstart++,l.lookahead--}return l.match_available&&(y=a._tr_tally(l,0,l.window[l.strstart-1]),l.match_available=0),l.insert=l.strstart<M-1?l.strstart:M-1,A===g?(L(l,!0),l.strm.avail_out===0?Y:D):l.last_lit&&(L(l,!1),l.strm.avail_out===0)?p:R}function V(l,A,T,y,v){this.good_length=l,this.max_lazy=A,this.nice_length=T,this.max_chain=y,this.func=v}function se(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=x,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*N),this.dyn_dtree=new i.Buf16(2*(2*P+1)),this.bl_tree=new i.Buf16(2*(2*G+1)),oe(this.dyn_ltree),oe(this.dyn_dtree),oe(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(Z+1),this.heap=new i.Buf16(2*B+1),oe(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*B+1),oe(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function fe(l){var A;return l&&l.state?(l.total_in=l.total_out=0,l.data_type=b,(A=l.state).pending=0,A.pending_out=0,A.wrap<0&&(A.wrap=-A.wrap),A.status=A.wrap?C:j,l.adler=A.wrap===2?0:1,A.last_flush=f,a._tr_init(A),h):ee(l,_)}function ve(l){var A=fe(l);return A===h&&function(T){T.window_size=2*T.w_size,oe(T.head),T.max_lazy_match=o[T.level].max_lazy,T.good_match=o[T.level].good_length,T.nice_match=o[T.level].nice_length,T.max_chain_length=o[T.level].max_chain,T.strstart=0,T.block_start=0,T.lookahead=0,T.insert=0,T.match_length=T.prev_length=M-1,T.match_available=0,T.ins_h=0}(l.state),A}function S(l,A,T,y,v,E){if(!l)return _;var $=1;if(A===m&&(A=6),y<0?($=0,y=-y):15<y&&($=2,y-=16),v<1||k<v||T!==x||y<8||15<y||A<0||9<A||E<0||w<E)return ee(l,_);y===8&&(y=9);var z=new se;return(l.state=z).strm=l,z.wrap=$,z.gzhead=null,z.w_bits=y,z.w_size=1<<z.w_bits,z.w_mask=z.w_size-1,z.hash_bits=v+7,z.hash_size=1<<z.hash_bits,z.hash_mask=z.hash_size-1,z.hash_shift=~~((z.hash_bits+M-1)/M),z.window=new i.Buf8(2*z.w_size),z.head=new i.Buf16(z.hash_size),z.prev=new i.Buf16(z.w_size),z.lit_bufsize=1<<v+6,z.pending_buf_size=4*z.lit_bufsize,z.pending_buf=new i.Buf8(z.pending_buf_size),z.d_buf=1*z.lit_bufsize,z.l_buf=3*z.lit_bufsize,z.level=A,z.strategy=E,z.method=T,ve(l)}o=[new V(0,0,0,0,function(l,A){var T=65535;for(T>l.pending_buf_size-5&&(T=l.pending_buf_size-5);;){if(l.lookahead<=1){if(ge(l),l.lookahead===0&&A===f)return p;if(l.lookahead===0)break}l.strstart+=l.lookahead,l.lookahead=0;var y=l.block_start+T;if((l.strstart===0||l.strstart>=y)&&(l.lookahead=l.strstart-y,l.strstart=y,L(l,!1),l.strm.avail_out===0)||l.strstart-l.block_start>=l.w_size-ie&&(L(l,!1),l.strm.avail_out===0))return p}return l.insert=0,A===g?(L(l,!0),l.strm.avail_out===0?Y:D):(l.strstart>l.block_start&&(L(l,!1),l.strm.avail_out),p)}),new V(4,4,8,4,W),new V(4,5,16,8,W),new V(4,6,32,32,W),new V(4,4,16,16,X),new V(8,16,32,32,X),new V(8,16,128,128,X),new V(8,32,128,256,X),new V(32,128,258,1024,X),new V(32,258,258,4096,X)],r.deflateInit=function(l,A){return S(l,A,x,15,8,0)},r.deflateInit2=S,r.deflateReset=ve,r.deflateResetKeep=fe,r.deflateSetHeader=function(l,A){return l&&l.state?l.state.wrap!==2?_:(l.state.gzhead=A,h):_},r.deflate=function(l,A){var T,y,v,E;if(!l||!l.state||5<A||A<0)return l?ee(l,_):_;if(y=l.state,!l.output||!l.input&&l.avail_in!==0||y.status===666&&A!==g)return ee(l,l.avail_out===0?-5:_);if(y.strm=l,T=y.last_flush,y.last_flush=A,y.status===C)if(y.wrap===2)l.adler=0,le(y,31),le(y,139),le(y,8),y.gzhead?(le(y,(y.gzhead.text?1:0)+(y.gzhead.hcrc?2:0)+(y.gzhead.extra?4:0)+(y.gzhead.name?8:0)+(y.gzhead.comment?16:0)),le(y,255&y.gzhead.time),le(y,y.gzhead.time>>8&255),le(y,y.gzhead.time>>16&255),le(y,y.gzhead.time>>24&255),le(y,y.level===9?2:2<=y.strategy||y.level<2?4:0),le(y,255&y.gzhead.os),y.gzhead.extra&&y.gzhead.extra.length&&(le(y,255&y.gzhead.extra.length),le(y,y.gzhead.extra.length>>8&255)),y.gzhead.hcrc&&(l.adler=d(l.adler,y.pending_buf,y.pending,0)),y.gzindex=0,y.status=69):(le(y,0),le(y,0),le(y,0),le(y,0),le(y,0),le(y,y.level===9?2:2<=y.strategy||y.level<2?4:0),le(y,3),y.status=j);else{var $=x+(y.w_bits-8<<4)<<8;$|=(2<=y.strategy||y.level<2?0:y.level<6?1:y.level===6?2:3)<<6,y.strstart!==0&&($|=32),$+=31-$%31,y.status=j,ae(y,$),y.strstart!==0&&(ae(y,l.adler>>>16),ae(y,65535&l.adler)),l.adler=1}if(y.status===69)if(y.gzhead.extra){for(v=y.pending;y.gzindex<(65535&y.gzhead.extra.length)&&(y.pending!==y.pending_buf_size||(y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),F(l),v=y.pending,y.pending!==y.pending_buf_size));)le(y,255&y.gzhead.extra[y.gzindex]),y.gzindex++;y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),y.gzindex===y.gzhead.extra.length&&(y.gzindex=0,y.status=73)}else y.status=73;if(y.status===73)if(y.gzhead.name){v=y.pending;do{if(y.pending===y.pending_buf_size&&(y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),F(l),v=y.pending,y.pending===y.pending_buf_size)){E=1;break}E=y.gzindex<y.gzhead.name.length?255&y.gzhead.name.charCodeAt(y.gzindex++):0,le(y,E)}while(E!==0);y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),E===0&&(y.gzindex=0,y.status=91)}else y.status=91;if(y.status===91)if(y.gzhead.comment){v=y.pending;do{if(y.pending===y.pending_buf_size&&(y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),F(l),v=y.pending,y.pending===y.pending_buf_size)){E=1;break}E=y.gzindex<y.gzhead.comment.length?255&y.gzhead.comment.charCodeAt(y.gzindex++):0,le(y,E)}while(E!==0);y.gzhead.hcrc&&y.pending>v&&(l.adler=d(l.adler,y.pending_buf,y.pending-v,v)),E===0&&(y.status=103)}else y.status=103;if(y.status===103&&(y.gzhead.hcrc?(y.pending+2>y.pending_buf_size&&F(l),y.pending+2<=y.pending_buf_size&&(le(y,255&l.adler),le(y,l.adler>>8&255),l.adler=0,y.status=j)):y.status=j),y.pending!==0){if(F(l),l.avail_out===0)return y.last_flush=-1,h}else if(l.avail_in===0&&K(A)<=K(T)&&A!==g)return ee(l,-5);if(y.status===666&&l.avail_in!==0)return ee(l,-5);if(l.avail_in!==0||y.lookahead!==0||A!==f&&y.status!==666){var z=y.strategy===2?function(I,J){for(var U;;){if(I.lookahead===0&&(ge(I),I.lookahead===0)){if(J===f)return p;break}if(I.match_length=0,U=a._tr_tally(I,0,I.window[I.strstart]),I.lookahead--,I.strstart++,U&&(L(I,!1),I.strm.avail_out===0))return p}return I.insert=0,J===g?(L(I,!0),I.strm.avail_out===0?Y:D):I.last_lit&&(L(I,!1),I.strm.avail_out===0)?p:R}(y,A):y.strategy===3?function(I,J){for(var U,H,Q,ce,ue=I.window;;){if(I.lookahead<=q){if(ge(I),I.lookahead<=q&&J===f)return p;if(I.lookahead===0)break}if(I.match_length=0,I.lookahead>=M&&0<I.strstart&&(H=ue[Q=I.strstart-1])===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]){ce=I.strstart+q;do;while(H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&H===ue[++Q]&&Q<ce);I.match_length=q-(ce-Q),I.match_length>I.lookahead&&(I.match_length=I.lookahead)}if(I.match_length>=M?(U=a._tr_tally(I,1,I.match_length-M),I.lookahead-=I.match_length,I.strstart+=I.match_length,I.match_length=0):(U=a._tr_tally(I,0,I.window[I.strstart]),I.lookahead--,I.strstart++),U&&(L(I,!1),I.strm.avail_out===0))return p}return I.insert=0,J===g?(L(I,!0),I.strm.avail_out===0?Y:D):I.last_lit&&(L(I,!1),I.strm.avail_out===0)?p:R}(y,A):o[y.level].func(y,A);if(z!==Y&&z!==D||(y.status=666),z===p||z===Y)return l.avail_out===0&&(y.last_flush=-1),h;if(z===R&&(A===1?a._tr_align(y):A!==5&&(a._tr_stored_block(y,0,0,!1),A===3&&(oe(y.head),y.lookahead===0&&(y.strstart=0,y.block_start=0,y.insert=0))),F(l),l.avail_out===0))return y.last_flush=-1,h}return A!==g?h:y.wrap<=0?1:(y.wrap===2?(le(y,255&l.adler),le(y,l.adler>>8&255),le(y,l.adler>>16&255),le(y,l.adler>>24&255),le(y,255&l.total_in),le(y,l.total_in>>8&255),le(y,l.total_in>>16&255),le(y,l.total_in>>24&255)):(ae(y,l.adler>>>16),ae(y,65535&l.adler)),F(l),0<y.wrap&&(y.wrap=-y.wrap),y.pending!==0?h:1)},r.deflateEnd=function(l){var A;return l&&l.state?(A=l.state.status)!==C&&A!==69&&A!==73&&A!==91&&A!==103&&A!==j&&A!==666?ee(l,_):(l.state=null,A===j?ee(l,-3):h):_},r.deflateSetDictionary=function(l,A){var T,y,v,E,$,z,I,J,U=A.length;if(!l||!l.state||(E=(T=l.state).wrap)===2||E===1&&T.status!==C||T.lookahead)return _;for(E===1&&(l.adler=c(l.adler,A,U,0)),T.wrap=0,U>=T.w_size&&(E===0&&(oe(T.head),T.strstart=0,T.block_start=0,T.insert=0),J=new i.Buf8(T.w_size),i.arraySet(J,A,U-T.w_size,T.w_size,0),A=J,U=T.w_size),$=l.avail_in,z=l.next_in,I=l.input,l.avail_in=U,l.next_in=0,l.input=A,ge(T);T.lookahead>=M;){for(y=T.strstart,v=T.lookahead-(M-1);T.ins_h=(T.ins_h<<T.hash_shift^T.window[y+M-1])&T.hash_mask,T.prev[y&T.w_mask]=T.head[T.ins_h],T.head[T.ins_h]=y,y++,--v;);T.strstart=y,T.lookahead=M-1,ge(T)}return T.strstart+=T.lookahead,T.block_start=T.strstart,T.insert=T.lookahead,T.lookahead=0,T.match_length=T.prev_length=M-1,T.match_available=0,l.next_in=z,l.input=I,l.avail_in=$,T.wrap=E,h},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(n,s,r){s.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(n,s,r){s.exports=function(o,i){var a,c,d,u,f,g,h,_,m,w,b,x,k,B,P,G,N,Z,M,q,ie,C,j,p,R;a=o.state,c=o.next_in,p=o.input,d=c+(o.avail_in-5),u=o.next_out,R=o.output,f=u-(i-o.avail_out),g=u+(o.avail_out-257),h=a.dmax,_=a.wsize,m=a.whave,w=a.wnext,b=a.window,x=a.hold,k=a.bits,B=a.lencode,P=a.distcode,G=(1<<a.lenbits)-1,N=(1<<a.distbits)-1;e:do{k<15&&(x+=p[c++]<<k,k+=8,x+=p[c++]<<k,k+=8),Z=B[x&G];t:for(;;){if(x>>>=M=Z>>>24,k-=M,(M=Z>>>16&255)===0)R[u++]=65535&Z;else{if(!(16&M)){if(!(64&M)){Z=B[(65535&Z)+(x&(1<<M)-1)];continue t}if(32&M){a.mode=12;break e}o.msg="invalid literal/length code",a.mode=30;break e}q=65535&Z,(M&=15)&&(k<M&&(x+=p[c++]<<k,k+=8),q+=x&(1<<M)-1,x>>>=M,k-=M),k<15&&(x+=p[c++]<<k,k+=8,x+=p[c++]<<k,k+=8),Z=P[x&N];n:for(;;){if(x>>>=M=Z>>>24,k-=M,!(16&(M=Z>>>16&255))){if(!(64&M)){Z=P[(65535&Z)+(x&(1<<M)-1)];continue n}o.msg="invalid distance code",a.mode=30;break e}if(ie=65535&Z,k<(M&=15)&&(x+=p[c++]<<k,(k+=8)<M&&(x+=p[c++]<<k,k+=8)),h<(ie+=x&(1<<M)-1)){o.msg="invalid distance too far back",a.mode=30;break e}if(x>>>=M,k-=M,(M=u-f)<ie){if(m<(M=ie-M)&&a.sane){o.msg="invalid distance too far back",a.mode=30;break e}if(j=b,(C=0)===w){if(C+=_-M,M<q){for(q-=M;R[u++]=b[C++],--M;);C=u-ie,j=R}}else if(w<M){if(C+=_+w-M,(M-=w)<q){for(q-=M;R[u++]=b[C++],--M;);if(C=0,w<q){for(q-=M=w;R[u++]=b[C++],--M;);C=u-ie,j=R}}}else if(C+=w-M,M<q){for(q-=M;R[u++]=b[C++],--M;);C=u-ie,j=R}for(;2<q;)R[u++]=j[C++],R[u++]=j[C++],R[u++]=j[C++],q-=3;q&&(R[u++]=j[C++],1<q&&(R[u++]=j[C++]))}else{for(C=u-ie;R[u++]=R[C++],R[u++]=R[C++],R[u++]=R[C++],2<(q-=3););q&&(R[u++]=R[C++],1<q&&(R[u++]=R[C++]))}break}}break}}while(c<d&&u<g);c-=q=k>>3,x&=(1<<(k-=q<<3))-1,o.next_in=c,o.next_out=u,o.avail_in=c<d?d-c+5:5-(c-d),o.avail_out=u<g?g-u+257:257-(u-g),a.hold=x,a.bits=k}},{}],49:[function(n,s,r){var o=n("../utils/common"),i=n("./adler32"),a=n("./crc32"),c=n("./inffast"),d=n("./inftrees"),u=1,f=2,g=0,h=-2,_=1,m=852,w=592;function b(C){return(C>>>24&255)+(C>>>8&65280)+((65280&C)<<8)+((255&C)<<24)}function x(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new o.Buf16(320),this.work=new o.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function k(C){var j;return C&&C.state?(j=C.state,C.total_in=C.total_out=j.total=0,C.msg="",j.wrap&&(C.adler=1&j.wrap),j.mode=_,j.last=0,j.havedict=0,j.dmax=32768,j.head=null,j.hold=0,j.bits=0,j.lencode=j.lendyn=new o.Buf32(m),j.distcode=j.distdyn=new o.Buf32(w),j.sane=1,j.back=-1,g):h}function B(C){var j;return C&&C.state?((j=C.state).wsize=0,j.whave=0,j.wnext=0,k(C)):h}function P(C,j){var p,R;return C&&C.state?(R=C.state,j<0?(p=0,j=-j):(p=1+(j>>4),j<48&&(j&=15)),j&&(j<8||15<j)?h:(R.window!==null&&R.wbits!==j&&(R.window=null),R.wrap=p,R.wbits=j,B(C))):h}function G(C,j){var p,R;return C?(R=new x,(C.state=R).window=null,(p=P(C,j))!==g&&(C.state=null),p):h}var N,Z,M=!0;function q(C){if(M){var j;for(N=new o.Buf32(512),Z=new o.Buf32(32),j=0;j<144;)C.lens[j++]=8;for(;j<256;)C.lens[j++]=9;for(;j<280;)C.lens[j++]=7;for(;j<288;)C.lens[j++]=8;for(d(u,C.lens,0,288,N,0,C.work,{bits:9}),j=0;j<32;)C.lens[j++]=5;d(f,C.lens,0,32,Z,0,C.work,{bits:5}),M=!1}C.lencode=N,C.lenbits=9,C.distcode=Z,C.distbits=5}function ie(C,j,p,R){var Y,D=C.state;return D.window===null&&(D.wsize=1<<D.wbits,D.wnext=0,D.whave=0,D.window=new o.Buf8(D.wsize)),R>=D.wsize?(o.arraySet(D.window,j,p-D.wsize,D.wsize,0),D.wnext=0,D.whave=D.wsize):(R<(Y=D.wsize-D.wnext)&&(Y=R),o.arraySet(D.window,j,p-R,Y,D.wnext),(R-=Y)?(o.arraySet(D.window,j,p-R,R,0),D.wnext=R,D.whave=D.wsize):(D.wnext+=Y,D.wnext===D.wsize&&(D.wnext=0),D.whave<D.wsize&&(D.whave+=Y))),0}r.inflateReset=B,r.inflateReset2=P,r.inflateResetKeep=k,r.inflateInit=function(C){return G(C,15)},r.inflateInit2=G,r.inflate=function(C,j){var p,R,Y,D,ee,K,oe,F,L,le,ae,te,ge,W,X,V,se,fe,ve,S,l,A,T,y,v=0,E=new o.Buf8(4),$=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!C||!C.state||!C.output||!C.input&&C.avail_in!==0)return h;(p=C.state).mode===12&&(p.mode=13),ee=C.next_out,Y=C.output,oe=C.avail_out,D=C.next_in,R=C.input,K=C.avail_in,F=p.hold,L=p.bits,le=K,ae=oe,A=g;e:for(;;)switch(p.mode){case _:if(p.wrap===0){p.mode=13;break}for(;L<16;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(2&p.wrap&&F===35615){E[p.check=0]=255&F,E[1]=F>>>8&255,p.check=a(p.check,E,2,0),L=F=0,p.mode=2;break}if(p.flags=0,p.head&&(p.head.done=!1),!(1&p.wrap)||(((255&F)<<8)+(F>>8))%31){C.msg="incorrect header check",p.mode=30;break}if((15&F)!=8){C.msg="unknown compression method",p.mode=30;break}if(L-=4,l=8+(15&(F>>>=4)),p.wbits===0)p.wbits=l;else if(l>p.wbits){C.msg="invalid window size",p.mode=30;break}p.dmax=1<<l,C.adler=p.check=1,p.mode=512&F?10:12,L=F=0;break;case 2:for(;L<16;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(p.flags=F,(255&p.flags)!=8){C.msg="unknown compression method",p.mode=30;break}if(57344&p.flags){C.msg="unknown header flags set",p.mode=30;break}p.head&&(p.head.text=F>>8&1),512&p.flags&&(E[0]=255&F,E[1]=F>>>8&255,p.check=a(p.check,E,2,0)),L=F=0,p.mode=3;case 3:for(;L<32;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.head&&(p.head.time=F),512&p.flags&&(E[0]=255&F,E[1]=F>>>8&255,E[2]=F>>>16&255,E[3]=F>>>24&255,p.check=a(p.check,E,4,0)),L=F=0,p.mode=4;case 4:for(;L<16;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.head&&(p.head.xflags=255&F,p.head.os=F>>8),512&p.flags&&(E[0]=255&F,E[1]=F>>>8&255,p.check=a(p.check,E,2,0)),L=F=0,p.mode=5;case 5:if(1024&p.flags){for(;L<16;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.length=F,p.head&&(p.head.extra_len=F),512&p.flags&&(E[0]=255&F,E[1]=F>>>8&255,p.check=a(p.check,E,2,0)),L=F=0}else p.head&&(p.head.extra=null);p.mode=6;case 6:if(1024&p.flags&&(K<(te=p.length)&&(te=K),te&&(p.head&&(l=p.head.extra_len-p.length,p.head.extra||(p.head.extra=new Array(p.head.extra_len)),o.arraySet(p.head.extra,R,D,te,l)),512&p.flags&&(p.check=a(p.check,R,te,D)),K-=te,D+=te,p.length-=te),p.length))break e;p.length=0,p.mode=7;case 7:if(2048&p.flags){if(K===0)break e;for(te=0;l=R[D+te++],p.head&&l&&p.length<65536&&(p.head.name+=String.fromCharCode(l)),l&&te<K;);if(512&p.flags&&(p.check=a(p.check,R,te,D)),K-=te,D+=te,l)break e}else p.head&&(p.head.name=null);p.length=0,p.mode=8;case 8:if(4096&p.flags){if(K===0)break e;for(te=0;l=R[D+te++],p.head&&l&&p.length<65536&&(p.head.comment+=String.fromCharCode(l)),l&&te<K;);if(512&p.flags&&(p.check=a(p.check,R,te,D)),K-=te,D+=te,l)break e}else p.head&&(p.head.comment=null);p.mode=9;case 9:if(512&p.flags){for(;L<16;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(F!==(65535&p.check)){C.msg="header crc mismatch",p.mode=30;break}L=F=0}p.head&&(p.head.hcrc=p.flags>>9&1,p.head.done=!0),C.adler=p.check=0,p.mode=12;break;case 10:for(;L<32;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}C.adler=p.check=b(F),L=F=0,p.mode=11;case 11:if(p.havedict===0)return C.next_out=ee,C.avail_out=oe,C.next_in=D,C.avail_in=K,p.hold=F,p.bits=L,2;C.adler=p.check=1,p.mode=12;case 12:if(j===5||j===6)break e;case 13:if(p.last){F>>>=7&L,L-=7&L,p.mode=27;break}for(;L<3;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}switch(p.last=1&F,L-=1,3&(F>>>=1)){case 0:p.mode=14;break;case 1:if(q(p),p.mode=20,j!==6)break;F>>>=2,L-=2;break e;case 2:p.mode=17;break;case 3:C.msg="invalid block type",p.mode=30}F>>>=2,L-=2;break;case 14:for(F>>>=7&L,L-=7&L;L<32;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if((65535&F)!=(F>>>16^65535)){C.msg="invalid stored block lengths",p.mode=30;break}if(p.length=65535&F,L=F=0,p.mode=15,j===6)break e;case 15:p.mode=16;case 16:if(te=p.length){if(K<te&&(te=K),oe<te&&(te=oe),te===0)break e;o.arraySet(Y,R,D,te,ee),K-=te,D+=te,oe-=te,ee+=te,p.length-=te;break}p.mode=12;break;case 17:for(;L<14;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(p.nlen=257+(31&F),F>>>=5,L-=5,p.ndist=1+(31&F),F>>>=5,L-=5,p.ncode=4+(15&F),F>>>=4,L-=4,286<p.nlen||30<p.ndist){C.msg="too many length or distance symbols",p.mode=30;break}p.have=0,p.mode=18;case 18:for(;p.have<p.ncode;){for(;L<3;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.lens[$[p.have++]]=7&F,F>>>=3,L-=3}for(;p.have<19;)p.lens[$[p.have++]]=0;if(p.lencode=p.lendyn,p.lenbits=7,T={bits:p.lenbits},A=d(0,p.lens,0,19,p.lencode,0,p.work,T),p.lenbits=T.bits,A){C.msg="invalid code lengths set",p.mode=30;break}p.have=0,p.mode=19;case 19:for(;p.have<p.nlen+p.ndist;){for(;V=(v=p.lencode[F&(1<<p.lenbits)-1])>>>16&255,se=65535&v,!((X=v>>>24)<=L);){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(se<16)F>>>=X,L-=X,p.lens[p.have++]=se;else{if(se===16){for(y=X+2;L<y;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(F>>>=X,L-=X,p.have===0){C.msg="invalid bit length repeat",p.mode=30;break}l=p.lens[p.have-1],te=3+(3&F),F>>>=2,L-=2}else if(se===17){for(y=X+3;L<y;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}L-=X,l=0,te=3+(7&(F>>>=X)),F>>>=3,L-=3}else{for(y=X+7;L<y;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}L-=X,l=0,te=11+(127&(F>>>=X)),F>>>=7,L-=7}if(p.have+te>p.nlen+p.ndist){C.msg="invalid bit length repeat",p.mode=30;break}for(;te--;)p.lens[p.have++]=l}}if(p.mode===30)break;if(p.lens[256]===0){C.msg="invalid code -- missing end-of-block",p.mode=30;break}if(p.lenbits=9,T={bits:p.lenbits},A=d(u,p.lens,0,p.nlen,p.lencode,0,p.work,T),p.lenbits=T.bits,A){C.msg="invalid literal/lengths set",p.mode=30;break}if(p.distbits=6,p.distcode=p.distdyn,T={bits:p.distbits},A=d(f,p.lens,p.nlen,p.ndist,p.distcode,0,p.work,T),p.distbits=T.bits,A){C.msg="invalid distances set",p.mode=30;break}if(p.mode=20,j===6)break e;case 20:p.mode=21;case 21:if(6<=K&&258<=oe){C.next_out=ee,C.avail_out=oe,C.next_in=D,C.avail_in=K,p.hold=F,p.bits=L,c(C,ae),ee=C.next_out,Y=C.output,oe=C.avail_out,D=C.next_in,R=C.input,K=C.avail_in,F=p.hold,L=p.bits,p.mode===12&&(p.back=-1);break}for(p.back=0;V=(v=p.lencode[F&(1<<p.lenbits)-1])>>>16&255,se=65535&v,!((X=v>>>24)<=L);){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(V&&!(240&V)){for(fe=X,ve=V,S=se;V=(v=p.lencode[S+((F&(1<<fe+ve)-1)>>fe)])>>>16&255,se=65535&v,!(fe+(X=v>>>24)<=L);){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}F>>>=fe,L-=fe,p.back+=fe}if(F>>>=X,L-=X,p.back+=X,p.length=se,V===0){p.mode=26;break}if(32&V){p.back=-1,p.mode=12;break}if(64&V){C.msg="invalid literal/length code",p.mode=30;break}p.extra=15&V,p.mode=22;case 22:if(p.extra){for(y=p.extra;L<y;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.length+=F&(1<<p.extra)-1,F>>>=p.extra,L-=p.extra,p.back+=p.extra}p.was=p.length,p.mode=23;case 23:for(;V=(v=p.distcode[F&(1<<p.distbits)-1])>>>16&255,se=65535&v,!((X=v>>>24)<=L);){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(!(240&V)){for(fe=X,ve=V,S=se;V=(v=p.distcode[S+((F&(1<<fe+ve)-1)>>fe)])>>>16&255,se=65535&v,!(fe+(X=v>>>24)<=L);){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}F>>>=fe,L-=fe,p.back+=fe}if(F>>>=X,L-=X,p.back+=X,64&V){C.msg="invalid distance code",p.mode=30;break}p.offset=se,p.extra=15&V,p.mode=24;case 24:if(p.extra){for(y=p.extra;L<y;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}p.offset+=F&(1<<p.extra)-1,F>>>=p.extra,L-=p.extra,p.back+=p.extra}if(p.offset>p.dmax){C.msg="invalid distance too far back",p.mode=30;break}p.mode=25;case 25:if(oe===0)break e;if(te=ae-oe,p.offset>te){if((te=p.offset-te)>p.whave&&p.sane){C.msg="invalid distance too far back",p.mode=30;break}ge=te>p.wnext?(te-=p.wnext,p.wsize-te):p.wnext-te,te>p.length&&(te=p.length),W=p.window}else W=Y,ge=ee-p.offset,te=p.length;for(oe<te&&(te=oe),oe-=te,p.length-=te;Y[ee++]=W[ge++],--te;);p.length===0&&(p.mode=21);break;case 26:if(oe===0)break e;Y[ee++]=p.length,oe--,p.mode=21;break;case 27:if(p.wrap){for(;L<32;){if(K===0)break e;K--,F|=R[D++]<<L,L+=8}if(ae-=oe,C.total_out+=ae,p.total+=ae,ae&&(C.adler=p.check=p.flags?a(p.check,Y,ae,ee-ae):i(p.check,Y,ae,ee-ae)),ae=oe,(p.flags?F:b(F))!==p.check){C.msg="incorrect data check",p.mode=30;break}L=F=0}p.mode=28;case 28:if(p.wrap&&p.flags){for(;L<32;){if(K===0)break e;K--,F+=R[D++]<<L,L+=8}if(F!==(4294967295&p.total)){C.msg="incorrect length check",p.mode=30;break}L=F=0}p.mode=29;case 29:A=1;break e;case 30:A=-3;break e;case 31:return-4;case 32:default:return h}return C.next_out=ee,C.avail_out=oe,C.next_in=D,C.avail_in=K,p.hold=F,p.bits=L,(p.wsize||ae!==C.avail_out&&p.mode<30&&(p.mode<27||j!==4))&&ie(C,C.output,C.next_out,ae-C.avail_out)?(p.mode=31,-4):(le-=C.avail_in,ae-=C.avail_out,C.total_in+=le,C.total_out+=ae,p.total+=ae,p.wrap&&ae&&(C.adler=p.check=p.flags?a(p.check,Y,ae,C.next_out-ae):i(p.check,Y,ae,C.next_out-ae)),C.data_type=p.bits+(p.last?64:0)+(p.mode===12?128:0)+(p.mode===20||p.mode===15?256:0),(le==0&&ae===0||j===4)&&A===g&&(A=-5),A)},r.inflateEnd=function(C){if(!C||!C.state)return h;var j=C.state;return j.window&&(j.window=null),C.state=null,g},r.inflateGetHeader=function(C,j){var p;return C&&C.state&&2&(p=C.state).wrap?((p.head=j).done=!1,g):h},r.inflateSetDictionary=function(C,j){var p,R=j.length;return C&&C.state?(p=C.state).wrap!==0&&p.mode!==11?h:p.mode===11&&i(1,j,R,0)!==p.check?-3:ie(C,j,R,R)?(p.mode=31,-4):(p.havedict=1,g):h},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(n,s,r){var o=n("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],c=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],d=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];s.exports=function(u,f,g,h,_,m,w,b){var x,k,B,P,G,N,Z,M,q,ie=b.bits,C=0,j=0,p=0,R=0,Y=0,D=0,ee=0,K=0,oe=0,F=0,L=null,le=0,ae=new o.Buf16(16),te=new o.Buf16(16),ge=null,W=0;for(C=0;C<=15;C++)ae[C]=0;for(j=0;j<h;j++)ae[f[g+j]]++;for(Y=ie,R=15;1<=R&&ae[R]===0;R--);if(R<Y&&(Y=R),R===0)return _[m++]=20971520,_[m++]=20971520,b.bits=1,0;for(p=1;p<R&&ae[p]===0;p++);for(Y<p&&(Y=p),C=K=1;C<=15;C++)if(K<<=1,(K-=ae[C])<0)return-1;if(0<K&&(u===0||R!==1))return-1;for(te[1]=0,C=1;C<15;C++)te[C+1]=te[C]+ae[C];for(j=0;j<h;j++)f[g+j]!==0&&(w[te[f[g+j]]++]=j);if(N=u===0?(L=ge=w,19):u===1?(L=i,le-=257,ge=a,W-=257,256):(L=c,ge=d,-1),C=p,G=m,ee=j=F=0,B=-1,P=(oe=1<<(D=Y))-1,u===1&&852<oe||u===2&&592<oe)return 1;for(;;){for(Z=C-ee,q=w[j]<N?(M=0,w[j]):w[j]>N?(M=ge[W+w[j]],L[le+w[j]]):(M=96,0),x=1<<C-ee,p=k=1<<D;_[G+(F>>ee)+(k-=x)]=Z<<24|M<<16|q|0,k!==0;);for(x=1<<C-1;F&x;)x>>=1;if(x!==0?(F&=x-1,F+=x):F=0,j++,--ae[C]==0){if(C===R)break;C=f[g+w[j]]}if(Y<C&&(F&P)!==B){for(ee===0&&(ee=Y),G+=p,K=1<<(D=C-ee);D+ee<R&&!((K-=ae[D+ee])<=0);)D++,K<<=1;if(oe+=1<<D,u===1&&852<oe||u===2&&592<oe)return 1;_[B=F&P]=Y<<24|D<<16|G-m|0}}return F!==0&&(_[G+F]=C-ee<<24|64<<16|0),b.bits=Y,0}},{"../utils/common":41}],51:[function(n,s,r){s.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(n,s,r){var o=n("../utils/common"),i=0,a=1;function c(v){for(var E=v.length;0<=--E;)v[E]=0}var d=0,u=29,f=256,g=f+1+u,h=30,_=19,m=2*g+1,w=15,b=16,x=7,k=256,B=16,P=17,G=18,N=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Z=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],M=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],q=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ie=new Array(2*(g+2));c(ie);var C=new Array(2*h);c(C);var j=new Array(512);c(j);var p=new Array(256);c(p);var R=new Array(u);c(R);var Y,D,ee,K=new Array(h);function oe(v,E,$,z,I){this.static_tree=v,this.extra_bits=E,this.extra_base=$,this.elems=z,this.max_length=I,this.has_stree=v&&v.length}function F(v,E){this.dyn_tree=v,this.max_code=0,this.stat_desc=E}function L(v){return v<256?j[v]:j[256+(v>>>7)]}function le(v,E){v.pending_buf[v.pending++]=255&E,v.pending_buf[v.pending++]=E>>>8&255}function ae(v,E,$){v.bi_valid>b-$?(v.bi_buf|=E<<v.bi_valid&65535,le(v,v.bi_buf),v.bi_buf=E>>b-v.bi_valid,v.bi_valid+=$-b):(v.bi_buf|=E<<v.bi_valid&65535,v.bi_valid+=$)}function te(v,E,$){ae(v,$[2*E],$[2*E+1])}function ge(v,E){for(var $=0;$|=1&v,v>>>=1,$<<=1,0<--E;);return $>>>1}function W(v,E,$){var z,I,J=new Array(w+1),U=0;for(z=1;z<=w;z++)J[z]=U=U+$[z-1]<<1;for(I=0;I<=E;I++){var H=v[2*I+1];H!==0&&(v[2*I]=ge(J[H]++,H))}}function X(v){var E;for(E=0;E<g;E++)v.dyn_ltree[2*E]=0;for(E=0;E<h;E++)v.dyn_dtree[2*E]=0;for(E=0;E<_;E++)v.bl_tree[2*E]=0;v.dyn_ltree[2*k]=1,v.opt_len=v.static_len=0,v.last_lit=v.matches=0}function V(v){8<v.bi_valid?le(v,v.bi_buf):0<v.bi_valid&&(v.pending_buf[v.pending++]=v.bi_buf),v.bi_buf=0,v.bi_valid=0}function se(v,E,$,z){var I=2*E,J=2*$;return v[I]<v[J]||v[I]===v[J]&&z[E]<=z[$]}function fe(v,E,$){for(var z=v.heap[$],I=$<<1;I<=v.heap_len&&(I<v.heap_len&&se(E,v.heap[I+1],v.heap[I],v.depth)&&I++,!se(E,z,v.heap[I],v.depth));)v.heap[$]=v.heap[I],$=I,I<<=1;v.heap[$]=z}function ve(v,E,$){var z,I,J,U,H=0;if(v.last_lit!==0)for(;z=v.pending_buf[v.d_buf+2*H]<<8|v.pending_buf[v.d_buf+2*H+1],I=v.pending_buf[v.l_buf+H],H++,z===0?te(v,I,E):(te(v,(J=p[I])+f+1,E),(U=N[J])!==0&&ae(v,I-=R[J],U),te(v,J=L(--z),$),(U=Z[J])!==0&&ae(v,z-=K[J],U)),H<v.last_lit;);te(v,k,E)}function S(v,E){var $,z,I,J=E.dyn_tree,U=E.stat_desc.static_tree,H=E.stat_desc.has_stree,Q=E.stat_desc.elems,ce=-1;for(v.heap_len=0,v.heap_max=m,$=0;$<Q;$++)J[2*$]!==0?(v.heap[++v.heap_len]=ce=$,v.depth[$]=0):J[2*$+1]=0;for(;v.heap_len<2;)J[2*(I=v.heap[++v.heap_len]=ce<2?++ce:0)]=1,v.depth[I]=0,v.opt_len--,H&&(v.static_len-=U[2*I+1]);for(E.max_code=ce,$=v.heap_len>>1;1<=$;$--)fe(v,J,$);for(I=Q;$=v.heap[1],v.heap[1]=v.heap[v.heap_len--],fe(v,J,1),z=v.heap[1],v.heap[--v.heap_max]=$,v.heap[--v.heap_max]=z,J[2*I]=J[2*$]+J[2*z],v.depth[I]=(v.depth[$]>=v.depth[z]?v.depth[$]:v.depth[z])+1,J[2*$+1]=J[2*z+1]=I,v.heap[1]=I++,fe(v,J,1),2<=v.heap_len;);v.heap[--v.heap_max]=v.heap[1],function(ue,me){var Re,Ae,Fe,ye,ft,Kt,Te=me.dyn_tree,Xe=me.max_code,jn=me.stat_desc.static_tree,Un=me.stat_desc.has_stree,Oa=me.stat_desc.extra_bits,zr=me.stat_desc.extra_base,fn=me.stat_desc.max_length,Gn=0;for(ye=0;ye<=w;ye++)ue.bl_count[ye]=0;for(Te[2*ue.heap[ue.heap_max]+1]=0,Re=ue.heap_max+1;Re<m;Re++)fn<(ye=Te[2*Te[2*(Ae=ue.heap[Re])+1]+1]+1)&&(ye=fn,Gn++),Te[2*Ae+1]=ye,Xe<Ae||(ue.bl_count[ye]++,ft=0,zr<=Ae&&(ft=Oa[Ae-zr]),Kt=Te[2*Ae],ue.opt_len+=Kt*(ye+ft),Un&&(ue.static_len+=Kt*(jn[2*Ae+1]+ft)));if(Gn!==0){do{for(ye=fn-1;ue.bl_count[ye]===0;)ye--;ue.bl_count[ye]--,ue.bl_count[ye+1]+=2,ue.bl_count[fn]--,Gn-=2}while(0<Gn);for(ye=fn;ye!==0;ye--)for(Ae=ue.bl_count[ye];Ae!==0;)Xe<(Fe=ue.heap[--Re])||(Te[2*Fe+1]!==ye&&(ue.opt_len+=(ye-Te[2*Fe+1])*Te[2*Fe],Te[2*Fe+1]=ye),Ae--)}}(v,E),W(J,ce,v.bl_count)}function l(v,E,$){var z,I,J=-1,U=E[1],H=0,Q=7,ce=4;for(U===0&&(Q=138,ce=3),E[2*($+1)+1]=65535,z=0;z<=$;z++)I=U,U=E[2*(z+1)+1],++H<Q&&I===U||(H<ce?v.bl_tree[2*I]+=H:I!==0?(I!==J&&v.bl_tree[2*I]++,v.bl_tree[2*B]++):H<=10?v.bl_tree[2*P]++:v.bl_tree[2*G]++,J=I,ce=(H=0)===U?(Q=138,3):I===U?(Q=6,3):(Q=7,4))}function A(v,E,$){var z,I,J=-1,U=E[1],H=0,Q=7,ce=4;for(U===0&&(Q=138,ce=3),z=0;z<=$;z++)if(I=U,U=E[2*(z+1)+1],!(++H<Q&&I===U)){if(H<ce)for(;te(v,I,v.bl_tree),--H!=0;);else I!==0?(I!==J&&(te(v,I,v.bl_tree),H--),te(v,B,v.bl_tree),ae(v,H-3,2)):H<=10?(te(v,P,v.bl_tree),ae(v,H-3,3)):(te(v,G,v.bl_tree),ae(v,H-11,7));J=I,ce=(H=0)===U?(Q=138,3):I===U?(Q=6,3):(Q=7,4)}}c(K);var T=!1;function y(v,E,$,z){ae(v,(d<<1)+(z?1:0),3),function(I,J,U,H){V(I),H&&(le(I,U),le(I,~U)),o.arraySet(I.pending_buf,I.window,J,U,I.pending),I.pending+=U}(v,E,$,!0)}r._tr_init=function(v){T||(function(){var E,$,z,I,J,U=new Array(w+1);for(I=z=0;I<u-1;I++)for(R[I]=z,E=0;E<1<<N[I];E++)p[z++]=I;for(p[z-1]=I,I=J=0;I<16;I++)for(K[I]=J,E=0;E<1<<Z[I];E++)j[J++]=I;for(J>>=7;I<h;I++)for(K[I]=J<<7,E=0;E<1<<Z[I]-7;E++)j[256+J++]=I;for($=0;$<=w;$++)U[$]=0;for(E=0;E<=143;)ie[2*E+1]=8,E++,U[8]++;for(;E<=255;)ie[2*E+1]=9,E++,U[9]++;for(;E<=279;)ie[2*E+1]=7,E++,U[7]++;for(;E<=287;)ie[2*E+1]=8,E++,U[8]++;for(W(ie,g+1,U),E=0;E<h;E++)C[2*E+1]=5,C[2*E]=ge(E,5);Y=new oe(ie,N,f+1,g,w),D=new oe(C,Z,0,h,w),ee=new oe(new Array(0),M,0,_,x)}(),T=!0),v.l_desc=new F(v.dyn_ltree,Y),v.d_desc=new F(v.dyn_dtree,D),v.bl_desc=new F(v.bl_tree,ee),v.bi_buf=0,v.bi_valid=0,X(v)},r._tr_stored_block=y,r._tr_flush_block=function(v,E,$,z){var I,J,U=0;0<v.level?(v.strm.data_type===2&&(v.strm.data_type=function(H){var Q,ce=4093624447;for(Q=0;Q<=31;Q++,ce>>>=1)if(1&ce&&H.dyn_ltree[2*Q]!==0)return i;if(H.dyn_ltree[18]!==0||H.dyn_ltree[20]!==0||H.dyn_ltree[26]!==0)return a;for(Q=32;Q<f;Q++)if(H.dyn_ltree[2*Q]!==0)return a;return i}(v)),S(v,v.l_desc),S(v,v.d_desc),U=function(H){var Q;for(l(H,H.dyn_ltree,H.l_desc.max_code),l(H,H.dyn_dtree,H.d_desc.max_code),S(H,H.bl_desc),Q=_-1;3<=Q&&H.bl_tree[2*q[Q]+1]===0;Q--);return H.opt_len+=3*(Q+1)+5+5+4,Q}(v),I=v.opt_len+3+7>>>3,(J=v.static_len+3+7>>>3)<=I&&(I=J)):I=J=$+5,$+4<=I&&E!==-1?y(v,E,$,z):v.strategy===4||J===I?(ae(v,2+(z?1:0),3),ve(v,ie,C)):(ae(v,4+(z?1:0),3),function(H,Q,ce,ue){var me;for(ae(H,Q-257,5),ae(H,ce-1,5),ae(H,ue-4,4),me=0;me<ue;me++)ae(H,H.bl_tree[2*q[me]+1],3);A(H,H.dyn_ltree,Q-1),A(H,H.dyn_dtree,ce-1)}(v,v.l_desc.max_code+1,v.d_desc.max_code+1,U+1),ve(v,v.dyn_ltree,v.dyn_dtree)),X(v),z&&V(v)},r._tr_tally=function(v,E,$){return v.pending_buf[v.d_buf+2*v.last_lit]=E>>>8&255,v.pending_buf[v.d_buf+2*v.last_lit+1]=255&E,v.pending_buf[v.l_buf+v.last_lit]=255&$,v.last_lit++,E===0?v.dyn_ltree[2*$]++:(v.matches++,E--,v.dyn_ltree[2*(p[$]+f+1)]++,v.dyn_dtree[2*L(E)]++),v.last_lit===v.lit_bufsize-1},r._tr_align=function(v){ae(v,2,3),te(v,k,ie),function(E){E.bi_valid===16?(le(E,E.bi_buf),E.bi_buf=0,E.bi_valid=0):8<=E.bi_valid&&(E.pending_buf[E.pending++]=255&E.bi_buf,E.bi_buf>>=8,E.bi_valid-=8)}(v)}},{"../utils/common":41}],53:[function(n,s,r){s.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(n,s,r){(function(o){(function(i,a){if(!i.setImmediate){var c,d,u,f,g=1,h={},_=!1,m=i.document,w=Object.getPrototypeOf&&Object.getPrototypeOf(i);w=w&&w.setTimeout?w:i,c={}.toString.call(i.process)==="[object process]"?function(B){process.nextTick(function(){x(B)})}:function(){if(i.postMessage&&!i.importScripts){var B=!0,P=i.onmessage;return i.onmessage=function(){B=!1},i.postMessage("","*"),i.onmessage=P,B}}()?(f="setImmediate$"+Math.random()+"$",i.addEventListener?i.addEventListener("message",k,!1):i.attachEvent("onmessage",k),function(B){i.postMessage(f+B,"*")}):i.MessageChannel?((u=new MessageChannel).port1.onmessage=function(B){x(B.data)},function(B){u.port2.postMessage(B)}):m&&"onreadystatechange"in m.createElement("script")?(d=m.documentElement,function(B){var P=m.createElement("script");P.onreadystatechange=function(){x(B),P.onreadystatechange=null,d.removeChild(P),P=null},d.appendChild(P)}):function(B){setTimeout(x,0,B)},w.setImmediate=function(B){typeof B!="function"&&(B=new Function(""+B));for(var P=new Array(arguments.length-1),G=0;G<P.length;G++)P[G]=arguments[G+1];var N={callback:B,args:P};return h[g]=N,c(g),g++},w.clearImmediate=b}function b(B){delete h[B]}function x(B){if(_)setTimeout(x,0,B);else{var P=h[B];if(P){_=!0;try{(function(G){var N=G.callback,Z=G.args;switch(Z.length){case 0:N();break;case 1:N(Z[0]);break;case 2:N(Z[0],Z[1]);break;case 3:N(Z[0],Z[1],Z[2]);break;default:N.apply(a,Z)}})(P)}finally{b(B),_=!1}}}}function k(B){B.source===i&&typeof B.data=="string"&&B.data.indexOf(f)===0&&x(+B.data.slice(f.length))}})(typeof self>"u"?o===void 0?this:o:self)}).call(this,typeof Kn<"u"?Kn:typeof self<"u"?self:typeof window<"u"?window:{})},{}]},{},[10])(10)})})(ga);var va=ga.exports;const _a=$f(va),zf=Ta({__proto__:null,default:_a},[va]);async function Df(e){const t=Rr(e.name);if(t==="zip")return await Mf(e);throw new Error(`只支持ZIP文件格式，当前文件格式: .${t}`)}async function Mf(e){const t=e.name.toLowerCase();if(t.includes("kof")||t.includes("拳皇")||t.includes("king")||t.includes("fighter")||t.includes("neogeo")||t.includes("snk")||t.includes("mvs")||t.includes("arcade")||t.includes("97")||t.includes("98")||t.includes("2002")||t.includes("街机")){const a=await e.arrayBuffer();return[{name:e.name,extension:"zip",data:a,size:a.byteLength,system:"arcade"}]}const s=ma(),r=[],o=[],i=[];try{const c=await new _a().loadAsync(e);for(const[d,u]of Object.entries(c.files)){if(u.dir)continue;o.push(d);const f=Rr(d);if(s.includes(f)){const g=await u.async("arraybuffer");r.push({name:d,extension:f,data:g,size:g.byteLength})}else i.push(d)}if(r.length===0){let d=`ZIP文件中未找到支持的游戏文件格式！

`;throw o.length===0?d+="ZIP文件为空或只包含文件夹。":(d+=`发现的文件：
${o.join(`
`)}

`,d+=`支持的格式：${s.map(u=>"."+u).join(", ")}

`,d+="请确保ZIP文件包含正确格式的游戏文件。"),new Error(d)}return r}catch(a){throw a instanceof Error?a.message.includes("ZIP文件中未找到支持的游戏文件格式")?a:new Error(`解压ZIP文件失败: ${a.message}`):new Error("解压ZIP文件失败: 未知错误")}}function Rr(e){var t;return((t=e.split(".").pop())==null?void 0:t.toLowerCase())||""}function ya(e){if(e===0)return"0 Bytes";const t=1024,n=["Bytes","KB","MB","GB"],s=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,s)).toFixed(2))+" "+n[s]}async function Lf(){try{const n=await fetch("/games/games.json");if(n.ok){const s=await n.json();return console.log("从配置文件加载游戏列表:",s),s.games||[]}}catch{console.log("无法加载游戏配置文件，尝试自动发现游戏")}const e=[{name:"新超级马里奥",path:"/games/新超级马里奥.zip"},{name:"星之卡比",path:"/games/星之卡比.zip"}],t=[];for(const n of e)try{(await fetch(n.path,{method:"HEAD"})).ok&&(t.push(n),console.log(`发现游戏: ${n.name}`))}catch{console.log(`游戏文件不存在: ${n.path}`)}return t}async function Ff(e,t){try{console.log("正在加载本地游戏:",t,"路径:",e);const n=await fetch(e);if(!n.ok)return console.warn("未找到游戏文件:",e),null;const s=await n.arrayBuffer(),r=Rr(t);if(r==="zip"){const o=(await pa(()=>Promise.resolve().then(()=>zf),void 0)).default,a=await new o().loadAsync(s);console.log("ZIP文件内容:",Object.keys(a.files));const c=["nds","gb","gbc","gba","nes","smc","sfc","md","gen","n64","v64","z64"];for(const d of c)for(const[u,f]of Object.entries(a.files))if(!f.dir&&u.toLowerCase().endsWith(`.${d}`)){const g=await f.async("arraybuffer");return console.log("找到游戏文件:",u,"大小:",g.byteLength),{name:u,extension:d,data:g,size:g.byteLength}}throw new Error("ZIP文件中未找到支持的游戏文件")}else return{name:t,extension:r,data:s,size:s.byteLength}}catch(n){return console.warn("无法加载游戏:",t,n),null}}class Nf{constructor(){vt(this,"dbName","GameEmulatorSaves");vt(this,"dbVersion",1);vt(this,"db",null);vt(this,"autoSaveInterval",null);vt(this,"currentGame",null);vt(this,"gameStartTime",null)}async init(){return new Promise((t,n)=>{const s=indexedDB.open(this.dbName,this.dbVersion);s.onerror=()=>n(s.error),s.onsuccess=()=>{this.db=s.result,t()},s.onupgradeneeded=r=>{const o=r.target.result;if(!o.objectStoreNames.contains("gameProgress")){const i=o.createObjectStore("gameProgress",{keyPath:"gameName"});i.createIndex("gameSystem","gameSystem",{unique:!1}),i.createIndex("lastPlayTime","lastPlayTime",{unique:!1})}if(!o.objectStoreNames.contains("saveStates")){const i=o.createObjectStore("saveStates",{keyPath:"id"});i.createIndex("gameName","gameName",{unique:!1}),i.createIndex("saveTime","saveTime",{unique:!1}),i.createIndex("autoSave","autoSave",{unique:!1})}}})}startGameSession(t,n){this.currentGame=t,this.gameStartTime=new Date,this.startAutoSave(),console.log(`🎮 开始游戏会话: ${t}`,{currentGame:this.currentGame,gameStartTime:this.gameStartTime})}getCurrentGameStatus(){const t=window.EJS_emulator,n=t==null?void 0:t.gameManager;return{currentGame:this.currentGame,gameStartTime:this.gameStartTime,autoSaveInterval:this.autoSaveInterval,emulatorExists:!!t,gameManagerExists:!!n,emulatorMethods:t?Object.keys(t).filter(s=>typeof t[s]=="function"):[],emulatorProperties:t?Object.keys(t):[],gameManagerMethods:n?Object.keys(n).filter(s=>typeof n[s]=="function"):[],gameManagerProperties:n?Object.keys(n):[],getStateAvailable:!!(n!=null&&n.getState),loadStateAvailable:!!(n!=null&&n.loadState),supportsStates:n?n.supportsStates():!1}}async endGameSession(){if(!this.currentGame||!this.gameStartTime)return;this.stopAutoSave();const t=(new Date().getTime()-this.gameStartTime.getTime())/1e3;await this.updateGameProgress(this.currentGame,t),console.log(`🎮 结束游戏会话: ${this.currentGame}, 游戏时长: ${Math.round(t)}秒`),this.currentGame=null,this.gameStartTime=null}startAutoSave(){this.stopAutoSave(),this.autoSaveInterval=window.setInterval(async()=>{if(this.currentGame&&window.EJS_emulator)try{await this.autoSaveGame()}catch(t){console.warn("自动保存失败:",t)}},5e3)}stopAutoSave(){this.autoSaveInterval&&(clearInterval(this.autoSaveInterval),this.autoSaveInterval=null)}async autoSaveGame(){if(!this.currentGame)return;const t=window.EJS_emulator;if(!(!t||!t.gameManager||!t.gameManager.getState))try{const n=t.gameManager.getState();if(n){const s=await this.getGameProgress(this.currentGame);s!=null&&s.autoSave&&await this.deleteSaveState(s.autoSave.id);const r={id:`${this.currentGame}_auto`,gameName:this.currentGame,gameSystem:this.getCurrentGameSystem(),saveData:n.buffer,saveTime:new Date,autoSave:!0,description:"自动保存"};await this.saveSaveState(r),await this.updateAutoSave(this.currentGame,r),console.log(`💾 自动保存完成: ${this.currentGame}`)}}catch(n){console.warn("自动保存失败:",n)}}async manualSaveGame(t,n){const s=this.currentGame||n;if(!s)throw console.error("保存状态调试信息:",{currentGame:this.currentGame,providedGameName:n,emulatorExists:!!window.EJS_emulator}),new Error("没有正在进行的游戏");try{const r=window.EJS_emulator;if(!r||!r.gameManager)throw new Error("EmulatorJS gameManager 不可用");console.log("🎮 开始手动保存游戏状态...");const o=r.gameManager.getState();console.log("📦 获取到保存数据:",o);const i=await this.captureScreenshot(),a={id:`${s}_manual_${Date.now()}`,gameName:s,gameSystem:this.getCurrentGameSystem(),saveData:o.buffer,screenshot:i,saveTime:new Date,autoSave:!1,description:t||`手动保存 ${new Date().toLocaleString()}`};return await this.saveSaveState(a),await this.addSaveStateToProgress(s,a),console.log("💾 保存状态成功:",a.id),a}catch(r){throw console.error("保存状态失败:",r),r}}async quickSaveGame(t){const n=this.currentGame||t;if(!n)return null;try{const s=window.EJS_emulator;if(!s||!s.gameManager)return console.warn("EmulatorJS gameManager 不可用，快速保存失败"),null;console.log("⚡ 开始快速保存游戏状态...");const r=s.gameManager.getState();console.log("📦 获取到快速保存数据:",r);const o=await this.captureScreenshot(),i={id:`${n}_quick`,gameName:n,gameSystem:this.getCurrentGameSystem(),saveData:r.buffer,screenshot:o,saveTime:new Date,autoSave:!1,description:"快速保存"};return await this.saveSaveState(i),await this.updateQuickSave(n,i),console.log(`⚡ 快速保存完成: ${n}`),i}catch(s){return console.error("快速保存失败:",s),null}}async loadSaveState(t){const n=window.EJS_emulator;if(!n||!n.gameManager)throw new Error("模拟器未初始化");if(!n.gameManager.loadState)throw new Error("模拟器不支持读档功能");if(!n.started)throw new Error("请先启动游戏再读取存档");try{const s=await this.getSaveState(t);if(!s)throw new Error("存档不存在");const r=new Uint8Array(s.saveData);return console.log(`📂 开始读档: ${s.description}`,{dataSize:r.length,gameStarted:n.started,hasGameManager:!!n.gameManager,hasLoadState:!!n.gameManager.loadState}),n.gameManager.loadState(r),console.log(`📂 读档完成: ${s.description}`),!0}catch(s){throw console.error("读档失败:",s),s}}async getGameProgress(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction(["gameProgress"],"readonly").objectStore("gameProgress").get(t);i.onsuccess=()=>n(i.result||null),i.onerror=()=>s(i.error)})}async hasGameProgress(t){const n=await this.getGameProgress(t);return n!==null&&(n.quickSave!==void 0||n.autoSave!==void 0||n.saveStates.length>0)}async getLatestSaveState(t){const n=await this.getGameProgress(t);return n?n.quickSave?n.quickSave:n.autoSave?n.autoSave:n.saveStates.length>0?n.saveStates.sort((s,r)=>new Date(r.saveTime).getTime()-new Date(s.saveTime).getTime())[0]:null:null}async deleteSaveState(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction(["saveStates"],"readwrite").objectStore("saveStates").delete(t);i.onsuccess=()=>n(),i.onerror=()=>s(i.error)})}async exportSaveState(t){const n=await this.getSaveState(t);if(!n)throw new Error("存档不存在");const s={version:"1.0",saveState:{...n,saveData:Array.from(new Uint8Array(n.saveData))}},r=JSON.stringify(s,null,2);return new Blob([r],{type:"application/json"})}async importSaveState(t){const n=await t.text(),s=JSON.parse(n);if(!s.version||!s.saveState)throw new Error("无效的存档文件格式");const r={...s.saveState,id:`${s.saveState.gameName}_import_${Date.now()}`,saveData:new Uint8Array(s.saveState.saveData).buffer,saveTime:new Date(s.saveState.saveTime)};return await this.saveSaveState(r),await this.addSaveStateToProgress(r.gameName,r),console.log(`📥 导入存档完成: ${r.gameName}`),r}async saveSaveState(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction(["saveStates"],"readwrite").objectStore("saveStates").put(t);i.onsuccess=()=>n(),i.onerror=()=>s(i.error)})}async getSaveState(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction(["saveStates"],"readonly").objectStore("saveStates").get(t);i.onsuccess=()=>n(i.result||null),i.onerror=()=>s(i.error)})}async updateGameProgress(t,n){const s=await this.getGameProgress(t)||{gameName:t,gameSystem:this.getCurrentGameSystem(),lastPlayTime:new Date,totalPlayTime:0,saveStates:[]};s.lastPlayTime=new Date,s.totalPlayTime+=n,await this.saveGameProgress(s)}async saveGameProgress(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction(["gameProgress"],"readwrite").objectStore("gameProgress").put(t);i.onsuccess=()=>n(),i.onerror=()=>s(i.error)})}async addSaveStateToProgress(t,n){const s=await this.getGameProgress(t)||{gameName:t,gameSystem:this.getCurrentGameSystem(),lastPlayTime:new Date,totalPlayTime:0,saveStates:[]};s.saveStates.push(n),s.saveStates=s.saveStates.filter(r=>!r.autoSave).sort((r,o)=>new Date(o.saveTime).getTime()-new Date(r.saveTime).getTime()).slice(0,10),await this.saveGameProgress(s)}async updateQuickSave(t,n){const s=await this.getGameProgress(t)||{gameName:t,gameSystem:this.getCurrentGameSystem(),lastPlayTime:new Date,totalPlayTime:0,saveStates:[]};s.quickSave=n,await this.saveGameProgress(s)}async updateAutoSave(t,n){const s=await this.getGameProgress(t)||{gameName:t,gameSystem:this.getCurrentGameSystem(),lastPlayTime:new Date,totalPlayTime:0,saveStates:[]};s.autoSave=n,await this.saveGameProgress(s)}async removeQuickSave(t){const n=await this.getGameProgress(t);n&&n.quickSave&&(n.quickSave=void 0,await this.saveGameProgress(n))}async removeAutoSave(t){const n=await this.getGameProgress(t);n&&n.autoSave&&(n.autoSave=void 0,await this.saveGameProgress(n))}async removeManualSave(t,n){const s=await this.getGameProgress(t);if(s){const r=s.saveStates.findIndex(o=>o.id===n);r!==-1&&(s.saveStates.splice(r,1),await this.saveGameProgress(s))}}getCurrentGameSystem(){return window.EJS_system||"unknown"}async captureScreenshot(){try{const t=document.querySelector("#canvas canvas");if(t)return t.toDataURL("image/png")}catch(t){console.warn("截图失败:",t)}}}const ze=new Nf,jf={class:"game-selector"},Uf={class:"games-grid"},Gf=["onClick"],Jf={class:"game-icon"},Hf=["src","alt"],Wf={key:1},Vf={class:"game-name"},qf={key:0,class:"game-description"},Kf={key:1,class:"game-system"},Zf={key:2,class:"save-indicator"},Yf={key:0,class:"loading"},Xf=je({__name:"GameSelector",emits:["gameSelected","error"],setup(e,{emit:t}){const n=t,s=be([]),r=be(!1),o=be({});async function i(){try{await ze.init();const d={};for(const u of s.value)d[u.name]=await ze.hasGameProgress(u.name);o.value=d}catch(d){console.error("检查游戏进度失败:",d)}}ut(async()=>{try{s.value=await Lf(),await i()}catch(d){console.error("获取游戏列表失败:",d),n("error","获取游戏列表失败")}});function a(d){return{nds:"Nintendo DS",gb:"Game Boy",gbc:"Game Boy Color",gba:"Game Boy Advance",nes:"Nintendo Entertainment System",snes:"Super Nintendo",md:"Sega Genesis",sms:"Sega Master System",n64:"Nintendo 64",psx:"PlayStation"}[d]||d.toUpperCase()}async function c(d){r.value=!0;try{const u=await Ff(d.path,d.name);u?(u.system=d.system,n("gameSelected",u)):n("error",`无法加载游戏: ${d.name}`)}catch(u){console.error("加载游戏失败:",u),n("error",`加载游戏失败: ${u.message}`)}finally{r.value=!1}}return(d,u)=>(ne(),re("div",jf,[u[2]||(u[2]=O("h3",null,[O("img",{src:If,alt:"游戏图标",class:"inline-block w-6 h-6 mr-2"}),we(" 选择游戏 ")],-1)),O("div",Uf,[(ne(!0),re(De,null,mt(s.value,f=>(ne(),re("div",{key:f.path,class:"relative game-card",onClick:g=>c(f)},[O("div",Jf,[f.icon&&f.icon.startsWith("/images/")?(ne(),re("img",{key:0,src:f.icon,alt:f.name,class:"game-icon-img"},null,8,Hf)):(ne(),re("span",Wf,de(f.icon||"🎮"),1))]),O("div",Vf,de(f.name),1),f.description?(ne(),re("div",qf,de(f.description),1)):Se("",!0),f.system?(ne(),re("div",Kf,de(a(f.system)),1)):Se("",!0),o.value[f.name]?(ne(),re("div",Zf,u[0]||(u[0]=[O("span",{class:"save-icon"},"💾",-1),O("span",{class:"save-text"},"有存档",-1)]))):Se("",!0)],8,Gf))),128))]),r.value?(ne(),re("div",Yf,u[1]||(u[1]=[O("div",{class:"loading-spinner"},null,-1),O("p",null,"正在加载游戏...",-1)]))):Se("",!0)]))}});const Qf=Fn(Xf,[["__scopeId","data-v-52a35266"]]),ed={key:0,class:"fixed top-4 right-4 z-50 max-w-md w-[80%] mx-4"},td={class:"flex items-start gap-3"},nd={class:"flex-shrink-0 mt-0.5"},sd={class:"flex-1 min-w-0"},rd={key:0,class:"mb-1 font-semibold text-white"},od={class:"text-sm text-gray-300 whitespace-pre-line"},id=je({__name:"Toast",props:{type:{default:"info"},title:{},message:{},duration:{default:5e3},persistent:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){const n=e,s=t,r=be(!1),o={success:"border-l-green-500 bg-green-500/10",error:"border-l-red-500 bg-red-500/10",warning:"border-l-yellow-500 bg-yellow-500/10",info:"border-l-blue-500 bg-blue-500/10"},i=Je(()=>({success:()=>nt("svg",{class:"w-5 h-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[nt("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),error:()=>nt("svg",{class:"w-5 h-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[nt("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})]),warning:()=>nt("svg",{class:"w-5 h-5 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[nt("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})]),info:()=>nt("svg",{class:"w-5 h-5 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[nt("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])})[n.type]);function a(){r.value=!1,setTimeout(()=>{s("close")},200)}return ut(()=>{r.value=!0,!n.persistent&&n.duration>0&&setTimeout(()=>{a()},n.duration)}),(c,d)=>(ne(),rr(xl,{to:"body"},[_e(Bc,{"enter-active-class":"transition-all duration-300 ease-out","enter-from-class":"scale-95 translate-y-2 opacity-0","enter-to-class":"scale-100 translate-y-0 opacity-100","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"scale-100 translate-y-0 opacity-100","leave-to-class":"scale-95 translate-y-2 opacity-0"},{default:qe(()=>[r.value?(ne(),re("div",ed,[O("div",{class:Le(["card p-4 shadow-2xl border-l-4",o[c.type]])},[O("div",td,[O("div",nd,[(ne(),rr(Dl(i.value),{class:"w-5 h-5"}))]),O("div",sd,[c.title?(ne(),re("h4",rd,de(c.title),1)):Se("",!0),O("div",od,de(c.message),1)]),O("button",{onClick:a,class:"flex-shrink-0 text-gray-400 transition-colors hover:text-white"},d[0]||(d[0]=[O("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])],2)])):Se("",!0)]),_:1})]))}});class ad{constructor(){vt(this,"toasts",[])}show(t){const n=Math.random().toString(36).substr(2,9),s=document.createElement("div");s.id=`toast-${n}`,document.body.appendChild(s);const r=ta({render(){return nt(id,{...t,onClose:()=>{this.remove(n)}})}});return r.mount(s),this.toasts.push({id:n,app:r}),n}remove(t){const n=this.toasts.findIndex(s=>s.id===t);if(n>-1){const{app:s}=this.toasts[n];s.unmount();const r=document.getElementById(`toast-${t}`);r&&r.remove(),this.toasts.splice(n,1)}}success(t,n){return this.show({type:"success",message:t,...n})}error(t,n){return this.show({type:"error",message:t,duration:8e3,...n})}warning(t,n){return this.show({type:"warning",message:t,...n})}info(t,n){return this.show({type:"info",message:t,...n})}clear(){this.toasts.forEach(({id:t})=>{this.remove(t)})}}const xe=new ad,ld={class:"w-full space-y-4"},cd={class:"p-6 text-center space-y-3"},ud={key:0,class:"card p-4"},fd={class:"flex flex-wrap gap-1.5"},dd={key:1,class:"card p-4"},hd=je({__name:"FileUploader",emits:["files-selected"],setup(e,{emit:t}){const n=t,s=be(),r=be(!1),o=be([]);ut(()=>{o.value=ma()});async function i(d){if(d.length!==0){r.value=!0;try{const u=[];for(const f of Array.from(d))try{const g=await Df(f);u.push(...g)}catch(g){console.error(`Error processing file ${f.name}:`,g),g instanceof Error?xe.error(g.message,{title:`处理文件 ${f.name} 时出错`,duration:1e4,persistent:!1}):xe.error(`处理文件 ${f.name} 时出错：未知错误`);return}u.length>0?(n("files-selected",u),xe.success(`成功加载 ${u.length} 个游戏文件`)):xe.warning("未找到支持的游戏文件！")}catch(u){console.error("Error processing files:",u),xe.error("处理文件时出错，请重试。")}finally{r.value=!1}}}function a(d){const u=d.target;u.files&&i(u.files)}function c(d){var u;d.preventDefault(),(u=d.dataTransfer)!=null&&u.files&&i(d.dataTransfer.files)}return(d,u)=>(ne(),re("div",ld,[O("div",{class:"card border-2 border-dashed border-gray-600 hover:border-blue-500 transition-colors duration-300 cursor-pointer group",onDrop:c,onDragover:u[1]||(u[1]=Et(()=>{},["prevent"])),onDragenter:u[2]||(u[2]=Et(()=>{},["prevent"])),onClick:u[3]||(u[3]=f=>{var g;return(g=s.value)==null?void 0:g.click()})},[O("div",cd,[u[5]||(u[5]=O("div",{class:"text-4xl mb-2 group-hover:scale-110 transition-transform duration-300"}," 📁 ",-1)),u[6]||(u[6]=O("h3",{class:"text-lg font-semibold text-white mb-1"}," 将游戏ZIP文件拖拽到这里 ",-1)),u[7]||(u[7]=O("p",{class:"text-gray-400 text-sm mb-4"},"或点击选择ZIP文件",-1)),O("input",{ref_key:"fileInput",ref:s,type:"file",multiple:"",accept:".zip",onChange:a,class:"hidden"},null,544),O("button",{type:"button",class:"btn-primary inline-flex items-center gap-2 text-sm",onClick:u[0]||(u[0]=Et(f=>{var g;return(g=s.value)==null?void 0:g.click()},["stop"]))},u[4]||(u[4]=[O("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),we(" 选择ZIP文件 ",-1)]))])],32),o.value.length>0?(ne(),re("div",ud,[u[8]||(u[8]=O("h4",{class:"text-sm font-semibold text-white mb-3 flex items-center gap-2"},[O("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),we(" 上传ZIP文件，支持解压以下游戏格式： ")],-1)),O("div",fd,[(ne(!0),re(De,null,mt(o.value,f=>(ne(),re("span",{key:f,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30"}," ."+de(f),1))),128))])])):Se("",!0),r.value?(ne(),re("div",dd,u[9]||(u[9]=[O("div",{class:"flex items-center justify-center space-x-3"},[O("div",{class:"loading-spinner"}),O("p",{class:"text-white font-medium text-sm"},"正在处理文件...")],-1)]))):Se("",!0)]))}}),pd="GameEmulatorDB",md=1,$e="games";class gd{constructor(){vt(this,"db",null)}async init(){return new Promise((t,n)=>{const s=indexedDB.open(pd,md);s.onerror=()=>{console.error("IndexedDB 打开失败:",s.error),n(s.error)},s.onsuccess=()=>{this.db=s.result,console.log("IndexedDB 初始化成功"),t()},s.onupgradeneeded=r=>{const o=r.target.result;if(!o.objectStoreNames.contains($e)){const i=o.createObjectStore($e,{keyPath:"id"});i.createIndex("name","name",{unique:!1}),i.createIndex("timestamp","timestamp",{unique:!1}),console.log("IndexedDB 对象存储创建成功")}}})}async saveGame(t){this.db||await this.init();const n=await this.findExistingGame(t.name,t.size);if(n)return await this.updateGameTimestamp(n.id),console.log("游戏已存在，更新时间戳:",n.id),n.id;const s=`game_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,r={id:s,name:t.name,extension:t.extension,data:t.data,size:t.size,system:t.system,timestamp:Date.now()};return new Promise((o,i)=>{const d=this.db.transaction([$e],"readwrite").objectStore($e).put(r);d.onsuccess=()=>{console.log("游戏数据保存成功:",s),o(s)},d.onerror=()=>{console.error("游戏数据保存失败:",d.error),i(d.error)}})}async findExistingGame(t,n){return this.db||await this.init(),new Promise((s,r)=>{const a=this.db.transaction([$e],"readonly").objectStore($e).getAll();a.onsuccess=()=>{const d=a.result.find(u=>u.name===t&&u.size===n);s(d||null)},a.onerror=()=>{console.error("查找现有游戏失败:",a.error),r(a.error)}})}async getGame(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction([$e],"readonly").objectStore($e).get(t);i.onsuccess=()=>{const a=i.result;if(a){const c={name:a.name,extension:a.extension,data:a.data,size:a.size,system:a.system};console.log("游戏数据读取成功:",t,"大小:",a.size,"字节"),n(c)}else console.log("未找到游戏数据:",t),n(null)},i.onerror=()=>{console.error("游戏数据读取失败:",i.error),s(i.error)}})}async deleteGame(t){return this.db||await this.init(),new Promise((n,s)=>{const i=this.db.transaction([$e],"readwrite").objectStore($e).delete(t);i.onsuccess=()=>{console.log("游戏数据删除成功:",t),n()},i.onerror=()=>{console.error("游戏数据删除失败:",i.error),s(i.error)}})}async getAllGames(){return this.db||await this.init(),new Promise((t,n)=>{const o=this.db.transaction([$e],"readonly").objectStore($e).getAll();o.onsuccess=()=>{const i=o.result;console.log("获取所有游戏数据成功:",i.length,"个游戏"),t(i)},o.onerror=()=>{console.error("获取所有游戏数据失败:",o.error),n(o.error)}})}async getRecentGames(t=5){return this.db||await this.init(),new Promise((n,s)=>{const a=this.db.transaction([$e],"readonly").objectStore($e).index("timestamp").openCursor(null,"prev"),c=[];let d=0;a.onsuccess=u=>{const f=u.target.result;f&&d<t?(c.push(f.value),d++,f.continue()):(console.log("获取最近游戏数据成功:",c.length,"个游戏"),n(c))},a.onerror=()=>{console.error("获取最近游戏数据失败:",a.error),s(a.error)}})}async updateGameTimestamp(t){return this.db||await this.init(),new Promise((n,s)=>{const o=this.db.transaction([$e],"readwrite").objectStore($e),i=o.get(t);i.onsuccess=()=>{const a=i.result;if(a){a.timestamp=Date.now();const c=o.put(a);c.onsuccess=()=>{console.log("游戏时间戳更新成功:",t),n()},c.onerror=()=>{console.error("游戏时间戳更新失败:",c.error),s(c.error)}}else s(new Error("游戏不存在"))},i.onerror=()=>{console.error("获取游戏数据失败:",i.error),s(i.error)}})}async updateGameName(t,n){return this.db||await this.init(),new Promise((s,r)=>{const i=this.db.transaction([$e],"readwrite").objectStore($e),a=i.get(t);a.onsuccess=()=>{const c=a.result;if(c){c.name=n,c.timestamp=Date.now();const d=i.put(c);d.onsuccess=()=>{console.log("游戏名称更新成功:",t,"->",n),s()},d.onerror=()=>{console.error("游戏名称更新失败:",d.error),r(d.error)}}else r(new Error("游戏不存在"))},a.onerror=()=>{console.error("获取游戏数据失败:",a.error),r(a.error)}})}async clearOldGames(t=24*60*60*1e3){this.db||await this.init();const n=Date.now()-t;return new Promise((s,r)=>{const c=this.db.transaction([$e],"readwrite").objectStore($e).index("timestamp").openCursor(IDBKeyRange.upperBound(n));c.onsuccess=d=>{const u=d.target.result;u?(u.delete(),u.continue()):(console.log("旧游戏数据清理完成"),s())},c.onerror=()=>{console.error("清理旧游戏数据失败:",c.error),r(c.error)}})}}const xt=new gd,vd={key:0,class:"space-y-4"},_d={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"},yd=["onClick"],bd={class:"flex items-center gap-4 mb-3"},wd={class:"text-3xl game-icon-container"},xd=["src","alt"],Sd={class:"flex-1 min-w-0"},kd={key:0,class:"mb-1"},Ed=["onBlur","onKeyup"],Cd=["onDblclick"],Ad={class:"flex items-center gap-2 text-sm text-gray-400"},Od={class:"font-medium uppercase"},Id={class:"flex items-center justify-between text-xs text-gray-500"},Bd={class:"relative z-10 flex gap-1 transition-opacity opacity-0 group-hover:opacity-100"},Td=["onClick"],Pd=["onClick"],Rd={key:1,class:"py-12 text-center"},$d=je({__name:"RecentGames",emits:["gameDeleted","gameRenamed"],setup(e,{expose:t,emit:n}){const s=Pr(),r=be([]),o=be(null),i=be(""),a=be(),c=n;ut(async()=>{await d()});async function d(){try{const x=await xt.getRecentGames(6);r.value=x}catch(x){console.error("加载最近游戏失败:",x),xe.error("加载最近游戏失败")}}async function u(x){try{await xt.updateGameTimestamp(x.id),sessionStorage.setItem("selectedGameId",x.id),s.push("/play"),xe.success(`正在启动 ${x.name}`)}catch(k){console.error("启动游戏失败:",k),xe.error("启动游戏失败，请重试")}}async function f(x){try{await xt.deleteGame(x),await d(),c("gameDeleted"),xe.success("游戏已删除")}catch(k){console.error("删除游戏失败:",k),xe.error("删除游戏失败")}}async function g(){if(confirm("确定要清空所有游戏吗？此操作不可撤销。"))try{const x=await xt.getAllGames();for(const k of x)await xt.deleteGame(k.id);await d(),c("gameDeleted"),xe.success("所有游戏已清空")}catch(x){console.error("清空游戏失败:",x),xe.error("清空游戏失败")}}async function h(x){o.value=x.id,i.value=x.name,await xr(),a.value&&(a.value.focus(),a.value.select())}function _(){o.value=null,i.value=""}async function m(x){if(!i.value.trim()){xe.error("游戏名称不能为空");return}try{await xt.updateGameName(x,i.value.trim()),await d(),c("gameRenamed"),xe.success("游戏重命名成功"),_()}catch(k){console.error("重命名游戏失败:",k),xe.error("重命名游戏失败")}}function w(x){const B=Date.now()-x,P=Math.floor(B/(1e3*60)),G=Math.floor(B/(1e3*60*60)),N=Math.floor(B/(1e3*60*60*24));return P<1?"刚刚":P<60?`${P}分钟前`:G<24?`${G}小时前`:N<7?`${N}天前`:new Date(x).toLocaleDateString("zh-CN")}function b(x){const k={"flappy-wings":"/images/game-icons/flappy-wings.png",flappybird:"/images/game-icons/flappybird.png",kof97:"/images/game-icons/kof97.png",新超级马里奥:"/images/game-icons/new_mario.png",星之卡比:"/images/game-icons/kirby.png",超级玛丽:"/images/game-icons/mario.png"};if(k[x])return k[x];const B=x.toLowerCase();return B.includes("mario")||B.includes("马里奥")||B.includes("玛丽")?"/images/game-icons/mario.png":B.includes("kirby")||B.includes("卡比")?"/images/game-icons/kirby.png":B.includes("flappy")||B.includes("飞")?"/images/game-icons/flappy-wings.png":B.includes("kof")||B.includes("拳皇")?"/images/game-icons/kof97.png":"/images/game-icons/mario.png"}return t({loadRecentGames:d}),(x,k)=>r.value.length>0?(ne(),re("div",vd,[O("div",_d,[(ne(!0),re(De,null,mt(r.value,B=>(ne(),re("div",{key:B.id,onClick:P=>u(B),class:"p-4 transition-all duration-300 cursor-pointer group card hover:scale-105 hover:border-blue-500/50 hover:shadow-lg hover:shadow-blue-500/20"},[O("div",bd,[O("div",wd,[O("img",{src:b(B.name),alt:B.name,class:"recent-game-icon"},null,8,xd)]),O("div",Sd,[o.value===B.id?(ne(),re("div",kd,[vi(O("input",{ref_for:!0,ref_key:"editInput",ref:a,"onUpdate:modelValue":k[0]||(k[0]=P=>i.value=P),onBlur:P=>m(B.id),onKeyup:[wo(P=>m(B.id),["enter"]),wo(_,["escape"])],class:"w-full px-2 py-1 text-lg font-semibold text-white bg-gray-700 border border-blue-500 rounded focus:outline-none focus:border-blue-400",onClick:k[1]||(k[1]=Et(()=>{},["stop"]))},null,40,Ed),[[Yc,i.value]])])):(ne(),re("h3",{key:1,onDblclick:P=>h(B),class:"mb-1 overflow-hidden text-lg font-semibold text-white transition-colors cursor-pointer whitespace-nowrap text-ellipsis hover:text-blue-300",title:"双击重命名"},de(B.name),41,Cd)),O("div",Ad,[O("span",Od,"."+de(B.extension),1),k[2]||(k[2]=O("span",null,"•",-1)),O("span",null,de(Dt(ya)(B.size)),1)])])]),O("div",Id,[O("span",null,de(w(B.timestamp)),1),O("div",Bd,[O("button",{onClick:Et(P=>h(B),["stop"]),class:"p-1 text-blue-400 transition-colors rounded hover:text-blue-300 hover:bg-blue-500/20",title:"重命名游戏"},k[3]||(k[3]=[O("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Td),O("button",{onClick:Et(P=>f(B.id),["stop"]),class:"p-1 text-red-400 transition-colors rounded hover:text-red-300 hover:bg-red-500/20",title:"删除游戏"},k[4]||(k[4]=[O("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Pd)])]),k[5]||(k[5]=O("div",{class:"absolute inset-0 flex items-center justify-center transition-opacity opacity-0 pointer-events-none group-hover:opacity-100 bg-black/20 rounded-2xl"},[O("div",{class:"p-3 text-white bg-blue-500 rounded-full shadow-lg"},[O("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24"},[O("path",{d:"M8 5v14l11-7z"})])])],-1))],8,yd))),128))]),O("div",{class:"flex justify-end pt-4"},[O("button",{onClick:g,class:"flex items-center gap-2 text-sm text-gray-400 transition-colors hover:text-red-400"},k[6]||(k[6]=[O("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[O("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),we(" 清空所有游戏 ",-1)]))])])):(ne(),re("div",Rd,k[7]||(k[7]=[O("div",{class:"mb-4 text-6xl"},"🎮",-1),O("h3",{class:"mb-2 text-xl font-semibold text-gray-300"},"还没有游戏记录",-1),O("p",{class:"text-gray-500"},"上传或选择游戏开始你的游戏之旅吧！",-1)])))}});const zd=Fn($d,[["__scopeId","data-v-3b0408ed"]]),Dd="EmulatorBiosDB",Md=1,gt="biosFiles";function Is(){return new Promise((e,t)=>{const n=indexedDB.open(Dd,Md);n.onerror=()=>t(n.error),n.onsuccess=()=>e(n.result),n.onupgradeneeded=s=>{const r=s.target.result;if(!r.objectStoreNames.contains(gt)){const o=r.createObjectStore(gt,{keyPath:"name"});o.createIndex("system","system",{unique:!1}),o.createIndex("uploadDate","uploadDate",{unique:!1})}}})}async function $r(e){const t=await Is();return new Promise((n,s)=>{const r=t.transaction([gt],"readwrite"),o=r.objectStore(gt),i={...e,uploadDate:new Date},a=o.put(i);a.onerror=()=>s(a.error),a.onsuccess=()=>{console.log(`BIOS 文件 ${e.name} 已存储`),n()},r.onerror=()=>s(r.error)})}async function Ld(){const e=await Is();return new Promise((t,n)=>{const o=e.transaction([gt],"readonly").objectStore(gt).getAll();o.onerror=()=>n(o.error),o.onsuccess=()=>t(o.result)})}async function Vt(e){const t=await Is();return new Promise((n,s)=>{const i=t.transaction([gt],"readonly").objectStore(gt).get(e);i.onerror=()=>s(i.error),i.onsuccess=()=>n(i.result||null)})}async function Fd(e){const t=await Is();return new Promise((n,s)=>{const i=t.transaction([gt],"readwrite").objectStore(gt).delete(e);i.onerror=()=>s(i.error),i.onsuccess=()=>{console.log(`BIOS 文件 ${e} 已删除`),n()}})}async function Nd(e){try{return await Vt(e)!==null}catch(t){return console.error("检查 BIOS 文件失败:",t),!1}}async function gn(e){try{const t=await Vt(e);if(!t)return null;const n=new Blob([t.data],{type:"application/octet-stream"});return URL.createObjectURL(n)}catch(t){return console.error("获取 BIOS 文件 URL 失败:",t),null}}const ba="1.0",wa="RetroHub 预置BIOS配置",xa=[{id:"neogeo",name:"Neo Geo BIOS",supportedCores:["arcade","mame2003","fbneo","fbalpha"],filename:"neogeo.zip",description:"Neo Geo BIOS - 支持所有 Neo Geo 街机游戏",version:"1.0",region:"Asia",size:131072,md5:"",url:"https://objectstorageapi.us-east-1.clawcloudrun.com/xeovhv72-1/BIOS/neogeo.zip",isVerified:!0,tags:["neogeo","arcade","asia","verified"],games:["拳皇","合金弹头","侍魂","饿狼传说","kof","king of fighters","metal slug","samurai shodown","garou","fatal fury","neogeo"]},{id:"pgm",name:"PGM BIOS",supportedCores:["arcade","mame2003","fbneo"],filename:"pgm.zip",description:"PGM (PolyGame Master) BIOS - 支持 IGS PGM 街机游戏",version:"1.0",region:"Asia",size:2094636,md5:"87cc944eef4c671aa2629a8ba48a08e0",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/pgm.zip",isVerified:!0,tags:["pgm","arcade","asia","verified"],games:["三国战纪","西游释厄传","傲剑狂刀"]},{id:"naomi",name:"Naomi BIOS",supportedCores:["arcade","mame2003","fbneo"],filename:"naomi.zip",description:"Sega Naomi BIOS - 支持 Sega Naomi 街机游戏",version:"1.0",region:"World",size:9321533,md5:"526eda1e2a7920c92c88178789d71d84",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/naomi.zip",isVerified:!0,tags:["naomi","sega","arcade","verified"],games:["疯狂出租车","死亡之屋2","VR战士3"]},{id:"awbios",name:"Atomiswave BIOS",supportedCores:["arcade","mame2003","fbneo"],filename:"awbios.zip",description:"Sega Atomiswave BIOS - 支持 Atomiswave 街机游戏",version:"1.0",region:"World",size:42296,md5:"85254fbe320ca82a768ec2c26bb08def",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/awbios.zip",isVerified:!0,tags:["atomiswave","sega","arcade","verified"],games:["拳皇XI","侍魂6","合金弹头6"]},{id:"skns",name:"Super Kaneko Nova System BIOS",supportedCores:["arcade","mame2003","fbneo"],filename:"skns.zip",description:"Super Kaneko Nova System BIOS - 支持 SKNS 街机游戏",version:"1.0",region:"World",size:924762,md5:"3f956c4e7008804cb47cbde49bd5b908",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Arcade/skns.zip",isVerified:!0,tags:["skns","kaneko","arcade","verified"],games:["JoJo的奇妙冒险","街头霸王EX"]},{id:"scph1001",name:"PlayStation BIOS (US)",supportedCores:["pcsx_rearmed","beetle_psx","beetle_psx_hw"],filename:"scph1001.bin",description:"Sony PlayStation BIOS (美版) - 支持 PlayStation 游戏",version:"1.0",region:"US",size:524288,md5:"924e392ed05558ffdb115408c263dccf",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Sony%20-%20PlayStation/scph1001.bin",isVerified:!0,tags:["playstation","sony","console","verified"],games:["最终幻想7","生化危机","铁拳3"]},{id:"scph5500",name:"PlayStation BIOS (JP)",supportedCores:["pcsx_rearmed","beetle_psx","beetle_psx_hw"],filename:"scph5500.bin",description:"Sony PlayStation BIOS (日版) - 支持 PlayStation 游戏",version:"1.0",region:"JP",size:524288,md5:"8dd7d5296a650fac7319bce665a6a53c",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Sony%20-%20PlayStation/scph5500.bin",isVerified:!0,tags:["playstation","sony","console","verified"],games:["最终幻想7","生化危机","铁拳3"]},{id:"dc_boot",name:"Dreamcast BIOS",supportedCores:["flycast","reicast"],filename:"dc_boot.bin",description:"Sega Dreamcast BIOS - 支持 Dreamcast 游戏",version:"1.0",region:"World",size:2097152,md5:"e10c53c2f8b90bab96ead2d368858623",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Sega%20-%20Dreamcast/dc_boot.bin",isVerified:!0,tags:["dreamcast","sega","console","verified"],games:["莎木","疯狂出租车","索尼克大冒险"]},{id:"dc_flash",name:"Dreamcast Flash",supportedCores:["flycast","reicast"],filename:"dc_flash.bin",description:"Sega Dreamcast Flash ROM - Dreamcast 系统配置文件",version:"1.0",region:"World",size:131072,md5:"0a93f7940c455905bea6e392dfde92a4",url:"https://github.com/Abdess/retroarch_system/raw/libretro/Sega%20-%20Dreamcast/dc_flash.bin",isVerified:!0,tags:["dreamcast","sega","console","flash","verified"],games:["莎木","疯狂出租车","索尼克大冒险"]}],Nn={version:ba,description:wa,bios:xa},jd=Object.freeze(Object.defineProperty({__proto__:null,bios:xa,default:Nn,description:wa,version:ba},Symbol.toStringTag,{value:"Module"}));function Sa(){return Nn.bios}function ka(e){return Nn.bios.find(n=>n.id===e)||null}async function Ea(e){const t=ka(e);return t?await Nd(t.filename):!1}async function ur(e,t){const n=ka(e);if(!n)throw new Error(`未找到 ID 为 ${e} 的预置 BIOS`);if(!n.url)throw new Error(`BIOS ${n.name} 没有可用的下载链接`);try{if(console.log(`🔽 开始下载 BIOS: ${n.name}`),await Ea(e))return console.log(`✅ BIOS ${n.name} 已经安装`),!0;const s=await fetch(n.url);if(!s.ok)throw new Error(`下载失败: ${s.status} ${s.statusText}`);const r=s.headers.get("content-length"),o=r?parseInt(r,10):n.size;if(!s.body)throw new Error("响应体为空");const i=s.body.getReader(),a=[];let c=0;for(;;){const{done:g,value:h}=await i.read();if(g)break;if(a.push(h),c+=h.length,t&&o>0){const _=Math.round(c/o*100);t(_)}}const d=new Uint8Array(c);let u=0;for(const g of a)d.set(g,u),u+=g.length;n.size>0&&d.length!==n.size&&console.warn(`⚠️ 文件大小不匹配: 期望 ${n.size}, 实际 ${d.length}`);const f={name:n.filename,data:d.buffer,size:d.length,system:n.name,uploadDate:new Date,isPreset:!0,presetId:n.id};return await $r(f),console.log(`✅ BIOS ${n.name} 下载并安装成功`),!0}catch(s){throw console.error(`❌ 下载 BIOS ${n.name} 失败:`,s),s}}function Ud(e){const t=e.toLowerCase();return Nn.bios.filter(n=>t.includes("fbneo")||t.includes("fbalpha")?n.tags.includes("neogeo")||n.tags.includes("pgm")||n.tags.includes("naomi")||n.tags.includes("atomiswave"):t.includes("psx")||t.includes("beetle")||t.includes("pcsx")?n.tags.includes("playstation"):t.includes("flycast")||t.includes("reicast")?n.tags.includes("dreamcast"):t.includes("saturn")||t.includes("yabause")?n.tags.includes("saturn"):!1)}function Gd(e){const t=e.toLowerCase();return Nn.bios.filter(n=>{const s=n.games.some(i=>t.includes(i.toLowerCase())||i.toLowerCase().includes(t)),r=n.tags.some(i=>t.includes(i.toLowerCase())),o=n.tags.some(i=>!!(i==="neogeo"&&(t.includes("kof")||t.includes("拳皇")||t.includes("metal")||t.includes("合金弹头")||t.includes("samurai")||t.includes("侍魂")||t.includes("garou")||t.includes("饿狼"))||i==="pgm"&&(t.includes("kov")||t.includes("三国")||t.includes("西游")||t.includes("傲剑"))||i==="naomi"&&(t.includes("crazy")||t.includes("taxi")||t.includes("出租车")||t.includes("virtua")||t.includes("fighter"))||i==="playstation"&&(t.includes("ff")||t.includes("final")||t.includes("最终幻想")||t.includes("resident")||t.includes("生化危机")||t.includes("tekken")||t.includes("铁拳"))||i==="dreamcast"&&(t.includes("shenmue")||t.includes("莎木")||t.includes("sonic")||t.includes("索尼克"))));return s||r||o})}async function Jd(){const e=Sa(),t=[],n=[];for(const s of e)await Ea(s.id)?t.push(s.id):s.url&&n.push(s.id);return{installed:t,available:n,total:e.length}}const Hd={class:"w-full space-y-4"},Wd={class:"card bg-green-900/20 border-green-500/30"},Vd={class:"p-4"},qd={class:"flex items-center gap-4 text-xs"},Kd={class:"flex items-center gap-1"},Zd={class:"text-gray-300"},Yd={class:"flex items-center gap-1"},Xd={class:"text-gray-300"},Qd={class:"flex items-center gap-1"},eh={class:"text-gray-300"},th={class:"space-y-3"},nh={class:"flex items-center justify-between"},sh={class:"flex-1"},rh={class:"flex items-center gap-3 mb-2"},oh={class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center"},ih={class:"text-white font-bold text-sm"},ah={class:"font-semibold text-white flex items-center gap-2"},lh={key:0,class:"text-green-400 text-xs"},ch={class:"text-gray-400 text-sm"},uh={class:"grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-500 mb-2"},fh={key:0,class:"mb-2"},dh={class:"flex flex-wrap gap-1"},hh={key:0,class:"px-2 py-1 bg-gray-700 rounded text-xs text-gray-400"},ph={class:"flex flex-col items-end gap-2 ml-4"},mh={class:"flex items-center gap-2"},gh={class:"text-xs text-gray-400"},vh=["onClick","disabled"],_h={key:0,class:"flex items-center gap-1"},yh={key:1},bh={key:1,class:"text-green-400 text-sm flex items-center gap-1"},wh={key:2,class:"text-gray-500 text-sm"},xh={key:0,class:"mt-3"},Sh={class:"w-full bg-gray-700 rounded-full h-2"},kh={class:"card p-4 bg-blue-900/20 border-blue-500/30"},Eh={class:"flex flex-wrap gap-2"},Ch=["disabled"],Ah=je({__name:"PresetBiosDownloader",setup(e){const t=be([]),n=be({installed:[],available:[],total:0}),s=be(new Map),r=Je(()=>s.value.size>0);function o(_){if(_===0)return"0 Bytes";const m=1024,w=["Bytes","KB","MB","GB"],b=Math.floor(Math.log(_)/Math.log(m));return parseFloat((_/Math.pow(m,b)).toFixed(2))+" "+w[b]}function i(_){return n.value.installed.includes(_)}function a(_){return s.value.has(_)}function c(_){return s.value.get(_)||0}function d(_){if(i(_))return"bg-green-500";if(a(_))return"bg-blue-500 animate-pulse";const m=t.value.find(w=>w.id===_);return m!=null&&m.url?"bg-yellow-500":"bg-gray-500"}function u(_){if(i(_))return"已安装";if(a(_))return"下载中";const m=t.value.find(w=>w.id===_);return m!=null&&m.url?"可下载":"暂无下载"}async function f(_){try{const m=t.value.find(w=>w.id===_);if(!m)return;s.value.set(_,0),await ur(_,w=>{s.value.set(_,w)}),s.value.delete(_),await h(),xe.success(`BIOS ${m.name} 下载安装成功！`)}catch(m){s.value.delete(_),console.error("下载 BIOS 失败:",m),xe.error(`下载 BIOS 失败: ${m}`)}}async function g(){const _=t.value.filter(m=>!i(m.id)&&m.url&&!a(m.id));if(_.length===0){xe.info("没有可下载的 BIOS 文件");return}xe.info(`开始下载 ${_.length} 个 BIOS 文件...`);for(const m of _)try{await f(m.id)}catch(w){console.error(`下载 ${m.name} 失败:`,w)}}async function h(){try{n.value=await Jd()}catch(_){console.error("刷新状态失败:",_)}}return ut(async()=>{t.value=Sa(),await h()}),(_,m)=>(ne(),re("div",Hd,[O("div",Wd,[O("div",Vd,[m[3]||(m[3]=O("h3",{class:"text-lg font-semibold text-green-400 mb-2 flex items-center"},[O("span",{class:"mr-2"},"🌐"),we(" 预置 BIOS 下载 ")],-1)),m[4]||(m[4]=O("p",{class:"text-gray-300 text-sm mb-3"}," 我们为你准备了经过验证的 BIOS 文件，可以一键下载安装。这些 BIOS 文件已经过测试，确保兼容性。 ",-1)),O("div",qd,[O("div",Kd,[m[0]||(m[0]=O("span",{class:"w-2 h-2 bg-green-500 rounded-full"},null,-1)),O("span",Zd,"已安装: "+de(n.value.installed.length),1)]),O("div",Yd,[m[1]||(m[1]=O("span",{class:"w-2 h-2 bg-blue-500 rounded-full"},null,-1)),O("span",Xd,"可下载: "+de(n.value.available.length),1)]),O("div",Qd,[m[2]||(m[2]=O("span",{class:"w-2 h-2 bg-gray-500 rounded-full"},null,-1)),O("span",eh,"总计: "+de(n.value.total),1)])])])]),O("div",th,[(ne(!0),re(De,null,mt(t.value,w=>(ne(),re("div",{key:w.id,class:"card p-4 hover:bg-gray-800/50 transition-colors"},[O("div",nh,[O("div",sh,[O("div",rh,[O("div",oh,[O("span",ih,de(w.name.charAt(0)),1)]),O("div",null,[O("h4",ah,[we(de(w.name)+" ",1),w.isVerified?(ne(),re("span",lh,"✓ 已验证")):Se("",!0)]),O("p",ch,de(w.description),1)])]),O("div",uh,[O("div",null,"文件: "+de(w.filename),1),O("div",null,"大小: "+de(o(w.size)),1),O("div",null,"区域: "+de(w.region),1),O("div",null,"版本: "+de(w.version),1)]),w.games.length>0?(ne(),re("div",fh,[m[5]||(m[5]=O("div",{class:"text-xs text-gray-400 mb-1"},"支持游戏:",-1)),O("div",dh,[(ne(!0),re(De,null,mt(w.games.slice(0,4),b=>(ne(),re("span",{key:b,class:"px-2 py-1 bg-gray-700 rounded text-xs text-gray-300"},de(b),1))),128)),w.games.length>4?(ne(),re("span",hh," +"+de(w.games.length-4),1)):Se("",!0)])])):Se("",!0)]),O("div",ph,[O("div",mh,[O("div",{class:Le(["w-3 h-3 rounded-full",d(w.id)])},null,2),O("span",gh,de(u(w.id)),1)]),!i(w.id)&&w.url?(ne(),re("button",{key:0,onClick:b=>f(w.id),disabled:a(w.id),class:"btn-primary text-sm px-4 py-2 min-w-[80px]"},[a(w.id)?(ne(),re("span",_h,[m[6]||(m[6]=O("div",{class:"animate-spin rounded-full h-3 w-3 border-b-2 border-white"},null,-1)),we(" "+de(c(w.id))+"% ",1)])):(ne(),re("span",yh,"下载"))],8,vh)):i(w.id)?(ne(),re("div",bh,m[7]||(m[7]=[O("span",null,"✓",-1),we(" 已安装 ",-1)]))):(ne(),re("div",wh,"暂无下载"))])]),a(w.id)?(ne(),re("div",xh,[O("div",Sh,[O("div",{class:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:vs({width:c(w.id)+"%"})},null,4)])])):Se("",!0)]))),128))]),O("div",kh,[m[8]||(m[8]=O("h4",{class:"font-semibold text-blue-400 mb-3"},"批量操作",-1)),O("div",Eh,[O("button",{onClick:g,disabled:r.value,class:"btn-primary text-sm"}," 下载所有可用 BIOS ",8,Ch),O("button",{onClick:h,class:"btn-secondary text-sm"}," 刷新状态 ")])])]))}}),Oh={class:"w-full space-y-6"},Ih={class:"p-6 text-center space-y-3"},Bh={key:0,class:"card"},Th={class:"p-4"},Ph={class:"space-y-2"},Rh={class:"flex items-center space-x-3"},$h={class:"w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center"},zh={class:"text-xs font-bold text-white"},Dh={class:"text-sm font-medium text-white"},Mh={class:"text-xs text-gray-400"},Lh=["onClick"],Fh={key:1,class:"card bg-purple-900/20 border-purple-500/30"},Nh=je({__name:"BiosUploader",setup(e){const t=be(!1),n=be([]),s=be();ut(async()=>{try{n.value=await Ld()}catch(u){console.error("加载 BIOS 文件失败:",u)}});function r(u){if(u===0)return"0 Bytes";const f=1024,g=["Bytes","KB","MB","GB"],h=Math.floor(Math.log(u)/Math.log(f));return parseFloat((u/Math.pow(f,h)).toFixed(2))+" "+g[h]}function o(u){const f=u.toLowerCase();return f.includes("neogeo")?"Neo Geo":f.includes("pgm")?"PGM":f.includes("scph")||f.includes("playstation")?"PlayStation":f.includes("dc_")||f.includes("dreamcast")?"Dreamcast":f.includes("saturn")?"Saturn":f.includes("32x")?"32X":f.includes("sega")?"Sega":f.includes("nintendo")||f.includes("nes")?"Nintendo":"通用"}async function i(u){if(u.length!==0){t.value=!0;try{for(const f of Array.from(u))try{const g=await f.arrayBuffer(),h={name:f.name,data:g,size:g.byteLength,system:o(f.name)};await $r(h),n.value.push(h),xe.success(`BIOS 文件 ${f.name} 上传成功`)}catch(g){console.error(`处理文件 ${f.name} 失败:`,g),xe.error(`处理文件 ${f.name} 失败`)}}catch(f){console.error("处理 BIOS 文件失败:",f),xe.error("处理 BIOS 文件失败")}finally{t.value=!1}}}async function a(u){try{await Fd(u),n.value=n.value.filter(f=>f.name!==u),xe.success(`BIOS 文件 ${u} 已删除`)}catch(f){console.error("删除 BIOS 文件失败:",f),xe.error("删除 BIOS 文件失败")}}function c(u){const f=u.target;f.files&&i(f.files)}function d(u){var f;u.preventDefault(),(f=u.dataTransfer)!=null&&f.files&&i(u.dataTransfer.files)}return(u,f)=>(ne(),re("div",Oh,[_e(Ah),f[10]||(f[10]=an('<div class="relative text-center"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-600"></div></div><div class="relative flex justify-center text-sm"><span class="px-4 py-2 bg-gray-800 text-gray-400 rounded-full font-medium"> 或者手动上传 </span></div></div><div class="card bg-blue-900/20 border-blue-500/30"><div class="p-4"><h3 class="text-lg font-semibold text-blue-400 mb-2 flex items-center"><span class="mr-2">🔧</span> BIOS 文件管理 </h3><p class="text-gray-300 text-sm mb-3"> 某些模拟器核心需要 BIOS 文件才能正常运行游戏。请上传对应的 BIOS 文件。 </p><div class="space-y-2"><h4 class="text-sm font-medium text-gray-400">常用 BIOS 文件：</h4><div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs"><div class="flex items-center space-x-2"><span class="w-2 h-2 bg-red-500 rounded-full"></span><span class="text-gray-300">neogeo.zip - Neo Geo 街机</span></div><div class="flex items-center space-x-2"><span class="w-2 h-2 bg-yellow-500 rounded-full"></span><span class="text-gray-300">pgm.zip - PGM 街机</span></div><div class="flex items-center space-x-2"><span class="w-2 h-2 bg-green-500 rounded-full"></span><span class="text-gray-300">scph1001.bin - PlayStation</span></div><div class="flex items-center space-x-2"><span class="w-2 h-2 bg-blue-500 rounded-full"></span><span class="text-gray-300">dc_boot.bin - Dreamcast</span></div></div></div></div></div>',2)),O("div",{class:"card border-2 border-dashed border-purple-600 hover:border-purple-500 transition-colors duration-300 cursor-pointer group",onDrop:d,onDragover:f[0]||(f[0]=Et(()=>{},["prevent"])),onDragenter:f[1]||(f[1]=Et(()=>{},["prevent"])),onClick:f[2]||(f[2]=g=>{var h;return(h=s.value)==null?void 0:h.click()})},[O("div",Ih,[f[3]||(f[3]=O("div",{class:"text-4xl mb-2 group-hover:scale-110 transition-transform duration-300"}," 🔧 ",-1)),f[4]||(f[4]=O("h3",{class:"text-lg font-semibold text-white mb-1"}," 将 BIOS 文件拖拽到这里 ",-1)),f[5]||(f[5]=O("p",{class:"text-gray-400 text-sm mb-4"},"支持 .zip, .bin, .rom 等格式",-1)),O("input",{ref_key:"fileInput",ref:s,type:"file",multiple:"",accept:".zip,.bin,.rom,.bios",onChange:c,class:"hidden"},null,544),f[6]||(f[6]=O("div",{class:"text-xs text-gray-500"}," 文件将安全存储在浏览器本地存储中 ",-1))])],32),n.value.length>0?(ne(),re("div",Bh,[O("div",Th,[f[8]||(f[8]=O("h3",{class:"text-lg font-semibold text-white mb-3 flex items-center"},[O("span",{class:"mr-2"},"📁"),we(" 已上传的 BIOS 文件 ")],-1)),O("div",Ph,[(ne(!0),re(De,null,mt(n.value,g=>{var h;return ne(),re("div",{key:g.name,class:"flex items-center justify-between p-3 bg-gray-800 rounded-lg"},[O("div",Rh,[O("div",$h,[O("span",zh,de((h=g.name.split(".").pop())==null?void 0:h.toUpperCase()),1)]),O("div",null,[O("div",Dh,de(g.name),1),O("div",Mh,de(r(g.size))+" • "+de(g.system||"通用"),1)])]),O("button",{onClick:_=>a(g.name),class:"text-red-400 hover:text-red-300 transition-colors p-1",title:"删除 BIOS 文件"},f[7]||(f[7]=[O("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[O("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,Lh)])}),128))])])])):Se("",!0),t.value?(ne(),re("div",Fh,f[9]||(f[9]=[O("div",{class:"p-4 text-center"},[O("div",{class:"inline-flex items-center space-x-2"},[O("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"}),O("span",{class:"text-purple-400"},"正在处理 BIOS 文件...")])],-1)]))):Se("",!0)]))}}),jh={class:"min-h-screen p-4 md:p-8"},Uh={class:"max-w-6xl mx-auto mb-8"},Gh={class:"flex items-center justify-between mb-6"},Jh={class:"flex gap-2"},Hh={class:"max-w-6xl mx-auto space-y-8"},Wh={class:"card p-6"},Vh={class:"card p-6"},qh={class:"card p-6"},Kh={class:"card p-6"},Zh={key:0,class:"fixed bottom-4 right-4 max-w-md z-50"},Yh={class:"bg-red-500/20 border border-red-500/30 backdrop-blur-md rounded-xl p-4 text-white shadow-2xl"},Xh={class:"flex items-start gap-3"},Qh={class:"flex-1"},ep={class:"text-sm"},tp=je({__name:"GameLibrary",setup(e){const t=Pr(),n=be(null),s=be();async function r(c){var d;console.log("选择的游戏:",c),console.log("游戏数据大小:",((d=c.data)==null?void 0:d.byteLength)||0,"字节");try{const u=await xt.saveGame(c);console.log("游戏数据已保存到 IndexedDB，ID:",u),sessionStorage.setItem("selectedGameId",u),t.push("/play"),s.value&&setTimeout(()=>{var f;(f=s.value)==null||f.loadRecentGames()},100)}catch(u){console.error("保存游戏数据失败:",u),xe.error("保存游戏数据失败，请重试")}}function o(c){c.length>0&&r(c[0])}function i(c){n.value=c}function a(){console.log("游戏已删除，最近游戏列表已更新")}return(c,d)=>{const u=un("router-link");return ne(),re("div",jh,[O("div",Uh,[O("div",Gh,[d[2]||(d[2]=O("h1",{class:"text-3xl md:text-4xl font-bold text-gradient"},"🎮 游戏库",-1)),O("div",Jh,[_e(u,{to:"/",class:"btn-secondary flex items-center gap-2"},{default:qe(()=>d[1]||(d[1]=[O("span",null,"←",-1),we(" 返回首页 ",-1)])),_:1,__:[1]})])]),d[3]||(d[3]=O("p",{class:"text-gray-300 text-lg"},"选择游戏或上传你的游戏文件开始游戏",-1))]),O("div",Hh,[O("section",Wh,[d[4]||(d[4]=O("h2",{class:"text-2xl font-bold mb-4 flex items-center gap-2"},[O("span",null,"🕒"),we(" 最近游戏 ")],-1)),_e(zd,{ref_key:"recentGamesRef",ref:s,onGameDeleted:a},null,512)]),O("section",Vh,[d[5]||(d[5]=O("h2",{class:"text-2xl font-bold mb-4 flex items-center gap-2"},[O("span",null,"🎯"),we(" 内置游戏 ")],-1)),_e(Qf,{onGameSelected:r,onError:i})]),d[9]||(d[9]=an('<div class="relative text-center"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-600"></div></div><div class="relative flex justify-center text-sm"><span class="px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full font-medium"> 或者 </span></div></div>',1)),O("section",qh,[d[6]||(d[6]=O("h2",{class:"text-2xl font-bold mb-4 flex items-center gap-2"},[O("span",null,"📁"),we(" 上传游戏文件 ")],-1)),_e(hd,{onFilesSelected:o})]),O("section",Kh,[d[7]||(d[7]=O("h2",{class:"text-2xl font-bold mb-4 flex items-center gap-2"},[O("span",null,"🔧"),we(" BIOS 文件管理 ")],-1)),d[8]||(d[8]=O("p",{class:"text-gray-400 text-sm mb-4"}," 某些游戏需要 BIOS 文件才能正常运行。请上传对应的 BIOS 文件以获得最佳游戏体验。 ",-1)),_e(Nh)])]),n.value?(ne(),re("div",Zh,[O("div",Yh,[O("div",Xh,[d[10]||(d[10]=O("span",{class:"text-red-400 text-xl"},"❌",-1)),O("div",Qh,[O("p",ep,de(n.value),1),O("button",{onClick:d[0]||(d[0]=f=>n.value=null),class:"mt-2 text-xs bg-red-500/30 hover:bg-red-500/50 px-3 py-1 rounded-lg transition-colors"}," 关闭 ")])])])])):Se("",!0)])}}}),np={class:"w-full mb-8"},sp={class:"p-8 card"},rp={class:"flex items-center gap-6 mb-8"},op={class:"flex-1 text-white"},ip={class:"w-[50vw] text-3xl font-bold mb-2 whitespace-nowrap overflow-hidden text-ellipsis"},ap={class:"mb-1 text-base opacity-80"},lp={class:"text-sm font-semibold uppercase opacity-70"},cp={key:0,class:"text-white"},up={class:"p-6 border bg-white/5 border-white/10 rounded-xl"},fp={class:"mb-2 text-xl font-semibold"},dp={class:"leading-relaxed opacity-80"},hp=je({__name:"GameInfo",props:{game:{},core:{}},emits:["back"],setup(e){return(t,n)=>(ne(),re("div",np,[O("div",sp,[O("div",rp,[n[0]||(n[0]=O("div",{class:"text-5xl"},"🎮",-1)),O("div",op,[O("h2",ip,de(t.game.name),1),O("p",ap,de(Dt(ya)(t.game.size)),1),O("p",lp," ."+de(t.game.extension.toUpperCase())+" 文件 ",1)])]),t.core?(ne(),re("div",cp,[n[1]||(n[1]=O("h3",{class:"mb-4 font-semibold opacity-90"},"已选择的核心：",-1)),O("div",up,[O("h4",fp,de(t.core.name),1),O("p",dp,de(t.core.description),1)])])):Se("",!0)])]))}}),pp={class:"core-selector"},mp={class:"selector-card"},gp={key:0,class:"recommended-section"},vp={class:"core-info"},_p={class:"extensions"},yp={class:"selection-indicator"},bp={key:0,class:"checkmark"},wp={class:"all-cores-section"},xp={class:"cores-grid"},Sp=["onClick"],kp={class:"core-info"},Ep={class:"extensions"},Cp={class:"selection-indicator"},Ap={key:0,class:"checkmark"},Op={class:"actions"},Ip=["disabled"],Bp=je({__name:"CoreSelector",props:{game:{},selectedCore:{},gameSystem:{}},emits:["core-selected","play"],setup(e,{emit:t}){const n=e,s=t,r=Je(()=>Os),o=Je(()=>{if(n.gameSystem){const a=Rf(n.gameSystem);if(a)return a}return Pf(n.game.extension)});ut(()=>{o.value&&!n.selectedCore&&i(o.value)});function i(a){s("core-selected",a)}return(a,c)=>{var d,u;return ne(),re("div",pp,[O("div",mp,[c[4]||(c[4]=O("h3",null,"选择模拟器核心",-1)),o.value?(ne(),re("div",gp,[c[2]||(c[2]=O("h4",null,"🎯 推荐：",-1)),O("div",{class:Le(["core-option recommended",{selected:((d=a.selectedCore)==null?void 0:d.id)===o.value.id}]),onClick:c[0]||(c[0]=f=>i(o.value))},[O("div",vp,[O("h5",null,de(o.value.name),1),O("p",null,de(o.value.description),1),O("div",_p,[(ne(!0),re(De,null,mt(o.value.extensions,f=>(ne(),re("span",{key:f,class:"ext-tag"}," ."+de(f),1))),128))])]),O("div",yp,[((u=a.selectedCore)==null?void 0:u.id)===o.value.id?(ne(),re("div",bp," ✓ ")):Se("",!0)])],2)])):Se("",!0),O("div",wp,[c[3]||(c[3]=O("h4",null,"所有可用核心：",-1)),O("div",xp,[(ne(!0),re(De,null,mt(r.value,f=>{var g,h,_;return ne(),re("div",{key:f.id,class:Le(["core-option",{selected:((g=a.selectedCore)==null?void 0:g.id)===f.id,recommended:f.id===((h=o.value)==null?void 0:h.id)}]),onClick:m=>i(f)},[O("div",kp,[O("h5",null,de(f.name),1),O("p",null,de(f.description),1),O("div",Ep,[(ne(!0),re(De,null,mt(f.extensions,m=>(ne(),re("span",{key:m,class:"ext-tag"}," ."+de(m),1))),128))])]),O("div",Cp,[((_=a.selectedCore)==null?void 0:_.id)===f.id?(ne(),re("div",Ap,"✓")):Se("",!0)])],10,Sp)}),128))])]),O("div",Op,[O("button",{onClick:c[1]||(c[1]=f=>a.$emit("play")),disabled:!a.selectedCore,class:"btn-play"}," 🎮 开始游戏 ",8,Ip)])])])}}});const Tp=Fn(Bp,[["__scopeId","data-v-2738054f"]]),Pp={key:0,class:"absolute inset-0 z-10 flex items-center justify-center bg-black/80 backdrop-blur-sm"},Rp={class:"space-y-4 text-center"},$p={class:"text-gray-300"},zp=["disabled"],Dp={class:"flex items-center gap-2"},Mp={class:"w-full max-w-md p-6 mx-4 overflow-y-auto bg-white rounded-lg max-h-96"},Lp={class:"flex items-center justify-between mb-4"},Fp={key:0,class:"p-3 mb-4 rounded-lg bg-green-50"},Np={class:"text-sm text-green-700"},jp={key:1,class:"mb-4 space-y-2"},Up={class:"flex-1"},Gp={class:"text-[#333] font-medium flex items-center gap-2"},Jp={key:0,class:"text-blue-600"},Hp={key:1,class:"text-yellow-600"},Wp={key:2,class:"text-gray-600"},Vp={class:"text-sm text-gray-500"},qp={class:"flex gap-2"},Kp=["onClick"],Zp=["onClick"],Yp=["onClick","disabled","title"],Xp={class:"pt-4 border-t"},Qp=je({__name:"RetroArchEmulator",props:{game:{},core:{},loading:{type:Boolean}},emits:["error","stop","back"],setup(e,{emit:t}){const n=e,s=t,r=be(!0),o=be(!1),i=be(window.innerHeight>window.innerWidth),a=be(!1),c=be(!1),d=be([]),u=be(!1),f=be(null);async function g(){try{await ze.init(),a.value=await ze.hasGameProgress(n.game.name),console.log(`🎮 游戏 ${n.game.name} 进度检查:`,a.value?"有存档":"无存档")}catch(p){console.error("检查游戏进度失败:",p)}}async function h(){try{u.value=!0;const p=await ze.manualSaveGame(void 0,n.game.name);p&&(f.value=p.saveTime,await m(),console.log("💾 手动保存成功"))}catch(p){console.error("手动保存失败:",p)}finally{u.value=!1}}async function _(){try{const p=await ze.quickSaveGame(n.game.name);p&&(f.value=p.saveTime,console.log("⚡ 快速保存成功"))}catch(p){console.error("快速保存失败:",p)}}async function m(){try{const p=await ze.getGameProgress(n.game.name);if(p){const R=[...p.saveStates];p.autoSave&&R.unshift(p.autoSave),p.quickSave&&R.unshift(p.quickSave),d.value=R}}catch(p){console.error("加载存档列表失败:",p)}}async function w(p){try{await ze.loadSaveState(p),console.log("📂 读档成功")}catch(R){console.error("读档失败:",R)}}async function b(p,R){var Y,D;try{if(!confirm(`确定要删除存档"${R}"吗？此操作无法撤销。`))return;await ze.deleteSaveState(p);const K=await ze.getGameProgress(n.game.name);K&&(((Y=K.quickSave)==null?void 0:Y.id)===p&&await ze.removeQuickSave(n.game.name),((D=K.autoSave)==null?void 0:D.id)===p&&await ze.removeAutoSave(n.game.name),await ze.removeManualSave(n.game.name,p)),await m(),console.log("🗑️ 存档删除成功")}catch(ee){console.error("删除存档失败:",ee),alert("删除存档失败，请重试")}}async function x(p){try{const R=await ze.exportSaveState(p),Y=URL.createObjectURL(R),D=document.createElement("a");D.href=Y,D.download=`${n.game.name}_save_${new Date().toISOString().slice(0,10)}.json`,D.click(),URL.revokeObjectURL(Y),console.log("📤 导出存档成功")}catch(R){console.error("导出存档失败:",R)}}async function k(p){try{const R=await ze.importSaveState(p);await m(),console.log("📥 导入存档成功:",R.description)}catch(R){console.error("导入存档失败:",R)}}function B(p){var D;const R=p.target,Y=(D=R.files)==null?void 0:D[0];Y&&(k(Y),R.value="")}async function P(p){try{console.log(`🔍 尝试从文件系统加载 BIOS: ${p}`);const R=await fetch(`/games/${p}`);if(R.ok){const Y=await R.arrayBuffer(),D={name:p,data:Y,size:Y.byteLength,system:p.includes("neogeo")?"neogeo":"arcade",uploadDate:new Date};return await $r(D),console.log(`✅ BIOS 文件 ${p} 从文件系统加载成功`),!0}}catch(R){console.log(`❌ 从文件系统加载 BIOS ${p} 失败:`,R)}return!1}async function G(p,R){var Y,D,ee,K,oe,F,L,le,ae,te,ge,W,X,V,se,fe,ve,S,l,A;try{console.log(`🔧 检查 ${p} 系统的 BIOS 文件...`),(((Y=n.core)==null?void 0:Y.id)==="fbneo"||(ee=(D=n.core)==null?void 0:D.id)!=null&&ee.includes("fbalpha")||p==="arcade")&&(window.EJS_dontExtractBIOS=!1,console.log("🎮 FBNeo/街机模式：设置不解压 BIOS 文件"));const T=((K=R.split("/").pop())==null?void 0:K.split(".")[0])||"";console.log(`🎮 游戏名称: ${T}`),console.log(`🔍 游戏URL: ${R}`),console.log("🔍 URL分割结果:",R.split("/")),console.log("🔍 文件名:",R.split("/").pop());let y=[];(oe=n.core)!=null&&oe.id&&(y=Ud(n.core.id),console.log(`💡 基于核心 ${n.core.id} 推荐的 BIOS:`,y.map(v=>v.name))),y.length===0&&(y=Gd(T),console.log(`💡 基于游戏名称 ${T} 推荐的 BIOS:`,y.map(v=>v.name))),console.log(`💡 最终推荐的 BIOS 数量: ${y.length}`);for(const v of y){console.log(`🔧 尝试加载 BIOS 文件: ${v.filename}`),console.log(`🔍 查找 BIOS 文件: ${v.filename}`);const E=await Vt(v.filename);if(console.log("🔍 BIOS 文件查找结果:",E?"找到":"未找到"),E){const $=await gn(v.filename);if($){((F=n.core)==null?void 0:F.id)==="fbneo"||(le=(L=n.core)==null?void 0:L.id)!=null&&le.includes("fbalpha")?(window.EJS_dontExtractBIOS=!1,window.EJS_biosUrl=$,console.log(`✅ FBNeo BIOS 文件 ${v.filename} 已加载 (不解压模式)`),console.log(`🔧 EJS_dontExtractBIOS = ${window.EJS_dontExtractBIOS}`),console.log(`🔧 EJS_biosUrl = ${window.EJS_biosUrl}`)):(window.EJS_biosUrl=$,console.log(`✅ BIOS 文件 ${v.filename} 已加载`));return}}else if(console.warn(`⚠️ 未找到 BIOS 文件: ${v.filename}`),v.url&&v.isVerified){console.log(`🔄 自动下载 BIOS: ${v.name}`),xe.info(`检测到游戏需要 ${v.name}，正在自动下载...`,{duration:3e3});try{if(await ur(v.id),await Vt(v.filename)){const z=await gn(v.filename);if(z){((ae=n.core)==null?void 0:ae.id)==="fbneo"||(ge=(te=n.core)==null?void 0:te.id)!=null&&ge.includes("fbalpha")?(window.EJS_dontExtractBIOS=!1,window.EJS_biosUrl=z,console.log(`✅ FBNeo BIOS 文件 ${v.filename} 下载并加载成功 (不解压模式)`)):(window.EJS_biosUrl=z,console.log(`✅ BIOS 文件 ${v.filename} 下载并加载成功`)),xe.success(`${v.name} 下载成功！游戏即将启动`,{duration:3e3});return}}}catch($){console.error("下载 BIOS 失败:",$),xe.error(`下载 ${v.name} 失败，请手动在 BIOS 管理页面下载`,{duration:5e3})}}else v.url?xe.warning(`游戏需要 ${v.name}，请在 BIOS 管理页面下载`,{duration:5e3}):xe.warning(`游戏需要 ${v.name}，请在 BIOS 管理页面手动上传`,{duration:5e3})}if(y.length===0){let v="";if(p==="arcade"||p==="fbneo"||p==="fbalpha"){const E=R.toLowerCase();E.includes("kof")||E.includes("neogeo")||E.includes("metal")||E.includes("samurai")||E.includes("garou")||E.includes("lastblade")||E.includes("fatfury")?v="neogeo.zip":E.includes("pgm")||E.includes("knights")||E.includes("ddp")||E.includes("kov")||E.includes("三国")?v="pgm.zip":E.includes("naomi")||E.includes("crazy")||E.includes("taxi")||E.includes("virtua")?v="naomi.zip":(E.includes("awbios")||E.includes("atomiswave"))&&(v="awbios.zip")}else p==="psx"||p==="playstation"?v="scph1001.bin":p==="dreamcast"&&(v="dc_boot.bin");if(v)if(console.log(`🔧 传统方式检测到需要 BIOS: ${v}`),await Vt(v)){const $=await gn(v);$&&(((W=n.core)==null?void 0:W.id)==="fbneo"||(V=(X=n.core)==null?void 0:X.id)!=null&&V.includes("fbalpha")?(window.EJS_dontExtractBIOS=!1,window.EJS_biosUrl=$,console.log(`✅ FBNeo BIOS 文件 ${v} 已加载 (不解压模式)`)):(window.EJS_biosUrl=$,console.log(`✅ BIOS 文件 ${v} 已加载`)))}else{if(console.warn(`⚠️ 未找到 BIOS 文件: ${v}`),console.log(`🔄 尝试从文件系统加载 BIOS: ${v}`),await P(v)&&await Vt(v)){const U=await gn(v);if(U){((se=n.core)==null?void 0:se.id)==="fbneo"||(ve=(fe=n.core)==null?void 0:fe.id)!=null&&ve.includes("fbalpha")?(window.EJS_dontExtractBIOS=!1,window.EJS_biosUrl=U,console.log(`✅ FBNeo BIOS 文件 ${v} 从文件系统加载成功 (不解压模式)`)):(window.EJS_biosUrl=U,console.log(`✅ BIOS 文件 ${v} 从文件系统加载成功`));return}}const I=(await pa(()=>Promise.resolve().then(()=>jd),void 0)).default.bios.find(J=>J.filename===v);if(I&&I.url&&I.isVerified){console.log(`🔄 尝试自动下载传统检测的 BIOS: ${I.name}`),xe.info(`检测到游戏需要 ${I.name}，正在自动下载...`,{duration:3e3});try{if(await ur(I.id),await Vt(v)){const U=await gn(v);if(U){((S=n.core)==null?void 0:S.id)==="fbneo"||(A=(l=n.core)==null?void 0:l.id)!=null&&A.includes("fbalpha")?(window.EJS_dontExtractBIOS=!1,window.EJS_biosUrl=U,console.log(`✅ FBNeo BIOS 文件 ${v} 下载并加载成功 (不解压模式)`)):(window.EJS_biosUrl=U,console.log(`✅ BIOS 文件 ${v} 下载并加载成功`)),xe.success(`${I.name} 下载成功！游戏即将启动`,{duration:3e3});return}}}catch(J){console.error("下载 BIOS 失败:",J),xe.error(`下载 ${I.name} 失败，请手动在 BIOS 管理页面下载`,{duration:5e3})}}else xe.warning(`游戏需要 ${v}，请在 BIOS 管理页面上传或下载`,{duration:5e3})}}}catch(T){console.error("加载 BIOS 文件失败:",T)}}function N(){i.value=window.innerHeight>window.innerWidth,console.log("屏幕方向变化:",i.value?"竖屏":"横屏")}ut(async()=>{window.addEventListener("resize",N),window.addEventListener("orientationchange",N),await g()}),on(()=>[n.game,n.core],async([p,R])=>{if(p&&R){console.log("游戏和核心都准备好了，开始初始化...",{game:p.name,core:R.name});try{await Z()}catch(Y){console.error("RetroArch初始化失败:",Y),s("error","RetroArch初始化失败: "+Y.message)}}},{immediate:!0}),on(c,async p=>{p&&await m()});async function Z(){var p,R;try{r.value=!0,console.log("开始加载EmulatorJS模拟器...");const Y=await ie(),D=M(((p=n.core)==null?void 0:p.id)||"gambatte");window.currentGameName=n.game.name;const ee=q(((R=n.core)==null?void 0:R.id)||"gambatte");console.log("🎮 游戏信息:"),console.log("  - 游戏名称:",n.game.name),console.log("  - 游戏URL:",Y),console.log("  - 核心类型:",D),console.log("  - 系统类型:",ee),window.EJS_ready=()=>{console.log("🎮 EmulatorJS ready!"),r.value=!1},window.EJS_onGameStart=async()=>{var oe;console.log("🎮 Game started!"),r.value=!1;try{await ze.init(),ze.startGameSession(n.game.name,q(((oe=n.core)==null?void 0:oe.id)||"gambatte")),console.log("🎮 进度保存会话已启动"),setTimeout(async()=>{try{const F=await ze.getGameProgress(n.game.name);F!=null&&F.autoSave&&(console.log("🔄 自动读取最新自动存档..."),await ze.loadSaveState(F.autoSave.id),console.log("✅ 自动存档读取成功"))}catch(F){console.warn("自动读取存档失败:",F)}},2e3)}catch(F){console.error("启动进度保存会话失败:",F)}},window.EJS_onLoadState=()=>{console.log("🎮 Game loaded!")},window.EJS_onError=oe=>{console.error("❌ EmulatorJS 错误:",oe),r.value=!1},window.EJS_onGameLoaded=()=>{console.log("🎮 游戏文件已加载")},window.EJS_player="#canvas",ee==="arcade"?window.EJS_core="fbneo":window.EJS_core=D,window.EJS_system=ee,window.EJS_pathtodata="/emulatorjs/",window.EJS_gameUrl=Y;let K=n.game.name;if(console.log("🔧 原始游戏名称:",K),D==="fbneo"){K=n.game.name.replace(/\.(zip|neo|mvs)$/i,""),console.log("🔧 移除扩展名后:",K);const oe=K.toLowerCase();oe.includes("kof")&&(console.log("🔧 检测到 KOF 游戏，进行名称标准化..."),oe.includes("97")?(K="kof97",console.log("🔧 标准化为: kof97 (包含97)")):oe.includes("970")?(K="kof97",console.log("🔧 标准化为: kof97 (从970)")):oe.includes("971")&&(K="kof97",console.log("🔧 标准化为: kof97 (从971)")))}console.log("🔧 最终游戏名称:",K),window.EJS_gameName=K,window.EJS_startOnLoaded=!0,window.EJS_threads=!1,window.EJS_volume=.8,ee==="arcade"||ee==="neogeo"||D==="fbneo"?(window.EJS_forceLegacyCores=!0,window.EJS_webgl2=!1,window.EJS_threads=!1,console.log("🔧 FBNeo 配置：使用 legacy 核心，禁用 WebGL2 和多线程")):window.EJS_forceLegacyCores=!1,D==="fbneo"&&(window.EJS_pathParentDirectory="/games/",window.EJS_gameParentUrl="/games/neogeo.zip",window.EJS_softLoad=0,window.EJS_dontExtractBIOS=!1,window.EJS_gameUrl=`/games/${K}.zip`,ee==="neogeo"?(window.EJS_biosUrl="/games/neogeo.zip",console.log("🔧 Neo Geo 模式：使用文件系统 BIOS 路径")):window.EJS_biosUrl="",console.log("🔧 FBNeo 配置:",{gameUrl:window.EJS_gameUrl,gameParentUrl:window.EJS_gameParentUrl,biosUrl:window.EJS_biosUrl,pathParentDirectory:window.EJS_pathParentDirectory,dontExtractBIOS:window.EJS_dontExtractBIOS,gameName:window.EJS_gameName})),console.log("EmulatorJS配置:",{player:window.EJS_player,core:window.EJS_core,system:window.EJS_system,pathtodata:window.EJS_pathtodata,gameUrl:window.EJS_gameUrl,gameName:window.EJS_gameName,startOnLoaded:window.EJS_startOnLoaded}),console.log("验证游戏 URL:",Y),fetch(Y).then(oe=>(console.log("游戏文件响应:",oe.status,oe.headers.get("content-type")),oe.arrayBuffer())).then(oe=>{console.log("游戏文件大小:",oe.byteLength,"字节")}).catch(oe=>{console.error("游戏文件访问失败:",oe)}),await C(),D==="fbneo"&&setTimeout(()=>{if(window.EJS_emulator){const oe=window.EJS_emulator;oe.supportsWebgl2!==void 0&&(oe.webgl2Enabled=!1,oe.supportsWebgl2=!1),console.log("使用 FBNeo Legacy 核心，WebGL2:",oe.webgl2Enabled)}},100),setTimeout(()=>{r.value&&(console.log("EmulatorJS初始化超时，尝试手动启动..."),r.value=!1)},15e3)}catch(Y){throw r.value=!1,Y}}function M(p){return{gambatte:"gambatte",mgba:"mgba",snes9x:"snes9x",fceumm:"fceumm",genesis_plus_gx:"genesis_plus_gx",melonds:"melonds",desmume:"desmume",fbneo:"fbneo"}[p]||"gambatte"}function q(p){const R={gambatte:"gb",mgba:"gba",snes9x:"snes",fceumm:"nes",genesis_plus_gx:"segaMD",melonds:"nds",desmume:"nds",fbneo:"arcade"};if(p==="fbneo"){const Y=window.currentGameName||"";return Y.toLowerCase().includes("kof")||Y.toLowerCase().includes("neogeo")||Y.toLowerCase().includes("metal")||Y.toLowerCase().includes("samurai")?"neogeo":"arcade"}return R[p]||"gb"}async function ie(){try{console.log("原始游戏数据:",n.game.data),console.log("游戏数据类型:",typeof n.game.data),console.log("游戏数据是否为数组:",Array.isArray(n.game.data));const p=new Uint8Array(n.game.data);console.log("游戏数据大小:",p.length,"字节");let R="application/octet-stream";const Y=new Blob([p],{type:R}),D=URL.createObjectURL(Y);return console.log("创建Blob URL:",D),console.log("MIME类型:",R),D}catch(p){throw console.error("创建游戏Blob失败:",p),new Error("无法创建游戏文件")}}async function C(){return new Promise(async(p,R)=>{if(window.EJS_emulator){try{window.EJS_emulator.destroy()}catch(V){console.log("清理旧实例失败:",V)}delete window.EJS_emulator}const Y=window.EJS_gameUrl,D=window.EJS_core,ee=window.EJS_system;window.EJS_player="#canvas",window.EJS_core=D,window.EJS_system=ee,window.EJS_pathtodata="/emulatorjs/",window.EJS_gameUrl=Y,window.EJS_startOnLoaded=!0,window.EJS_threads=!0,window.EJS_volume=.8,window.EJS_DEBUG_XX=!1,window.EJS_webgl2=!0;const K=document.createElement("canvas"),F=!!(K.getContext("webgl2")||K.getContext("webgl")),L=typeof SharedArrayBuffer<"u";if(console.log(`🔍 浏览器兼容性检查:
      WebGL支持: ${F?"✅":"❌"}
      WebGL2支持: ${K.getContext("webgl2")?"✅":"❌"}
      SharedArrayBuffer支持: ${L?"✅":"❌"}
      多线程支持: ${L?"✅":"❌"}
    `),window.EJS_frameSkip=0,window.EJS_vsync=!0,window.EJS_fastForward=!1,window.EJS_rewind=!1,window.EJS_netplay=!1,window.EJS_cheats=!1,F||(console.warn("⚠️ WebGL不支持，降级到软件渲染"),window.EJS_videoDriver="software",window.EJS_webgl2=!1),L||(console.warn("⚠️ SharedArrayBuffer不支持，禁用多线程"),window.EJS_threads=!1),window.EJS_audioLatency=64,window.EJS_audioDriver="webaudio",window.EJS_videoDriver="gl",window.EJS_smoothing=!1,ee==="nds"){window.EJS_threads=L,window.EJS_frameSkip=0,window.EJS_audioLatency=128,window.EJS_threaded_video=L,window.EJS_gpu_screenshot=!1;const V=n.game.name.toLowerCase();(V.includes("马里奥")||V.includes("mario"))&&(window.EJS_frameSkip=1,window.EJS_audioLatency=256,window.EJS_vsync=!1,console.log("🍄 新超级马里奥专用优化已启用")),console.log("🎮 NDS 游戏性能优化已启用")}window.EJS_memorySize=128*1024*1024,window.EJS_stackSize=5*1024*1024,window.EJS_allowMemoryGrowth=!0,typeof window<"u"&&window.gc&&setInterval(()=>{try{window.gc(),console.log("🗑️ 执行垃圾回收")}catch{}},3e4);let le=0,ae=performance.now(),te=[];const ge=setInterval(()=>{const V=performance.now(),se=V-ae,fe=Math.round(le*1e3/se),ve=se/(le||1),S=1e3/60,l=Math.min(100,ve/S*100);te.push(l),te.length>10&&te.shift();const A=te.reduce((T,y)=>T+y,0)/te.length;fe<50||A>80?(console.warn(`⚠️ 性能警告: FPS = ${fe}, CPU使用率 = ${A.toFixed(1)}%`),A>90&&(console.log("🔧 自动启用性能优化..."),window.EJS_frameSkip=2,window.EJS_audioLatency=512)):console.log(`📊 性能正常: FPS = ${fe}, CPU使用率 = ${A.toFixed(1)}%`),le=0,ae=V},5e3);as(()=>{ge&&clearInterval(ge)}),(ee==="arcade"||D==="fbalpha"||D==="fbneo")&&(window.EJS_dontExtractBIOS=!1,console.log("🎮 街机游戏模式：ZIP文件将直接传递给核心"),await G(ee,Y)),window.EJS_onLoadState=()=>{console.log("🎮 游戏状态加载完成")},window.EJS_onError=V=>{console.error("🎮 EmulatorJS 错误:",V)};const W=document.querySelector('script[src="/emulatorjs/loader.js"]');W&&W.remove();const X=document.createElement("script");X.src="/emulatorjs/loader.js",X.async=!0,X.onload=()=>{console.log("✅ EmulatorJS加载器脚本加载成功"),setTimeout(()=>{window.EJS_emulator?(console.log("✅ EmulatorJS初始化成功"),console.log("🎮 模拟器准备就绪"),setTimeout(()=>{try{console.log("尝试自动启动游戏...");const V=document.querySelector(".ejs_start_button");V?(console.log("找到开始按钮，自动点击..."),V.click()):console.log("未找到开始按钮")}catch(V){console.log("自动启动游戏失败:",V)}},100),window.EJS_emulator.on("start",()=>{console.log("🎮 游戏开始"),r.value=!1}),window.EJS_emulator.on("error",V=>{console.error("❌ EmulatorJS错误:",V),r.value=!1,s("error",`模拟器错误: ${V}`)}),p()):(console.log("等待EmulatorJS初始化..."),p())},300)},X.onerror=V=>{console.error("❌ EmulatorJS加载器脚本加载失败:",V),R(new Error("无法加载EmulatorJS加载器"))},document.head.appendChild(X)})}function j(){try{if(console.log("🛑 开始停止模拟器..."),window.EJS_emulator){try{if(window.EJS_emulator.pause&&window.EJS_emulator.pause(),window.EJS_emulator.gameManager&&window.EJS_emulator.gameManager.toggleMainLoop&&window.EJS_emulator.gameManager.toggleMainLoop(0),window.EJS_emulator.Module&&window.EJS_emulator.Module.AL){const Y=window.EJS_emulator.Module.AL.currentCtx;if(Y&&Y.audioCtx){console.log("🔇 停止音频上下文..."),Y.sources&&Object.values(Y.sources).forEach(D=>{try{D.gain&&D.gain.disconnect(),D.panner&&D.panner.disconnect()}catch(ee){console.log("停止音频源时出错:",ee)}});try{Y.audioCtx.state!=="closed"&&Y.audioCtx.close()}catch(D){console.log("关闭音频上下文时出错:",D)}}}window.EJS_emulator.destroy()}catch(Y){console.log("销毁模拟器实例时出错:",Y)}delete window.EJS_emulator}const p=document.querySelector("#canvas");p&&(p.innerHTML=""),document.querySelectorAll("audio").forEach(Y=>{try{Y.pause(),Y.currentTime=0,Y.src="",Y.load()}catch(D){console.log("清理音频元素时出错:",D)}}),console.log("✅ 模拟器已完全停止")}catch(p){console.error("停止模拟器时出错:",p)}s("stop")}return as(async()=>{window.removeEventListener("resize",N),window.removeEventListener("orientationchange",N),await ze.endGameSession(),j()}),(p,R)=>{var Y;return ne(),re("div",{class:Le(["w-full",o.value?"mobile-fullscreen":"space-y-6"])},[O("div",{class:Le(["relative bg-black rounded-xl overflow-hidden shadow-2xl",o.value?"w-full h-full":"game-viewport mx-auto"])},[r.value?(ne(),re("div",Pp,[O("div",Rp,[R[4]||(R[4]=an('<div class="relative"><div class="mx-auto loading-spinner"></div><div class="pulse-ring"></div><div class="pulse-ring"></div><div class="pulse-ring"></div></div><h3 class="text-xl font-semibold text-white"> 正在加载RetroArch模拟器... </h3>',2)),O("p",$p,"正在下载 "+de((Y=p.core)==null?void 0:Y.name)+" 核心...",1),R[5]||(R[5]=O("div",{class:"w-64 h-2 mx-auto bg-gray-700 rounded-full"},[O("div",{class:"h-2 rounded-full bg-primary-500 animate-pulse",style:{width:"45%"}})],-1))])])):Se("",!0),R[6]||(R[6]=O("div",{id:"canvas",class:"flex items-center justify-center w-full h-full bg-black min-h-[400px]",style:{width:"100%",height:"100%","min-height":"400px"}},null,-1))],2),O("div",{class:Le(["game-controls",o.value?"fixed bottom-0 left-0 right-0 z-30 rounded-none":""])},[O("button",{onClick:j,class:"flex items-center gap-2 btn-secondary"},R[7]||(R[7]=[O("span",{class:"text-lg"},"⏹️",-1),we(" 停止游戏 ",-1)])),r.value?Se("",!0):(ne(),re("button",{key:0,onClick:h,disabled:u.value,class:"flex items-center gap-2 btn-secondary",title:"手动保存游戏进度"},[R[8]||(R[8]=O("span",{class:"text-lg"},"💾",-1)),we(" "+de(u.value?"保存中...":"保存"),1)],8,zp)),r.value?Se("",!0):(ne(),re("button",{key:1,onClick:_,class:"flex items-center gap-2 btn-secondary",title:"快速保存"},R[9]||(R[9]=[O("span",{class:"text-lg"},"⚡",-1),we(" 快存 ",-1)]))),r.value?Se("",!0):(ne(),re("button",{key:2,onClick:R[0]||(R[0]=D=>c.value=!c.value),class:"flex items-center gap-2 btn-secondary",title:"存档管理"},R[10]||(R[10]=[O("span",{class:"text-lg"},"📂",-1),we(" 存档 ",-1)]))),O("button",{onClick:R[1]||(R[1]=D=>p.$emit("back")),class:"flex items-center gap-2 btn-secondary"},R[11]||(R[11]=[O("span",{class:"text-lg"},"←",-1),we(" 返回选择 ",-1)])),O("div",Dp,[O("div",{class:Le(["status-indicator",r.value?"status-loading":"status-playing"])},[R[12]||(R[12]=O("div",{class:"w-2 h-2 mr-2 bg-current rounded-full animate-pulse"},null,-1)),we(" "+de(r.value?"加载中":"运行中"),1)],2)])],2),c.value?(ne(),re("div",{key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",onClick:R[3]||(R[3]=Et(D=>c.value=!1,["self"]))},[O("div",Mp,[O("div",Lp,[R[13]||(R[13]=O("h3",{class:"text-lg font-bold"},"存档管理",-1)),O("button",{onClick:R[2]||(R[2]=D=>c.value=!1),class:"text-gray-500 hover:text-gray-700"}," ✕ ")]),f.value?(ne(),re("div",Fp,[O("div",Np,[R[14]||(R[14]=O("span",{class:"font-medium"},"自动保存:",-1)),we(" "+de(f.value.toLocaleString()),1)])])):Se("",!0),d.value.length>0?(ne(),re("div",jp,[R[15]||(R[15]=O("h4",{class:"font-medium text-gray-700"},"存档列表",-1)),(ne(!0),re(De,null,mt(d.value,D=>(ne(),re("div",{key:D.id,class:Le(["flex items-center justify-between p-3 border rounded-lg",{"bg-blue-50 border-blue-200":D.autoSave,"bg-yellow-50 border-yellow-200":D.id.includes("_quick"),"bg-gray-50":!D.autoSave&&!D.id.includes("_quick")}])},[O("div",Up,[O("div",Gp,[D.autoSave?(ne(),re("span",Jp,"🔄")):D.id.includes("_quick")?(ne(),re("span",Hp,"⚡")):(ne(),re("span",Wp,"💾")),we(" "+de(D.description),1)]),O("div",Vp,de(new Date(D.saveTime).toLocaleString()),1)]),O("div",qp,[O("button",{onClick:ee=>w(D.id),class:"px-3 py-1 text-sm text-white bg-blue-500 rounded hover:bg-blue-600"}," 读取 ",8,Kp),O("button",{onClick:ee=>x(D.id),class:"px-3 py-1 text-sm text-white bg-green-500 rounded hover:bg-green-600"}," 导出 ",8,Zp),O("button",{onClick:ee=>b(D.id,D.description||"未命名存档"),class:Le(["px-3 py-1 text-sm text-white bg-red-500 rounded hover:bg-red-600",{"opacity-50 cursor-not-allowed":D.autoSave}]),disabled:D.autoSave,title:D.autoSave?"自动保存无法删除":"删除此存档"}," 删除 ",10,Yp)])],2))),128))])):Se("",!0),O("div",Xp,[R[16]||(R[16]=O("label",{class:"block mb-2 text-sm font-medium text-gray-700"}," 导入存档文件 ",-1)),O("input",{type:"file",accept:".json",onChange:B,class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32)])])])):Se("",!0)],2)}}}),em={class:"flex flex-col min-h-screen mobile-safe-area"},tm={key:0,class:"p-4 border-b bg-black/20 backdrop-blur-sm border-white/10"},nm={class:"flex items-center justify-between max-w-6xl mx-auto"},sm={class:"flex items-center gap-4"},rm={key:0},om={class:"text-xl font-bold"},im={class:"text-sm text-gray-400"},am={key:0,class:"text-sm text-gray-400"},lm={class:"flex flex-col flex-1"},cm={key:0,class:"flex items-center justify-center flex-1 p-8"},um={class:"space-y-6 text-center"},fm={key:1,class:"flex-1 p-4 md:p-8"},dm={class:"max-w-4xl mx-auto space-y-6"},hm={key:2,class:"flex-1"},pm={key:1,class:"fixed z-50 max-w-md bottom-4 right-4"},mm={class:"p-4 text-white border shadow-2xl bg-red-500/20 border-red-500/30 backdrop-blur-md rounded-xl"},gm={class:"flex items-start gap-3"},vm={class:"flex-1"},_m={class:"text-sm"},ym=je({__name:"GamePlay",setup(e){const t=Pr(),n=be(null),s=be(null),r=be(!1),o=be(!1),i=be(!1),a=be(null);ut(async()=>{const h=sessionStorage.getItem("selectedGameId");if(console.log("从 sessionStorage 获取的游戏 ID:",h),h)try{const _=await xt.getGame(h);if(_){n.value=_,console.log("从 IndexedDB 加载游戏成功:",_.name,"大小:",_.size,"字节");try{await xt.updateGameTimestamp(h),console.log("游戏时间戳已更新")}catch(m){console.warn("更新游戏时间戳失败:",m)}}else console.log("未找到游戏数据，重定向到游戏库"),t.push("/library")}catch(_){console.error("从 IndexedDB 加载游戏失败:",_),a.value="加载游戏数据失败",t.push("/library")}else console.log("没有找到游戏 ID，重定向到游戏库"),t.push("/library")});function c(h){s.value=h}function d(){n.value&&s.value&&(r.value=!0,o.value=!0)}function u(){r.value=!1,o.value=!1}function f(){t.push("/library")}function g(h){a.value=h,o.value=!1}return(h,_)=>{const m=un("router-link");return ne(),re("div",em,[n.value&&!i.value?(ne(),re("div",tm,[O("div",nm,[O("div",sm,[_e(m,{to:"/library",class:"flex items-center gap-2 btn-secondary"},{default:qe(()=>_[1]||(_[1]=[O("span",null,"←",-1),we(" 返回游戏库 ",-1)])),_:1,__:[1]}),n.value?(ne(),re("div",rm,[O("h1",om,de(n.value.name),1),O("p",im,de(n.value.system),1)])):Se("",!0)]),s.value?(ne(),re("div",am," 核心: "+de(s.value.name),1)):Se("",!0)])])):Se("",!0),O("main",lm,[n.value?r.value?n.value&&s.value?(ne(),re("div",hm,[_e(Qp,{game:n.value,core:s.value,loading:o.value,onError:g,onStop:u,onBack:u},null,8,["game","core","loading"])])):Se("",!0):(ne(),re("div",fm,[O("div",dm,[_e(hp,{game:n.value,core:s.value,onBack:f},null,8,["game","core"]),_e(Tp,{game:n.value,"selected-core":s.value,"game-system":n.value.system,onCoreSelected:c,onPlay:d},null,8,["game","selected-core","game-system"])])])):(ne(),re("div",cm,[O("div",um,[_[3]||(_[3]=O("h2",{class:"text-[100px]"},"🎮",-1)),_[4]||(_[4]=O("h2",{class:"text-2xl font-bold"},"没有选择游戏",-1)),_[5]||(_[5]=O("p",{class:"text-gray-400"},"请先选择一个游戏开始游戏",-1)),_e(m,{to:"/library",class:"inline-flex items-center gap-2 btn-primary"},{default:qe(()=>_[2]||(_[2]=[O("h2",{class:"text-[20px]"},"🎮",-1),we(" 选择游戏 ",-1)])),_:1,__:[2]})])]))]),a.value?(ne(),re("div",pm,[O("div",mm,[O("div",gm,[_[6]||(_[6]=O("span",{class:"text-xl text-red-400"},"❌",-1)),O("div",vm,[O("p",_m,de(a.value),1),O("button",{onClick:_[0]||(_[0]=w=>a.value=null),class:"px-3 py-1 mt-2 text-xs transition-colors rounded-lg bg-red-500/30 hover:bg-red-500/50"}," 关闭 ")])])])])):Se("",!0)])}}}),bm={class:"min-h-screen p-4 md:p-8"},wm={class:"max-w-4xl mx-auto"},xm={class:"text-center mb-12"},Sm=je({__name:"About",setup(e){return(t,n)=>{const s=un("router-link");return ne(),re("div",bm,[O("div",wm,[O("div",xm,[_e(s,{to:"/",class:"btn-secondary inline-flex items-center gap-2 mb-6"},{default:qe(()=>n[0]||(n[0]=[O("span",null,"←",-1),we(" 返回首页 ",-1)])),_:1,__:[0]}),n[1]||(n[1]=O("h1",{class:"text-4xl md:text-5xl font-bold text-gradient mb-4"}," 关于游戏模拟器 ",-1)),n[2]||(n[2]=O("p",{class:"text-xl text-gray-300"},"在浏览器中重温经典游戏的魅力",-1))]),n[3]||(n[3]=an('<div class="space-y-8"><section class="card p-8"><h2 class="text-2xl font-bold mb-4 flex items-center gap-2"><span>🎮</span> 项目介绍 </h2><div class="text-gray-300 space-y-4"><p> 这是一个基于 Web 技术的游戏模拟器，让你可以在浏览器中直接运行经典的掌机和家用机游戏。 无需下载安装任何软件，只需上传游戏文件即可开始游戏。 </p><p> 项目使用了 EmulatorJS 作为核心模拟器引擎，支持多种经典游戏平台， 并针对移动设备进行了优化，提供了良好的触屏游戏体验。 </p></div></section><section class="card p-8"><h2 class="text-2xl font-bold mb-4 flex items-center gap-2"><span>🎯</span> 支持的游戏平台 </h2><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="bg-black/20 rounded-lg p-4"><h3 class="font-semibold mb-2">Game Boy / Game Boy Color</h3><p class="text-sm text-gray-400">支持 .gb, .gbc 格式</p></div><div class="bg-black/20 rounded-lg p-4"><h3 class="font-semibold mb-2">Game Boy Advance</h3><p class="text-sm text-gray-400">支持 .gba 格式</p></div><div class="bg-black/20 rounded-lg p-4"><h3 class="font-semibold mb-2">Nintendo Entertainment System</h3><p class="text-sm text-gray-400">支持 .nes 格式</p></div><div class="bg-black/20 rounded-lg p-4"><h3 class="font-semibold mb-2">Super Nintendo</h3><p class="text-sm text-gray-400">支持 .snes, .smc 格式</p></div></div></section><section class="card p-8"><h2 class="text-2xl font-bold mb-4 flex items-center gap-2"><span>⚡</span> 技术栈 </h2><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center p-4 bg-black/20 rounded-lg"><div class="text-2xl mb-2">🟢</div><div class="font-semibold">Vue 3</div><div class="text-xs text-gray-400">前端框架</div></div><div class="text-center p-4 bg-black/20 rounded-lg"><div class="text-2xl mb-2">⚡</div><div class="font-semibold">Vite</div><div class="text-xs text-gray-400">构建工具</div></div><div class="text-center p-4 bg-black/20 rounded-lg"><div class="text-2xl mb-2">🎨</div><div class="font-semibold">Tailwind CSS</div><div class="text-xs text-gray-400">样式框架</div></div><div class="text-center p-4 bg-black/20 rounded-lg"><div class="text-2xl mb-2">🎮</div><div class="font-semibold">EmulatorJS</div><div class="text-xs text-gray-400">模拟器引擎</div></div></div></section><section class="card p-8"><h2 class="text-2xl font-bold mb-4 flex items-center gap-2"><span>📖</span> 使用说明 </h2><div class="space-y-4"><div class="flex items-start gap-3"><span class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span><div><h3 class="font-semibold">选择游戏</h3><p class="text-gray-400 text-sm"> 从内置游戏中选择或上传你的游戏文件 </p></div></div><div class="flex items-start gap-3"><span class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span><div><h3 class="font-semibold">选择模拟器核心</h3><p class="text-gray-400 text-sm"> 系统会自动推荐最适合的模拟器核心 </p></div></div><div class="flex items-start gap-3"><span class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span><div><h3 class="font-semibold">开始游戏</h3><p class="text-gray-400 text-sm"> 点击启动游戏按钮，享受游戏乐趣 </p></div></div></div></section><section class="card p-8 text-center"><h2 class="text-2xl font-bold mb-4">版权声明</h2><p class="text-gray-400 text-sm"> 本项目仅供学习和研究使用。请确保你拥有所上传游戏的合法版权。 我们不提供任何受版权保护的游戏文件。 内置游戏均来自网上，如果内置的游戏有相关版权影响，请通过邮件立即联系我，我会立刻删除！ 邮箱：<EMAIL> </p></section></div>',1))])])}}}),km=[{path:"/",name:"Home",component:Of,meta:{title:"首页 - 游戏模拟器"}},{path:"/library",name:"GameLibrary",component:tp,meta:{title:"游戏库 - 游戏模拟器"}},{path:"/play",name:"GamePlay",component:ym,meta:{title:"游戏中 - 游戏模拟器"}},{path:"/about",name:"About",component:Sm,meta:{title:"关于 - 游戏模拟器"}},{path:"/:pathMatch(.*)*",redirect:"/"}],Ca=af({history:zu(),routes:km,scrollBehavior(e,t,n){return n||{top:0}}});Ca.beforeEach((e,t,n)=>{var s;(s=e.meta)!=null&&s.title&&(document.title=e.meta.title),n()});const Aa=ta(xf);Aa.use(Ca);Aa.mount("#app");
