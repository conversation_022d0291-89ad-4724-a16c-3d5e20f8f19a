.ejs_parent {
    background: #000;
    overflow: hidden;
    position: relative;
    font-family: Avenir, "Avenir Next", "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-weight: 500;
    line-height: 1.7;
    width: 100%;
    height: 100%;
    color: #bcbcbc;
    outline-width: 0;
    outline: none;
    font-size: 14px;
}

.ejs_parent *,
.ejs_parent *::after,
.ejs_parent *::before {
    font-family: Avenir, "Avenir Next", "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
    box-sizing: border-box;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.ejs_parent ::-webkit-scrollbar {
    width: 8px;
    height: 16px;
}

.ejs_parent ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(var(--ejs-primary-color));
}

.ejs_parent ::-webkit-scrollbar-track {
    border-radius: 10px;
    background: rgba(64, 64, 64, 0.5)
}

.ejs_ad_iframe {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 1;
    transform: translate(-50%, -50%);
}

.ejs_ad_close {
    cursor: pointer;
}

.ejs_ad_close:after {
    content: "";
    position: absolute;
    border-left: 20px solid transparent;
    border-right: 20px solid #a7a7a77d;
    border-top: 20px solid #a7a7a77d;
    border-bottom: 20px solid transparent;
    right: 0;
}

.ejs_ad_close a {
    right: 4px;
    top: 4px;
    position: absolute;
    border: 0;
    padding: 0;
    width: 15px;
    height: 15px;
    z-index: 99;
}

.ejs_ad_close a:before {
    content: "";
    border-bottom: 1px solid #fff;
    transform: rotate(45deg) translateY(-45%);
    width: 15px;
    height: 15px;
    display: block;
    position: absolute;
    top: 0;
    right: 0;
}

.ejs_ad_close a:after {
    content: "";
    border-bottom: 1px solid #fff;
    width: 15px;
    height: 15px;
    display: block;
    position: absolute;
    right: 0;
    background: transparent;
    top: 0;
    transform: rotate(-45deg) translateY(-50%) translateX(0);
}

.ejs_game {
    width: inherit;
    height: inherit;
    background-color: var(--ejs-background-color);
    cursor: default;
    position: relative;
    overflow: hidden;
}

.ejs_game_background {
    background-image: var(--ejs-background-image);
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    text-align: -webkit-auto;
}

.ejs_game_background_blur:before,
.ejs_game_background_blur:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--ejs-background-image);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.ejs_game_background_blur:before {
    background-size: cover;
    filter: blur(10px);
    transform: scale(1.1);
}

.ejs_start_button {
    position: absolute;
    bottom: 65px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    box-sizing: inherit;
    display: flex;
    justify-content: center;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
    font-size: 20px;
    line-height: 45px;
    text-transform: uppercase;
    font-weight: bolder;
    position: relative;
    text-decoration: none;
    width: fit-content;
    padding-left: 40px;
    padding-right: 40px;
    white-space: nowrap;
    height: 45px;
    border: 0;
    color: #fff !important;
    border-radius: 35px;
    text-align: center;
    background-color: rgba(var(--ejs-primary-color), 1);
    box-shadow: 0 0 0 0 #222, 0 0px 0px 0 #111, inset 0 0px 0px 0 rgba(250, 250, 250, 0.2), inset 0 0px 0px 0px rgba(0, 0, 0, 0.5);
}

.ejs_start_button_border {
    border: 0.5px solid #333;
}

.ejs_start_button:active,
.ejs_start_button:hover {
    animation: ejs_start_button_pulse 2s infinite;
}

@keyframes ejs_start_button_pulse {
    50% {
        box-shadow: 0 0 0 0 #222, 0 3px 7px 0 #111, inset 0 1px 1px 0 rgba(250, 250, 250, 0.2), inset 0 0px 15px 1px rgba(0, 0, 0, 0.5);
    }

    0%,
    100% {
        box-shadow: 0 0 0 0 #222, 0 0px 0px 0 #111, inset 0 0px 0px 0 rgba(250, 250, 250, 0.2), inset 0 0px 0px 0px rgba(0, 0, 0, 0.5);
    }
}

.ejs_loading_text {
    position: absolute;
    bottom: 20px;
    font-weight: 500;
    left: 50%;
    transform: translateX(-50%);
    box-sizing: inherit;
    font-size: 12px;
    color: #bcbcbc;
    text-align: center;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.ejs_loading_text_glow {
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 10px;
    padding: 1px 5px 1px 5px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.9);
}

.ejs_error_text {
    bottom: 10%;
    color: #F82300;
    border-radius: 15px;
    padding: 1.5px 8px 1.5px 8px;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.6);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
}

.ejs_canvas {
    width: 100%;
    height: 100%;
}

.ejs_canvas_parent {
    width: 100%;
    height: 100%;
}

.ejs_context_menu {
    position: absolute;
    display: none;
    z-index: 9;
    background: rgba(16, 16, 16, 0.9);
    border-radius: 3px;
    font-size: 13px;
    min-width: 140px;
    padding: 8px;
    box-sizing: inherit;
}

.ejs_context_menu li {
    padding: 4px 11px;
    text-align: center;
}

.ejs_context_menu li a {
    color: #999;
    display: block;
    font-size: 13px;
}

.ejs_context_menu ul {
    color: #999;
    display: block;
    font-size: 13px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.ejs_context_menu li:hover {
    background: rgba(var(--ejs-primary-color), 1);
    border-radius: 4px;
    box-shadow: 0 0 0 5px rgba(var(--ejs-primary-color), 0.5);
    outline: 0;
}

.ejs_context_menu li:hover a {
    color: #fff;
}

.ejs_context_menu_tab {
    text-align: center;
    width: 100%;
    padding: 10px;
    overflow-wrap: anywhere;
}

.ejs_list_selector {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    font-size: 13px;
    min-width: 140px;
    padding: 8px;
    box-sizing: inherit;
    width: 10%;
    max-width: 15%;
    top: 10%;
    left: 0px;
    height: 80%;
    overflow: auto;
    position: sticky;
}

.ejs_list_selector li {
    padding: 4px 11px;
    text-align: center;
}

.ejs_list_selector li a {
    color: #999;
    display: block;
    font-size: 13px;
}

.ejs_list_selector ul {
    color: #999 !important;
    display: block;
    font-size: 13px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.ejs_list_selector li:hover {
    background: rgba(var(--ejs-primary-color), 1);
    border-radius: 4px;
    box-shadow: 0 0 0 5px rgba(var(--ejs-primary-color), 0.5);
    outline: 0;
}

.ejs_list_selector li:hover a {
    color: #fff !important;
}

.ejs_active_list_element {
    background: rgba(var(--ejs-primary-color), 1);
    border-radius: 4px;
    box-shadow: 0 0 0 5px rgba(var(--ejs-primary-color), 0.25);
    outline: 0;
    color: #fff !important;
}

.ejs_active_list_element a {
    color: #fff !important;
}

.ejs_svg_rotate {
    transform: rotate(90deg);
}

.ejs_small_screen .ejs_settings_parent::before {
    border: none;
}

.ejs_small_screen .ejs_settings_parent::after {
    border: none;
}

.ejs_small_screen .ejs_settings_center_right {
    right: -35%
}

.ejs_small_screen .ejs_settings_center_left {
    right: -135%
}

.ejs_small_screen .ejs_settings_center_right::after {
    right: 25%;
}

.ejs_small_screen .ejs_settings_center_left::after {
    left: 25%;
}

.ejs_small_screen .ejs_menu_bar {
    transition: opacity .4s ease-in-out, transform .4s ease-in-out;
    position: absolute;
    transform: translate(-50%, 0);
    width: 300px;
    max-height: 260px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    background: rgba(0, 0, 0, .9);
    padding: 10px;
    z-index: 9999;
    left: 50%;
    bottom: 0;
    color: #fff;
}

.ejs_small_screen .ejs_menu_bar_hidden {
    opacity: 0;
    pointer-events: none;
    transform: translate(-50%, 100%);
}

.ejs_small_screen .ejs_menu_button {
    width: 135px;
    margin: 0px;
    margin-left: 2px !important;
    margin-right: 0 !important;
    line-height: 1;
    background: 0 0;
    border: 0;
    border-radius: 3px;
    color: inherit;
    cursor: pointer;
    flex-shrink: 0;
    overflow: visible;
    padding: 7px;
    position: relative;
    transition: all .3s ease;
}

.ejs_small_screen .ejs_menu_button:hover {
    background: rgba(var(--ejs-primary-color), 1);
    color: #fff;
    box-shadow: none;
}

.ejs_small_screen .ejs_menu_button svg {
    float: left;
    transition: transform .3s ease;
}

.ejs_small_screen .ejs_menu_text {
    position: static;
    color: #fff;
    background: 0 0;
    opacity: 1;
    padding: 0;
    transform: scale(.8) !important;
    font-size: 12px;
}

.ejs_small_screen .ejs_menu_bar_spacer {
    display: none;
}

.ejs_small_screen .ejs_volume_parent span {
    display: none;
}

.ejs_small_screen .ejs_volume_parent button {
    width: 30px;
}

.ejs_big_screen .ejs_menu_bar_hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateY(100%);
}

.ejs_big_screen .ejs_settings_parent {
    right: -3px;
}

/* .ejs_big_screen .ejs_settings_parent::after {
    right: 15px;
} */
.ejs_big_screen .ejs_settings_text {
    display: none;
}

.ejs_big_screen .ejs_disks_text {
    display: none;
}

.ejs_big_screen .ejs_menu_bar_spacer {
    flex: 1;
}

.ejs_big_screen .ejs_menu_button svg {
    transition: transform .3s ease;
}

.ejs_big_screen .ejs_menu_button {
    width: auto;
    margin: auto;
    font-family: Avenir, "Avenir Next", "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
    margin-right: 2px;
    touch-action: manipulation;
    background: transparent;
    border: 0;
    border-radius: 3px;
    color: inherit;
    cursor: pointer;
    flex-shrink: 0;
    overflow: visible;
    padding: 7px;
    position: relative;
    transition: all .3s ease;
}

.ejs_big_screen .ejs_menu_button:hover {
    background: rgba(var(--ejs-primary-color), 1);
    color: #fff;
    box-shadow: none;
}

.ejs_big_screen .ejs_menu_button:hover .ejs_menu_text {
    transform: translate(0, 0) scale(1);
    opacity: 1;
}

.ejs_big_screen .ejs_menu_text_right {
    right: 0;
    left: auto !important;
}

.ejs_big_screen .ejs_menu_text {
    left: 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 3px;
    bottom: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    color: #4f5b5f;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 10px;
    opacity: 0;
    padding: 5px 7.5px;
    pointer-events: none;
    position: absolute;
    transform: translate(0, 10px) scale(0.8);
    transform-origin: 0 100%;
    transition: transform .2s .1s ease, opacity .2s .1s ease;
    white-space: nowrap;
    z-index: 2;
}

.ejs_big_screen .ejs_menu_text::before {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(255, 255, 255, 0.9);
    bottom: -4px;
    content: '';
    height: 0;
    left: 16px;
    position: absolute;
    transform: translateX(-50%);
    width: 0;
    z-index: 2;
}

.ejs_big_screen .ejs_menu_text_right::before {
    left: auto !important;
    right: 16px;
    transform: translateX(50%) !important;
}

.ejs_big_screen .ejs_menu_bar {
    padding: 15px 10px 10px;
    background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
    bottom: 0;
    color: #fff;
    left: 0;
    position: absolute;
    right: 0;
    transition: opacity .4s ease-in-out, transform .4s ease-in-out;
    z-index: 9999;
    align-items: center;
    display: flex;
    justify-content: flex-start;
    text-align: center;
}

.ejs_menu_button.shadow {
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.7);
}

.ejs_menu_bar svg {
    display: block;
    fill: currentColor;
    height: 18px;
    pointer-events: none;
    width: 18px;
}

.ejs_popup_container {
    background: rgba(0, 0, 0, 0.8);
    text-align: center;
    z-index: 9999;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    color: #ccc;
}

.ejs_popup_container *,
.ejs_popup_container *::after,
.ejs_popup_container *::before {
    box-sizing: border-box;
    color: #bcbcbc;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.ejs_popup_container h4 {
    color: #ccc;
    font-size: 24px;
    margin: 0;
    padding: 10px;
}

.ejs_button {
    border-radius: .25rem;
    font-size: .875rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: .5rem;
    padding-bottom: .5rem;
    display: inline-block;
    background-color: rgba(var(--ejs-primary-color), 1);
    margin: 0 10px;
    color: #fff !important;
    touch-action: manipulation;
    cursor: pointer;
}

.ejs_popup_body {
    height: calc(100% - 130px);
    overflow: auto;
}

.ejs_control_body {
    color: #bcbcbc !important;
    border: unset;
}

.ejs_control_body input[type='text'] {
    background-color: #fff;
    border: 1px solid #000;
    font-size: 12px;
    font-weight: 700;
    color: #000 !important;
}

.ejs_control_player_bar {
    margin: 0;
    padding: 0;
}

.ejs_control_player_bar ul {
    list-style: none;
}

.ejs_control_player_bar li {
    color: #bcbcbc !important;
    display: inline-block;
}

.ejs_control_player_bar a {
    padding: 2px 5px;
    color: #bcbcbc !important;
    font-size: 14px;
    cursor: pointer;
}

.ejs_control_player_bar li:hover:not(.ejs_control_selected) {
    background-color: #333;
    border-bottom: 1px solid #333;
}

.ejs_control_selected {
    border-bottom: 1px solid #fff;
    background-color: #fff;
}

.ejs_control_selected a {
    color: #000 !important;
}

.ejs_control_bar:hover {
    background-color: #2d2d2d;
}

.ejs_control_set_button {
    float: none;
    padding: .1rem .5rem;
    background-color: rgba(var(--ejs-primary-color), 1);
    color: #fff !important;
    border-radius: .25rem;
    cursor: pointer;
    font-size: 14px;
}

.ejs_control_row label::before {
    top: -0.15rem !important;
}

.ejs_control_row label::after {
    top: calc(-0.15rem + 2px) !important;
}

.ejs_popup_box {
    position: absolute;
    width: 300px;
    top: 50%;
    margin-left: -150px;
    margin-top: -50px;
    left: 50%;
    background: rgba(0, 0, 0, 0.8) !important;
    padding: 15px 0;
}

.ejs_virtualGamepad_parent {
    width: 100%;
    position: absolute;
    bottom: 50px;
}

.ejs_virtualGamepad_top {
    position: absolute;
    bottom: 250px;
    width: 100%;
}

.ejs_virtualGamepad_bottom {
    position: absolute;
    bottom: 10px;
    height: 30px;
    width: 124px;
    left: 50%;
    margin-left: -62px;
}

.ejs_virtualGamepad_left {
    position: absolute;
    bottom: 50px;
    width: 125px;
    height: 125px;
    left: 10px;
}

.ejs_virtualGamepad_right {
    position: absolute;
    bottom: 50px;
    width: 130px;
    height: 130px;
    right: 10px;
}

.ejs_virtualGamepad_button {
    position: absolute;
    font-size: 20px;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border: 1px solid #ccc;
    border-radius: 50%;
    font-size: 30px;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.15);
    user-select: none;
    transition: all .2s;
}

.ejs_virtualGamepad_button_down {
    background-color: #000000ad;
}

.ejs_dpad_main {
    touch-action: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: .7;
}

.ejs_dpad_horizontal {
    width: 100%;
    height: 36px;
    transform: translate(0, -50%);
    position: absolute;
    left: 0;
    top: 50%;
    border-radius: 5px;
    overflow: hidden;
}

.ejs_dpad_horizontal:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    z-index: 1;
    transform: translate(0, -50%);
    width: 0;
    height: 0;
    border: 8px solid;
    border-color: transparent #333 transparent transparent;
}

.ejs_dpad_horizontal:after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    z-index: 1;
    transform: translate(0, -50%);
    width: 0;
    height: 0;
    border: 8px solid;
    border-color: transparent transparent transparent #333;
}

.ejs_dpad_vertical {
    width: 36px;
    height: 100%;
    transform: translate(-50%, 0);
    position: absolute;
    left: 50%;
    border-radius: 5px;
    overflow: hidden;
}

.ejs_dpad_vertical:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 1;
    transform: translate(-50%, 0);
    width: 0;
    height: 0;
    border: 8px solid;
    border-color: transparent transparent #333 transparent;
}

.ejs_dpad_vertical:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    z-index: 1;
    transform: translate(-50%, 0);
    width: 0;
    height: 0;
    border: 8px solid;
    border-color: #333 transparent transparent transparent;
}

.ejs_dpad_bar {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #787878;
}

.ejs_dpad_left_pressed .ejs_dpad_horizontal:before {
    border-right-color: #fff;
}

.ejs_dpad_right_pressed .ejs_dpad_horizontal:after {
    border-left-color: #fff;
}

.ejs_dpad_up_pressed .ejs_dpad_vertical:before {
    border-bottom-color: #fff;
}

.ejs_dpad_down_pressed .ejs_dpad_vertical:after {
    border-top-color: #fff
}

@keyframes ejs_settings_parent_animation {
    0% {
        opacity: 0.5;
        transform: translateY(10px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.ejs_settings_parent {
    animation: ejs_settings_parent_animation .2s ease;
    background: rgba(29, 29, 29, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(49, 49, 49, 0.9);
    bottom: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    color: #4f5b5f;
    font-size: 16px;
    margin-bottom: 10px;
    position: absolute;
    text-align: left;
    white-space: nowrap;
    z-index: 9999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* .ejs_settings_parent::after {
    border: 5px solid transparent;
    border-top-color: rgba(119, 119, 119, 0.9);
    content: '';
    height: 0;
    position: absolute;
    top: 100%;
    width: 0;
} */
.ejs_settings_parent::before,
.ejs_settings_parent::after {
    position: absolute;
    right: 15px;
    width: 0;
    height: 0;
    content: '';
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top-width: 5px;
    border-top-style: solid;
}

.ejs_settings_parent::before {
    top: calc(100% + 1px);
    border-top-color: rgba(49, 49, 49, 0.9);
}

.ejs_settings_parent::after {
    top: 100%;
    border-top-color: rgba(29, 29, 29, 0.9);
}

.ejs_settings_transition {
    overflow: hidden;
    transition: height .35s cubic-bezier(0.4, 0, 0.2, 1), width .35s cubic-bezier(0.4, 0, 0.2, 1);
}

.ejs_settings_main_bar {
    align-items: center;
    color: #999;
    display: flex;
    font-size: 13px;
    padding: 4px 11px;
    user-select: none;
    width: 100%;
    padding-right: 28px;
    background: transparent;
    border: 0;
    border-radius: 3px;
    flex-shrink: 0;
    overflow: visible;
    position: relative;
    transition: all .3s ease;
    box-sizing: border-box;
}

.ejs_settings_main_bar::after {
    border: 4px solid transparent;
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: rgba(79, 91, 95, 0.8);
    right: 5px;
}

.ejs_settings_main_bar>span {
    align-items: inherit;
    display: flex;
    width: 100%;
}

.ejs_settings_main_bar:hover {
    background: rgba(var(--ejs-primary-color), 1);
    color: #fff;
    cursor: pointer;
}

.ejs_settings_main_bar:hover::after {
    border-left-color: currentColor;
}

.ejs_settings_main_bar_selected {
    align-items: center;
    display: flex;
    margin-left: auto;
    margin-right: -5px;
    overflow: hidden;
    padding-left: 25px;
    pointer-events: none;
}

.ejs_setting_menu {
    padding: 7px;
    overflow-x: hidden;
    overflow-y: auto;
}

.ejs_parent_option_div {
    display: flex;
    flex-direction: column;
    max-height: inherit;
    overflow: hidden;
}

.ejs_back_button {
    font-weight: 500;
    margin: 7px;
    margin-bottom: 3px;
    padding-left: 28px;
    position: relative;
    width: calc(100% - 14px);
    align-items: center;
    color: #999;
    display: flex;
    font-size: 13px;
    padding: 4px 11px;
    user-select: none;

    background: transparent;
    border: 0;
    border-radius: 3px;
    cursor: pointer;
    flex-shrink: 0;
    overflow: visible;
    transition: all .3s ease;
}

.ejs_back_button:hover {
    background: rgba(var(--ejs-primary-color), 1);
    color: #fff;
}

.ejs_back_button:hover::after {
    border-right-color: currentColor;
}

.ejs_back_button::before {
    background: #b7c5cd;
    box-shadow: 0 1px 0 #fff;
    content: '';
    height: 1px;
    left: 0;
    margin-top: 4px;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 100%;
}

.ejs_back_button::after {
    border: 4px solid transparent;
    border-right-color: rgba(79, 91, 95, 0.8);
    left: 7px;
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.ejs_menu_text_a {
    align-items: center;
    display: flex;
    font-size: 13px;
    padding: 4px 11px;
    user-select: none;
    width: 100%;
}

.ejs_option_row {
    padding-left: 7px;
}

.ejs_option_row:hover::before {
    background: rgba(0, 0, 0, 0.1);
}

.ejs_option_row::before {
    background: rgba(204, 204, 204, 0.1);
    content: '';
    display: block;
    flex-shrink: 0;
    height: 16px;
    margin-right: 10px;
    transition: all .3s ease;
    width: 16px;
    border-radius: 100%;
}

.ejs_option_row::after {
    background: #fff;
    border: 0;
    height: 6px;
    left: 12px;
    opacity: 0;
    top: 50%;
    transform: translateY(-50%) scale(0);
    transition: transform .3s ease, opacity .3s ease;
    width: 6px;
    border-radius: 100%;
    content: '';
    position: absolute;
}

.ejs_option_row_selected::before {
    background: rgba(var(--ejs-primary-color), 1);
}

.ejs_option_row_selected::after {
    opacity: 1;
    transform: translateY(-50%) scale(1);
}

.ejs_option_row>span {
    align-items: center;
    display: flex;
    width: 100%;
}

.ejs_button_style {
    margin: 0px;
    background: transparent;
    border: 0;
    border-radius: 3px;
    cursor: pointer;
    flex-shrink: 0;
    overflow: visible;
    padding: 7px;
    position: relative;
    transition: all .3s ease;

    align-items: center;
    color: #999;
    display: flex;
    font-size: 13px;
    user-select: none;
    width: 100%;
}

.ejs_button_style:hover {
    background: rgba(var(--ejs-primary-color), 1);
    color: #fff;
}

.ejs_cheat_heading {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    font-weight: 600 !important;
    font-size: 1.25rem;
    line-height: 1.25 !important;
    color: rgba(var(--ejs-primary-color), 1) !important;
}

.ejs_cheat_close {
    font: inherit;
    line-height: inherit;
    width: auto;
    background: transparent;
    border: 0;
    color: #bcbcbc !important;
    cursor: pointer;
}

.ejs_cheat_close::before {
    content: "\2715";
    color: #bcbcbc !important;
    font: inherit;
    line-height: inherit;
    width: auto;
}

.ejs_cheat_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ejs_cheat_main {
    margin-top: 2rem;
    margin-bottom: 2rem;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.8);
    text-align: left;
    color: #bcbcbc !important;
    border: unset;
}

.ejs_cheat_code {
    color: #000 !important;
    font-size: 1rem;
    padding: .4rem;
    max-width: 100%;
}

@keyframes ejs_cheat_animation {
    from {
        transform: translateY(15%)
    }

    to {
        transform: translateY(0)
    }
}

.ejs_cheat_parent {
    background-color: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(238, 238, 238, 0.2);
    padding: 30px;
    min-width: 200px;
    max-width: 500px;
    max-height: 100vh;
    border-radius: 4px;
    overflow-y: auto;
    box-sizing: border-box;
    will-change: transform;
    animation: ejs_cheat_animation .3s cubic-bezier(0, 0, 0.2, 1);
    font-size: 14px;
}

.ejs_popup_container_box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
}

.ejs_popup_submit {
    touch-action: manipulation;
    font: inherit;
    line-height: inherit;
    width: auto;
}

.ejs_button_button {
    color: #fff !important;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: .5rem;
    padding-bottom: .5rem;
    background-color: #929292;
    border-radius: .25rem;
    border-style: none;
    border-width: 0;
    cursor: pointer;
    -webkit-appearance: button;
    appearance: button;
    text-transform: none;
    overflow: visible;
    margin: 0;
    will-change: transform;
    transition: transform .25s ease-out, -webkit-transform .25s ease-out;
}

.ejs_button_button:hover {
    transform: scale(1.05);
}

.ejs_cheat_rows {
    max-width: 320px;
    margin: 0 auto;
    text-align: left;
    width: 100%;
    float: none;
    user-select: text !important;
}

.ejs_cheat_row {
    padding-left: 2.25rem;
    position: relative;
    padding: .2em 0;
    clear: both;
}

.ejs_cheat_row:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.ejs_cheat_row input[type=checkbox] {
    position: absolute;
    z-index: -1;
    opacity: 0;
    box-sizing: border-box;
    width: auto;
}

.ejs_cheat_row label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
    word-break: break-word;
}

.ejs_cheat_row label::before {
    position: absolute;
    top: 0rem;
    display: block;
    height: 1rem;
    content: "";
    background-color: #fff;
    border: #adb5bd solid 1px;
    left: -2.25rem;
    width: 1.75rem;
    pointer-events: all;
    border-radius: .5rem;
}

.ejs_cheat_row input:checked+label::before {
    color: #fff;
    border-color: rgba(var(--ejs-primary-color), 1);
    background-color: rgba(var(--ejs-primary-color), 1);
}

.ejs_cheat_row label::after {
    position: absolute;
    display: block;
    content: "";
    background-repeat: no-repeat;
    background-position: center center;
    top: calc(0rem + 2px);
    left: calc(-2.25rem + 2px);
    width: calc(1rem - 4px);
    height: calc(1rem - 4px);
    background-color: #adb5bd;
    border-radius: .5rem;
    transition: transform .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-transform .15s ease-in-out;
}

.ejs_cheat_row input:checked+label::after {
    background-color: #fff;
    -webkit-transform: translateX(0.75rem);
    transform: translateX(0.75rem);
}

.ejs_cheat_row_button {
    position: absolute;
    padding: .1rem .5rem;
    background-color: rgba(var(--ejs-primary-color), 1);
    color: #fff !important;
    border-radius: .25rem;
    cursor: pointer;
    right: .025rem;
    border: 0;
}

.ejs_small_screen .ejs_volume_parent input[type='range'] {
    width: 100%;
}

.ejs_big_screen .ejs_volume_parent {
    max-width: 110px;
}

.ejs_volume_parent {
    align-items: center;
    display: flex;
    flex: 1;
    position: relative;
}

.ejs_volume_parent {
    padding-right: 15px;
}

.ejs_volume_parent::-webkit-media-controls {
    display: none;
}

.ejs_volume_parent input[type='range'] {
    display: block;
    -webkit-appearance: none;
    appearance: none;
    border: 0;
    border-radius: 28px;
    color: rgba(var(--ejs-primary-color), 1);
    margin: 0;
    padding: 0;
    transition: box-shadow 0.3s ease;
    width: 100%;
    background: white;
    height: 6px;
}

.ejs_volume_parent input[type='range']::-webkit-slider-runnable-track {
    background-color: rgba(255, 255, 255, 0.25);
    outline: 0;
    background: transparent;
    border: 0;
    border-radius: 3px;
    height: 6px;
    transition: box-shadow 0.3s ease;
    user-select: none;
    background-image: linear-gradient(to right, currentColor var(--value, 0%), transparent var(--value, 0%))
}

.ejs_volume_parent input[type='range']::-webkit-slider-thumb {
    background: #fff;
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2);
    height: 14px;
    position: relative;
    transition: all 0.2s ease;
    width: 14px;
    -webkit-appearance: none;
    margin-top: -4px
}

.ejs_volume_parent input[type='range']::-moz-range-track {
    background-color: rgba(255, 255, 255, 0.25);
    outline: 0;
    background: transparent;
    border: 0;
    border-radius: 3px;
    height: 6px;
    transition: box-shadow 0.3s ease;
    user-select: none
}

.ejs_volume_parent input[type='range']::-moz-range-thumb {
    background: #fff;
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2);
    height: 14px;
    position: relative;
    transition: all 0.2s ease;
    width: 14px
}

.ejs_volume_parent input[type='range']::-moz-range-progress {
    background: currentColor;
    border-radius: 3px;
    height: 6px
}

.ejs_volume_parent input[type='range']::-ms-track {
    background-color: rgba(255, 255, 255, 0.25);
    outline: 0;
    background: transparent;
    border: 0;
    border-radius: 3px;
    height: 6px;
    transition: box-shadow 0.3s ease;
    user-select: none;
    color: transparent
}

.ejs_volume_parent input[type='range']::-ms-fill-upper {
    background: transparent;
    border: 0;
    border-radius: 3px;
    height: 6px;
    transition: box-shadow 0.3s ease;
    user-select: none
}

.ejs_volume_parent input[type='range']::-ms-fill-lower {
    background: transparent;
    border: 0;
    border-radius: 3px;
    height: 6px;
    transition: box-shadow 0.3s ease;
    user-select: none;
    background: currentColor
}

.ejs_volume_parent input[type='range']::-ms-thumb {
    background: #fff;
    border: 0;
    border-radius: 100%;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2);
    height: 14px;
    position: relative;
    transition: all 0.2s ease;
    width: 14px;
    margin-top: 0
}

.ejs_volume_parent input[type='range']::-ms-tooltip {
    display: none
}

.ejs_volume_parent input[type='range']:focus {
    outline: 0
}

.ejs_volume_parent input[type='range']::-moz-focus-outer {
    border: 0
}

.ejs_volume_parent input[type='range']:active::-webkit-slider-thumb {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5)
}

.ejs_volume_parent input[type='range']:active::-moz-range-thumb {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5)
}

.ejs_volume_parent input[type='range']:active::-ms-thumb {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(47, 52, 61, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.5)
}

.ejs_message {
    float: none;
    overflow: hidden;
    position: absolute;
    top: 0;
    color: red;
    font-size: 17px;
    padding: 10px;
    text-align: left;
    text-shadow: rgb(0, 0, 0) 1px 1px 1px;
}

.ejs_virtualGamepad_open {
    display: inline-block;
    width: 24px;
    height: 24px;
    color: #fff;
    position: absolute;
    top: 5px;
    right: 5px;
    opacity: .5;
    z-index: 999;
    cursor: pointer;
}

.ejs_virtualGamepad_open svg {
    fill: currentColor;
}

.ejs_netplay_header {
    margin-top: .5rem;
    margin-bottom: .5rem;
    line-height: 1.5;
    color: rgba(0, 0, 0, .8);
    text-align: left;
}

.ejs_netplay_header input,
.ejs_netplay_header select {
    font-size: 1rem;
    padding: .4rem;
    max-width: 100%;
    color: #000 !important;
    background-color: #fff !important;
    margin: 0;
    height: 2rem;
    display: block;
    font-family: Arial;
    border: 0px;
}

.ejs_netplay_name_heading {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    font-weight: 600 !important;
    font-size: 1.25rem;
    line-height: 1.25 !important;
    color: rgba(var(--ejs-primary-color), 1) !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ejs_netplay_table {
    font-family: Avenir, "Avenir Next", "Helvetica Neue", "Segoe UI", Helvetica, Arial, sans-serif;
    font-size: 0.8rem;
    padding: 0 10px;
}

.ejs_netplay_join_button {
    float: none;
    padding: .1rem .5rem;
    background-color: rgba(var(--ejs-primary-color), 1);
    color: #fff !important;
    border-radius: .25rem;
    cursor: pointer;
}

.ejs_netplay_table_row:hover {
    background-color: #2d2d2d;
}

.ejs_gamepad_dropdown {
    background-color: var(--ejs-background-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: inherit;
    outline: none;
    cursor: pointer;
    font-size: inherit;
}

.ejs_gamepad_dropdown:focus, .ejs_gamepad_dropdown:active, .ejs_gamepad_dropdown:hover {
    box-shadow: 0 0 0 2px rgba(51, 153, 255, 0.6);
    background-color: var(--ejs-background-color);
}

.ejs_gamepad_section {
    font-size: 12px;
    justify-content: center;
    display: flex;
    align-items: center;
}
