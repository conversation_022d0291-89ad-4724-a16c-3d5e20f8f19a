{"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "Restart": "Reiniciar", "Pause": "Pausar", "Play": "<PERSON><PERSON><PERSON>", "Save State": "Guardar E<PERSON>o", "Load State": "<PERSON><PERSON>", "Control Settings": "Ajustes de Control", "Cheats": "Trucos", "Cache Manager": "Administrador de Caché", "Export Save File": "Exportar Archivo de Guardado", "Import Save File": "Importar Archivo de Guardado", "Netplay": "Juego en Red", "Mute": "Silenciar", "Unmute": "Activar Sonido", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Enter Fullscreen": "<PERSON><PERSON><PERSON>", "Exit Fullscreen": "Salir de Pantalla Completa", "Context Menu": "Menú Contextual", "Reset": "Reiniciar", "Clear": "Limpiar", "Close": "<PERSON><PERSON><PERSON>", "QUICK SAVE STATE": "GUARDADO RÁPIDO DE ESTADO", "QUICK LOAD STATE": "CARGA RÁPIDA DE ESTADO", "CHANGE STATE SLOT": "CAMBIAR RANURA DE ESTADO", "FAST FORWARD": "AVANCE RÁPIDO", "Player": "Jugador", "Connected Gamepad": "<PERSON><PERSON>", "Gamepad": "Mando", "Keyboard": "Teclado", "Set": "Guardar", "Add Cheat": "<PERSON><PERSON><PERSON>", "Note that some cheats require a restart to disable": "Algunos trucos requieren reiniciar para desactivarlos", "Create a Room": "<PERSON><PERSON><PERSON> una Sala", "Rooms": "Salas", "Start Game": "In<PERSON>ar j<PERSON><PERSON>", "Click to resume Emulator": "Haz clic para reanudar el Emulador", "Drop save state here to load": "Suelta el estado guardado aquí para cargarlo", "Loading...": "Cargando...", "Download Game Core": "Descargando <PERSON>ego", "Outdated graphics driver": "Controlador de gráficos obsoleto", "Decompress Game Core": "Descomprimiendo Núcleo del Juego", "Download Game Data": "Descargando Datos del Juego", "Decompress Game Data": "Descomprimiendo Datos del Juego", "Shaders": "Shaders", "Disabled": "Desactivado", "2xScaleHQ": "2xScaleHQ", "4xScaleHQ": "4xScaleHQ", "CRT easymode": "CRT easymode", "CRT aperture": "CRT aperture", "CRT geom": "CRT geom", "CRT mattias": "CRT mattias", "FPS": "FPS", "show": "mostrar", "hide": "ocultar", "Fast Forward Ratio": "Proporción de Avance Rápido", "Fast Forward": "Avance <PERSON>", "Enabled": "Activado", "Save State Slot": "Guardar Ranura de Estado", "Save State Location": "Guardar Ubicación de Estado", "Download": "<PERSON><PERSON><PERSON>", "Keep in Browser": "Mantener en Navegador", "Auto": "Auto", "NTSC": "NTSC", "PAL": "PAL", "Dendy": "<PERSON><PERSON>", "8:7 PAR": "8:7 PAR", "4:3": "4:3", "Low": "<PERSON><PERSON>", "High": "Alto", "Very High": "<PERSON><PERSON> alto", "None": "<PERSON><PERSON><PERSON>", "Player 1": "Jugador 1", "Player 2": "jugador 2", "Both": "Ambos", "SAVED STATE TO SLOT": "ESTADO GUARDADO EN RANURA", "LOADED STATE FROM SLOT": "ESTADO CARGADO DESDE LA RANURA", "SET SAVE STATE SLOT TO": "ESTABLECER RANURA DE GUARDADO DE ESTADO EN", "Network Error": "<PERSON><PERSON><PERSON> <PERSON>", "Submit": "Enviar", "Description": "Descripción", "Code": "Código", "Add Cheat Code": "Agregar Código de Trucos", "Leave Room": "<PERSON><PERSON> la Sala", "Password": "Contraseña", "Password (optional)": "Contraseña (opcional)", "Max Players": "Ju<PERSON>res Máxi<PERSON>", "Room Name": "Nombre de la Sala", "Join": "Unirse", "Player Name": "Nombre del Jugador", "Set Player Name": "<PERSON><PERSON><PERSON>", "Left Handed Mode": "Modo para Zurdos", "Virtual Gamepad": "Mando Virtual", "Disk": "Disco", "Press Keyboard": "Presionar Teclado", "INSERT COIN": "INSERTE MONEDA", "Remove": "Eliminar", "SAVE LOADED FROM BROWSER": "GUARDAR CARGADO DESDE EL NAVEGADOR", "SAVE SAVED TO BROWSER": "GUARDAR GUARDADO EN EL NAVEGADOR", "Join the discord": "Únete al Discord", "View on GitHub": "Ver en GitHub", "Failed to start game": "Error al iniciar el juego", "Download Game BIOS": "Descargando BIOS del Juego", "Decompress Game BIOS": "Descomprimiendo BIOS del Juego", "Download Game Parent": "<PERSON><PERSON><PERSON><PERSON>", "Decompress Game Parent": "Descomprim<PERSON><PERSON>", "Download Game Patch": "Descargando Parche del Juego", "Decompress Game Patch": "Descomprimiendo Parche del Juego", "Download Game State": "Descargando Estado del Juego", "Check console": "Verificar consola", "Error for site owner": "Error para el propietario del sitio", "EmulatorJS": "EmulatorJS", "Clear All": "<PERSON><PERSON><PERSON> todo", "Take Screenshot": "Tomar Captura <PERSON> Pan<PERSON>la", "Start Screen Recording": "Iniciar grabación de pantalla", "Stop Screen Recording": "Detener grabación de pantalla", "Quick Save": "Guardado Rápido", "Quick Load": "Carga Rápida", "REWIND": "REBOBINAR", "Rewind Enabled (requires restart)": "Rebobinado Habilitado (requiere reinicio)", "Rewind Granularity": "Granularidad de Rebobinado", "Slow Motion Ratio": "Proporción de Cámara Lenta", "Slow Motion": "<PERSON><PERSON><PERSON>", "Home": "<PERSON><PERSON>o", "EmulatorJS License": "Licencia de EmulatorJS", "RetroArch License": "Licencia de RetroArch", "This project is powered by": "Este proyecto está impulsado por", "View the RetroArch license here": "Ver la licencia de RetroArch aquí", "SLOW MOTION": "CÁMARA LENTA", "A": "A", "B": "B", "SELECT": "SELECT", "START": "START", "UP": "ARRIBA", "DOWN": "ABAJO", "LEFT": "IZQUIERDA", "RIGHT": "DERECHA", "X": "X", "Y": "Y", "L": "L", "R": "R", "Z": "Z", "STICK UP": "STICK ARRIBA", "STICK DOWN": "STICK ABAJO", "STICK LEFT": "STICK IZQUIERDA", "STICK RIGHT": "STICK DERECHA", "C-PAD UP": "C-PAD ARRIBA", "C-PAD DOWN": "C-PAD ABAJO", "C-PAD LEFT": "C-PAD IZQUIERDA", "C-PAD RIGHT": "C-PAD DERECHA", "MICROPHONE": "MICRÓFONO", "BUTTON 1 / START": "BOTÓN 1 / INICIO", "BUTTON 2": "BOTÓN 2", "BUTTON": "BOTÓN", "LEFT D-PAD UP": "D-PAD IZQUIERDO ARRIBA", "LEFT D-PAD DOWN": "D-PAD IZQUIERDO ABAJO", "LEFT D-PAD LEFT": "D-PAD IZQUIERDO IZQUIERDA", "LEFT D-PAD RIGHT": "D-PAD IZQUIERDO DERECHA", "RIGHT D-PAD UP": "D-PAD DERECHO ARRIBA", "RIGHT D-PAD DOWN": "D-PAD DERECHO ABAJO", "RIGHT D-PAD LEFT": "D-PAD DERECHO IZQUIERDO", "RIGHT D-PAD RIGHT": "D-PAD DERECHO DERECHA", "C": "C", "MODE": "MODO", "FIRE": "FUEGO", "RESET": "RESET", "LEFT DIFFICULTY A": "IZQUIERDA DIFICULTAD A", "LEFT DIFFICULTY B": "IZQUIERDA DIFICULTAD B", "RIGHT DIFFICULTY A": "DERECHA DIFICULTAD A", "RIGHT DIFFICULTY B": "DERECHA DIFICULTAD B", "COLOR": "COLOR", "B/W": "B/N", "PAUSE": "PAUSA", "OPTION": "OPCIÓN", "OPTION 1": "OPCIÓN 1", "OPTION 2": "OPCION 2", "L2": "L2", "R2": "R2", "L3": "L3", "R3": "R3", "L STICK UP": "L STICK ARRIBA", "L STICK DOWN": "L STICK ABAJO", "L STICK LEFT": "L STICK IZQUIERDA", "L STICK RIGHT": "L STICK DERECHA", "R STICK UP": "R STICK ARRIBA", "R STICK DOWN": "R STICK ABAJO", "R STICK LEFT": "R STICK IZQUIERDA", "R STICK RIGHT": "R STICK DERECHA", "Start": "Start", "Select": "Select", "Fast": "<PERSON><PERSON><PERSON><PERSON>", "Slow": "<PERSON><PERSON>", "a": "a", "b": "b", "c": "c", "d": "d", "e": "e", "f": "f", "g": "g", "h": "h", "i": "i", "j": "j", "k": "k", "l": "l", "m": "m", "n": "n", "o": "o", "p": "p", "q": "q", "r": "r", "s": "s", "t": "t", "u": "u", "v": "v", "w": "w", "x": "x", "y": "y", "z": "z", "enter": "intro", "escape": "escape", "space": "espacio", "tab": "tabulador", "backspace": "retroceso", "delete": "borrar", "arrowup": "flecha arriba", "arrowdown": "flecha abajo", "arrowleft": "flecha i<PERSON>a", "arrowright": "flecha derecha", "f1": "f1", "f2": "f2", "f3": "f3", "f4": "f4", "f5": "f5", "f6": "f6", "f7": "f7", "f8": "f8", "f9": "f9", "f10": "f10", "f11": "f11", "f12": "f12", "shift": "shift", "control": "control", "alt": "alt", "meta": "meta", "capslock": "bloq may<PERSON>", "insert": "insertar", "home": "inicio", "end": "fin", "pageup": "página arriba", "pagedown": "página abajo", "!": "!", "@": "@", "#": "#", "$": "$", "%": "%", "^": "^", "&": "&", "*": "*", "(": "(", ")": ")", "-": "-", "_": "_", "+": "+", "=": "=", "[": "[", "]": "]", "{": "{", "}": "}", ";": ";", ":": ":", "'": "'", "\"": "\"", ",": ",", ".": ".", "<": "<", ">": ">", "/": "/", "?": "?", "LEFT_STICK_X": "STICK_IZQUIERDO_X", "LEFT_STICK_Y": "STICK_IZQUIERDO_Y", "RIGHT_STICK_X": "STICK_DERECHO_X", "RIGHT_STICK_Y": "STICK_DERECHO_Y", "LEFT_TRIGGER": "GATILLO_IZQUIERDO", "RIGHT_TRIGGER": "GATILLO_DERECHO", "A_BUTTON": "BOTÓN_A", "B_BUTTON": "BOTÓN_B", "X_BUTTON": "BOTÓN_X", "Y_BUTTON": "BOTÓN_Y", "START_BUTTON": "BOTÓN_START", "SELECT_BUTTON": "BOTÓN_SELECT", "L1_BUTTON": "BOTÓN_L1", "R1_BUTTON": "BOTÓN_R1", "L2_BUTTON": "BOTÓN_L2", "R2_BUTTON": "BOTÓN_R2", "LEFT_THUMB_BUTTON": "BOTÓN_L3", "RIGHT_THUMB_BUTTON": "BOTÓN_R3", "DPAD_UP": "DPAD ARRIBA", "DPAD_DOWN": "DPAD_ABAJO", "DPAD_LEFT": "DPAD_IZQUIERDA", "DPAD_RIGHT": "DPAD_DERECHO"}