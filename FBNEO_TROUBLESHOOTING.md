# FBNeo 街机游戏故障排除指南

## 🚨 常见问题

### 1. "Romset is unknown" 错误

这是最常见的FBNeo错误，表示ROM文件不被识别。

#### 原因分析
- **ROM版本不匹配**：FBNeo需要特定版本的ROM文件
- **文件名不正确**：ROM文件名必须与FBNeo数据库匹配
- **ROM文件损坏**：ZIP文件可能不完整或损坏

#### 解决方案

##### 1. 使用正确的ROM文件名
拳皇97的标准ROM名称应该是：
- `kof97.zip` (推荐)
- `kof97h.zip` (家用版)
- `kof97pls.zip` (Plus版)

##### 2. 检查ROM版本
FBNeo支持的拳皇97版本：
- **MAME 0.78** ROM集
- **FinalBurn Alpha** ROM集
- **Neo Geo MVS** 原版ROM

##### 3. ROM文件要求
```
文件名: kof97.zip
大小: 约 45-50MB
包含文件:
- 232-p1.p1 (程序ROM)
- 232-s1.s1 (系统ROM)  
- 232-c1.c1 到 232-c8.c8 (图像ROM)
- 232-m1.m1 (音频ROM)
- 232-v1.v1 到 232-v4.v4 (音频数据)
```

## 🔧 技术修复

### 已实施的修复

#### 1. 核心配置优化
```typescript
// 使用标准FBNeo核心而非legacy版本
{
  id: 'fbneo',
  name: 'Arcade (FBNeo)',
  extensions: ['zip', 'neo', 'mvs'],
  cdnUrl: 'https://cdn.emulatorjs.org/latest/data/cores/fbneo-wasm.data'
}
```

#### 2. 游戏名称标准化
```typescript
// 自动转换为标准ROM名称
if (coreType === "fbneo") {
  gameName = props.game.name.replace(/\.(zip|neo|mvs)$/i, "");
  if (gameName.toLowerCase().includes("kof") && gameName.includes("97")) {
    gameName = "kof97";
  }
}
```

#### 3. FBNeo特殊配置
```typescript
// 添加FBNeo专用设置
if (coreType === "fbneo") {
  (window as any).EJS_biosUrl = "";
  (window as any).EJS_gameParentUrl = "";
  (window as any).EJS_softLoad = 0;
}
```

## 📁 ROM文件获取指南

### 合法获取方式
1. **购买官方合集**：SNK 40th Anniversary Collection
2. **从原版卡带转储**：使用专业设备
3. **开源ROM项目**：查找开源替代品

### 文件验证
使用MD5校验确保ROM文件完整性：
```bash
# 拳皇97标准ROM的MD5值
kof97.zip: [具体MD5值需要查询MAME数据库]
```

## 🎮 替代方案

### 1. 使用其他核心
如果FBNeo不工作，可以尝试：
- **MAME 2003 Plus**
- **FinalBurn Alpha 2012**

### 2. 不同ROM版本
尝试不同的ROM版本：
- 日版 (Japan)
- 美版 (USA) 
- 欧版 (Europe)
- 家用版 (Home)

### 3. 在线街机模拟器
作为备选方案：
- **Internet Archive** 的街机游戏
- **RetroGames.cc**
- **ClassicReload**

## 🔍 调试步骤

### 1. 检查浏览器控制台
查看详细错误信息：
```javascript
// 打开开发者工具 (F12)
// 查看Console标签页的错误信息
```

### 2. 验证文件完整性
```javascript
// 在控制台检查游戏文件大小
console.log("游戏文件大小:", gameData.byteLength);
```

### 3. 测试不同浏览器
- **Chrome** (推荐)
- **Firefox**
- **Edge**
- 避免使用Safari (兼容性问题)

## ⚠️ 注意事项

### 法律声明
- 确保拥有游戏的合法使用权
- 仅用于个人学习和研究
- 遵守当地版权法律

### 技术限制
- 需要现代浏览器支持WebAssembly
- 移动设备性能可能不足
- 某些ROM可能永远无法在浏览器中运行

### 性能优化
- 关闭其他浏览器标签页
- 使用桌面浏览器而非移动端
- 确保稳定的网络连接

## 📞 获取帮助

### 社区资源
- **RetroArch论坛**
- **FBNeo官方文档**
- **MAME社区**

### 技术支持
如果问题持续存在：
1. 提供详细的错误信息
2. 说明使用的ROM文件信息
3. 包含浏览器和操作系统版本

## 🔄 下一步计划

### 即将改进
- 自动ROM验证
- 更好的错误提示
- ROM兼容性数据库
- 一键修复工具
