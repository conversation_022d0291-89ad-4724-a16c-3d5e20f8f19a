{"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "Restart": "<PERSON><PERSON><PERSON><PERSON>", "Pause": "Mettre en pause", "Play": "<PERSON><PERSON>", "Save State": "Enregistrer l'état", "Load State": "Ouv<PERSON>r un état", "Control Settings": "Paramètres des contrôles", "Cheats": "Triches", "Cache Manager": "Gestionnaire du cache", "Export Save File": "Exporter vers une sauvegarde", "Import Save File": "Importer une sauvegarde", "Netplay": "<PERSON>uer en ligne", "Mute": "<PERSON><PERSON><PERSON><PERSON> le son", "Unmute": "<PERSON><PERSON><PERSON><PERSON> le son", "Settings": "Paramètres", "Enter Fullscreen": "Passer en mode plein écran", "Exit Fullscreen": "<PERSON><PERSON><PERSON> le mode plein écran", "Context Menu": "Menu contextuel", "Reset": "Réinitialiser", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "Close": "<PERSON><PERSON><PERSON>", "QUICK SAVE STATE": "ENREGISTREMENT RAPIDE DE L'ÉTAT", "QUICK LOAD STATE": "CHARGEMENT RAPIDE DE L'ÉTAT", "CHANGE STATE SLOT": "CHANGER L'EMPLACEMENT DE L'ÉTAT", "FAST FORWARD": "AVANCE RAPIDE", "Player": "<PERSON><PERSON><PERSON>", "Connected Gamepad": "<PERSON><PERSON> de jeu <PERSON>", "Gamepad": "<PERSON><PERSON>eu", "Keyboard": "<PERSON><PERSON><PERSON>", "Set": "Définir", "Add Cheat": "Ajouter une triche", "Note that some cheats require a restart to disable": "Notez que certaines triches nécessitent un redémarrage pour être désactivées", "Create a Room": "Créer un salon", "Rooms": "Salons", "Start Game": "<PERSON><PERSON><PERSON><PERSON> le jeu", "Click to resume Emulator": "Cliquez pour reprendre avec l'émulateur", "Drop save state here to load": "Déposez un état enregistré ici pour le charger", "Loading...": "Chargement...", "Download Game Core": "Téléchargement du noyau pour le jeu...", "Outdated graphics driver": "Pilote graphique obsolète", "Decompress Game Core": "Décompression du noyau pour le jeu...", "Download Game Data": "Téléchargement des données du jeu...", "Decompress Game Data": "Décompression des données du jeu...", "Shaders": "Shaders", "Disabled": "Désactivé", "2xScaleHQ": "2xScaleHQ", "4xScaleHQ": "4xScaleHQ", "CRT easymode": "CRT easymode", "CRT aperture": "CRT aperture", "CRT geom": "CRT geom", "CRT mattias": "CRT mattias", "FPS": "FPS", "show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Masquer", "Fast Forward Ratio": "Vitesse de l'avance rapide", "Fast Forward": "Avance rapide", "Enabled": "Activé", "Save State Slot": "Emplacement de l'état", "Save State Location": "Stockage des états", "Download": "Télécharger", "Keep in Browser": "<PERSON><PERSON> le <PERSON>ur", "Auto": "Auto", "NTSC": "NTSC", "PAL": "PAL", "Dendy": "<PERSON><PERSON>", "8:7 PAR": "8:7 PAR", "4:3": "4:3", "Low": "<PERSON><PERSON>", "High": "Élevée", "Very High": "<PERSON><PERSON><PERSON>", "None": "Aucun", "Player 1": "Joueur 1", "Player 2": "Joueur 2", "Both": "Les deux", "SAVED STATE TO SLOT": "ÉTAT ENREGISTRÉ VERS L'EMPLACEMENT", "LOADED STATE FROM SLOT": "ÉTAT CHARGÉ À PARTIR DE L'EMPLACEMENT", "SET SAVE STATE SLOT TO": "DÉFINIR L'EMPLACEMENT DE L'ÉTAT ENREGISTRÉ À", "Network Error": "<PERSON><PERSON><PERSON>", "Submit": "So<PERSON><PERSON><PERSON>", "Description": "Description", "Code": "Code", "Add Cheat Code": "Ajouter un code de triche", "Leave Room": "Sortir du salon", "Password": "Mot de passe", "Password (optional)": "<PERSON>t de passe (facultatif)", "Max Players": "Nombre maximal de joueurs", "Room Name": "Nom du salon", "Join": "Rejoindre", "Player Name": "Nom du joueur", "Set Player Name": "<PERSON><PERSON><PERSON><PERSON> le nom du joueur", "Left Handed Mode": "<PERSON> gaucher", "Virtual Gamepad": "<PERSON><PERSON>", "Disk": "Disque", "Press Keyboard": "App<PERSON>ez sur le clavier", "INSERT COIN": "INSÉRER UNE PIÈCE", "Remove": "<PERSON><PERSON><PERSON>", "SAVE LOADED FROM BROWSER": "SAUVEGARDE CHARGÉE À PARTIR DU NAVIGATEUR", "SAVE SAVED TO BROWSER": "SAUVEGARDE ENREGISTRÉE DANS LE NAVIGATEUR", "Join the discord": "Rejoindre le serveur Discord", "View on GitHub": "Afficher sur GitHub", "Failed to start game": "Échec au démarrage du jeu", "Download Game BIOS": "Téléchargement du BIOS du jeu...", "Decompress Game BIOS": "Décompression du BIOS du jeu...", "Download Game Parent": "Téléchargement du jeu parent...", "Decompress Game Parent": "Décompression du jeu parent...", "Download Game Patch": "Téléchargement du correctif du jeu...", "Decompress Game Patch": "Décompression du correctif du jeu...", "Download Game State": "Téléchargement de l'état enregistré...", "Check console": "Vérifier la console", "Error for site owner": "Erreur pour le propriétaire du site", "EmulatorJS": "EmulatorJS", "Clear All": "Effacer tout", "Take Screenshot": "<PERSON><PERSON><PERSON> une capture d'é<PERSON>ran", "Start Screen Recording": "Démarrer l'enregistrement de l'écran", "Stop Screen Recording": "<PERSON><PERSON><PERSON>ter l'enregistrement de l'écran", "Quick Save": "Enregistrement rapide", "Quick Load": "Chargement rapide", "REWIND": "REMBOBINER", "Rewind Enabled (requires restart)": "Rembobinage activé (nécessite un redémarrage)", "Rewind Granularity": "Granularité du rembobinage", "Slow Motion Ratio": "Vitesse du ralenti", "Slow Motion": "Au ralenti", "Home": "Accueil", "EmulatorJS License": "Licence EmulatorJS", "RetroArch License": "Licence RetroArch", "This project is powered by": "Ce projet est propulsé par", "View the RetroArch license here": "Consulter la licence de RetroArch", "SLOW MOTION": "AU RALENTI", "A": "A", "B": "B", "SELECT": "SELECT", "START": "START", "UP": "HAUT", "DOWN": "BAS", "LEFT": "GAUCHE", "RIGHT": "DROITE", "X": "X", "Y": "Y", "L": "L", "R": "R", "Z": "Z", "STICK UP": "STICK HAUT", "STICK DOWN": "STICK BAS", "STICK LEFT": "STICK GAUCHE", "STICK RIGHT": "STICK DROITE", "C-PAD UP": "C-PAD HAUT", "C-PAD DOWN": "C-PAD BAS", "C-PAD LEFT": "C-PAD GAUCHE", "C-PAD RIGHT": "C-PAD DROITE", "MICROPHONE": "MICROPHONE", "BUTTON 1 / START": "BOUTON 1 / START", "BUTTON 2": "BOUTON 2", "BUTTON": "BOUTON", "LEFT D-PAD UP": "D-PAD GAUCHE HAUT", "LEFT D-PAD DOWN": "D-PAD GAUCHE BAS", "LEFT D-PAD LEFT": "D-PAD GAUCHE GAUCHE", "LEFT D-PAD RIGHT": "D-PAD GAUCHE DROITE", "RIGHT D-PAD UP": "D-PAD DROIT HAUT", "RIGHT D-PAD DOWN": "D-PAD DROIT BAS", "RIGHT D-PAD LEFT": "D-PAD DROIT GAUCHE", "RIGHT D-PAD RIGHT": "D-PAD DROIT DROITE", "C": "C", "MODE": "MODE", "FIRE": "FIRE", "RESET": "RESET", "LEFT DIFFICULTY A": "LEFT DIFFICULTY A", "LEFT DIFFICULTY B": "LEFT DIFFICULTY B", "RIGHT DIFFICULTY A": "RIGHT DIFFICULTY A", "RIGHT DIFFICULTY B": "RIGHT DIFFICULTY A", "COLOR": "COLOR", "B/W": "B/W", "PAUSE": "PAUSE", "OPTION": "OPTION", "OPTION 1": "OPTION 1", "OPTION 2": "OPTION 2", "L2": "L2", "R2": "R2", "L3": "L3", "R3": "R3", "L STICK UP": "STICK G HAUT", "L STICK DOWN": "STICK G BAS", "L STICK LEFT": "STICK G GAUCHE", "L STICK RIGHT": "STICK G DROITE", "R STICK UP": "STICK D HAUT", "R STICK DOWN": "STICK D BAS", "R STICK LEFT": "STICK D GAUCHE", "R STICK RIGHT": "STICK D DROITE", "Start": "START", "Select": "SELECT", "Fast": "FAST", "Slow": "SLOW", "a": "A", "b": "B", "c": "C", "d": "D", "e": "E", "f": "F", "g": "G", "h": "H", "i": "I", "j": "J", "k": "K", "l": "L", "m": "M", "n": "N", "o": "O", "p": "P", "q": "Q", "r": "R", "s": "S", "t": "T", "u": "U", "v": "V", "w": "W", "x": "X", "y": "Y", "z": "Z", "enter": "Entrée", "escape": "Échap", "space": "Espace", "tab": "Tab.", "backspace": "Retour arrière", "delete": "Suppr.", "arrowup": "Flèche vers le haut", "arrowdown": "Flèche vers le bas", "arrowleft": "Flèche vers la gauche", "arrowright": "Flèche vers la droite", "f1": "F1", "f2": "F2", "f3": "F3", "f4": "F4", "f5": "F5", "f6": "F6", "f7": "F7", "f8": "F8", "f9": "F9", "f10": "F10", "f11": "F11", "f12": "F12", "shift": "Maj", "control": "Ctrl", "alt": "Alt", "meta": "<PERSON><PERSON><PERSON>", "capslock": "Verr. maj", "insert": "Insér.", "home": "D<PERSON>but", "end": "Fin", "pageup": "Pg préc", "pagedown": "Pg suiv", "!": "!", "@": "@", "#": "#", "$": "$", "%": "%", "^": "^", "&": "&", "*": "*", "(": "(", ")": ")", "-": "-", "_": "_", "+": "+", "=": "=", "[": "[", "]": "]", "{": "{", "}": "}", ";": ";", ":": ":", "'": "'", "\"": "\"", ",": ",", ".": ".", "<": "<", ">": ">", "/": "/", "?": "?", "LEFT_STICK_X": "STICK_GAUCHE_X", "LEFT_STICK_Y": "STICK_GAUCHE_Y", "RIGHT_STICK_X": "STICK_DROIT_X", "RIGHT_STICK_Y": "STICK_DROIT_Y", "LEFT_TRIGGER": "GACHETTE_GAUCHE", "RIGHT_TRIGGER": "GACHETTE_DROITE", "A_BUTTON": "BOUTON_A", "B_BUTTON": "BOUTON_B", "X_BUTTON": "BOUTON_X", "Y_BUTTON": "BOUTON_Y", "START_BUTTON": "BOUTON_START", "SELECT_BUTTON": "BOUTON_SELECT", "L1_BUTTON": "BOUTON_L1", "R1_BUTTON": "BOUTON_R1", "L2_BUTTON": "BOUTON_L2", "R2_BUTTON": "BOUTON_R2", "LEFT_THUMB_BUTTON": "BOUTON_L3", "RIGHT_THUMB_BUTTON": "BOUTON_R3", "DPAD_UP": "DPAD_HAUT", "DPAD_DOWN": "DPAD_BAS", "DPAD_LEFT": "DPAD_GAUCHE", "DPAD_RIGHT": "DPAD_DROITE", "Disks": "Disques", "Exit EmulatorJS": "Quitter EmulatorJS", "BUTTON_1": "BOUTON_1", "BUTTON_2": "BOUTON_2", "BUTTON_3": "BOUTON_3", "BUTTON_4": "BOUTON_4", "up arrow": "Flèche haut", "down arrow": "Flèche bas", "left arrow": "Flèche gauche", "right arrow": "Flèche droite", "LEFT_TOP_SHOULDER": "SHOULDER_GAUCHE_HAUT", "RIGHT_TOP_SHOULDER": "SHOULDER_DROITE_HAUT", "CRT beam": "CRT beam", "CRT caligari": "CRT caligari", "CRT lottes": "CRT lottes", "CRT yeetron": "CRT yeetron", "CRT zfast": "CRT zfast", "SABR": "SABR", "Bicubic": "Bicubique", "Mix frames": "Mix frames", "WebGL2": "WebGL2", "Requires restart": "Redémarrage requis", "VSync": "VSync", "Video Rotation": "Rotation vidéo", "Rewind Enabled (Requires restart)": "Rembobinage activé (Redémarrage requis)", "System Save interval": "Intervalle de sauvegarde du système", "Menu Bar Button": "Bouton de la barre de menu", "visible": "visible", "hidden": "<PERSON><PERSON><PERSON><PERSON>"}