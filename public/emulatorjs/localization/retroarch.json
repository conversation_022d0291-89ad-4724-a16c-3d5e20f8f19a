{"fceumm region": "fceumm region", "fceumm sndquality": "fceumm sndquality", "fceumm aspect": "fceumm aspect", "fceumm overscan h left": "fceumm overscan h left", "fceumm overscan h right": "fceumm overscan h right", "fceumm overscan v top": "fceumm overscan v top", "fceumm overscan v bottom": "fceumm overscan v bottom", "fceumm turbo enable": "fceumm turbo enable", "fceumm turbo delay": "fceumm turbo delay", "fceumm zapper tolerance": "fceumm zapper tolerance", "fceumm mouse sensitivity": "fceumm mouse sensitivity", "50%": "50%", "60%": "60%", "70%": "70%", "80%": "80%", "90%": "90%", "100%": "100%", "150%": "150%", "200%": "200%", "250%": "250%", "300%": "300%", "350%": "350%", "400%": "400%", "450%": "450%", "500%": "500%", "snes9x overclock superfx": "snes9x overclock superfx", "snes9x superscope crosshair": "snes9x superscope crosshair", "White": "White", "White (blend)": "White (blend)", "Red": "Red", "Red (blend)": "Red (blend)", "Orange": "Orange", "Orange (blend)": "Orange (blend)", "Yellow": "Yellow", "Yellow (blend)": "Yellow (blend)", "Green": "Green", "Green (blend)": "Green (blend)", "Cyan": "<PERSON><PERSON>", "Cyan (blend)": "<PERSON><PERSON> (blend)", "Sky": "Sky", "Sky (blend)": "Sky (blend)", "Blue": "Blue", "Blue (blend)": "Blue (blend)", "Violet": "Violet", "Violet (blend)": "Violet (blend)", "Pink": "Pink", "Pink (blend)": "Pink (blend)", "Purple": "Purple", "Purple (blend)": "Purple (blend)", "Black": "Black", "Black (blend)": "Black (blend)", "25% Grey": "25% Grey", "25% Grey (blend)": "25% Grey (blend)", "50% Grey": "50% Grey", "50% Grey (blend)": "50% Grey (blend)", "75% Grey": "75% Grey", "75% Grey (blend)": "75% Grey (blend)", "snes9x superscope color": "snes9x superscope color", "snes9x justifier1 crosshair": "snes9x justifier1 crosshair", "snes9x justifier1 color": "snes9x justifier1 color", "snes9x justifier2 crosshair": "snes9x justifier2 crosshair", "snes9x justifier2 color": "snes9x justifier2 color", "snes9x rifle crosshair": "snes9x rifle crosshair", "snes9x rifle color": "snes9x rifle color", "1.0x (12.50Mhz)": "1.0x (12.50Mhz)", "1.1x (13.75Mhz)": "1.1x (13.75Mhz)", "1.2x (15.00Mhz)": "1.2x (15.00Mhz)", "1.5x (18.75Mhz)": "1.5x (18.75Mhz)", "1.6x (20.00Mhz)": "1.6x (20.00Mhz)", "1.8x (22.50Mhz)": "1.8x (22.50Mhz)", "2.0x (25.00Mhz)": "2.0x (25.00Mhz)", "opera cpu overclock": "opera cpu overclock", "0RGB1555": "0RGB1555", "RGB565": "RGB565", "XRGB8888": "XRGB8888", "opera vdlp pixel format": "opera vdlp pixel format", "opera nvram version": "opera nvram version", "opera active devices": "opera active devices", "stella2014 stelladaptor analog sensitivity": "stella2014 stelladaptor analog sensitivity", "stella2014 stelladaptor analog center": "stella2014 stelladaptor analog center", "handy frameskip threshold": "handy frameskip threshold", "320x240": "320x240", "640x480": "640x480", "960x720": "960x720", "1280x960": "1280x960", "1440x1080": "1440x1080", "1600x1200": "1600x1200", "1920x1440": "1920x1440", "2240x1680": "2240x1680", "2560x1920": "2560x1920", "2880x2160": "2880x2160", "3200x2400": "3200x2400", "3520x2640": "3520x2640", "3840x2880": "3840x2880", "43screensize": "43screensize", "3point": "3point", "standard": "standard", "BilinearMode": "BilinearMode", "MultiSampling": "MultiSampling", "FXAA": "FXAA", "Software": "Software", "FromMem": "FromMem", "EnableCopyDepthToRDRAM": "EnableCopyDepthToRDRAM", "Stripped": "Stripped", "OnePiece": "OnePiece", "BackgroundMode": "BackgroundMode", "OverscanTop": "OverscanTop", "OverscanLeft": "OverscanLeft", "OverscanRight": "OverscanRight", "OverscanBottom": "OverscanBottom", "MaxHiResTxVramLimit": "MaxHiResTxVramLimit", "MaxTxCacheSize": "MaxTxCacheSize", "Smooth filtering 1": "Smooth filtering 1", "Smooth filtering 2": "Smooth filtering 2", "Smooth filtering 3": "Smooth filtering 3", "Smooth filtering 4": "Smooth filtering 4", "Sharp filtering 1": "Sharp filtering 1", "Sharp filtering 2": "Sharp filtering 2", "txFilterMode": "txFilterMode", "As Is": "As Is", "X2": "X2", "X2SAI": "X2SAI", "HQ2X": "HQ2X", "HQ2XS": "HQ2XS", "LQ2X": "LQ2X", "LQ2XS": "LQ2XS", "HQ4X": "HQ4X", "2xBRZ": "2xBRZ", "3xBRZ": "3xBRZ", "4xBRZ": "4xBRZ", "5xBRZ": "5xBRZ", "6xBRZ": "6xBRZ", "txEnhancementMode": "txEnhancementMode", "Original": "Original", "Fullspeed": "Fullspeed", "Framerate": "Framerate", "virefresh": "virefresh", "CountPerOp": "CountPerOp", "CountPerOpDenomPot": "CountPerOpDenomPot", "deadzone": "deadzone", "sensitivity": "sensitivity", "C1": "C1", "C2": "C2", "C3": "C3", "C4": "C4", "cbutton": "cbutton", "none": "none", "memory": "memory", "rumble": "rumble", "transfer": "transfer", "pak1": "pak1", "pak2": "pak2", "pak3": "pak3", "pak4": "pak4", "Autodetect": "Autodetect", "Game Boy": "Game Boy", "Super Game Boy": "Super Game Boy", "Game Boy Color": "Game Boy Color", "Game Boy Advance": "Game Boy Advance", "mgba gb model": "mgba gb model", "ON": "ON", "OFF": "OFF", "mgba use bios": "mgba use bios", "mgba skip bios": "mgba skip bios", "Grayscale": "Grayscale", "DMG Green": "DMG Green", "GB Pocket": "GB Pocket", "GB Light": "GB Light", "GBC Brown ↑": "GBC Brown ↑", "GBC Red ↑A": "GBC Red ↑A", "GBC Dark Brown ↑B": "GBC Dark Brown ↑B", "GBC Pale Yellow ↓": "GBC Pale Yellow ↓", "GBC Orange ↓A": "GBC Orange ↓A", "GBC Yellow ↓B": "GBC Yellow ↓B", "GBC Blue ←": "GBC Blue ←", "GBC Dark Blue ←A": "GBC Dark Blue ←A", "GBC Gray ←B": "GBC Gray ←B", "GBC Green →": "GBC Green →", "GBC Dark Green →A": "GBC Dark Green →A", "GBC Reverse →B": "GBC Reverse →B", "SGB 1-A": "SGB 1-A", "SGB 1-B": "SGB 1-B", "SGB 1-C": "SGB 1-C", "SGB 1-D": "SGB 1-D", "SGB 1-E": "SGB 1-E", "SGB 1-F": "SGB 1-F", "SGB 1-G": "SGB 1-G", "SGB 1-H": "SGB 1-H", "SGB 2-A": "SGB 2-A", "SGB 2-B": "SGB 2-B", "SGB 2-C": "SGB 2-C", "SGB 2-D": "SGB 2-D", "SGB 2-E": "SGB 2-E", "SGB 2-F": "SGB 2-F", "SGB 2-G": "SGB 2-G", "SGB 2-H": "SGB 2-H", "SGB 3-A": "SGB 3-A", "SGB 3-B": "SGB 3-B", "SGB 3-C": "SGB 3-C", "SGB 3-D": "SGB 3-D", "SGB 3-E": "SGB 3-E", "SGB 3-F": "SGB 3-F", "SGB 3-G": "SGB 3-G", "SGB 3-H": "SGB 3-H", "SGB 4-A": "SGB 4-A", "SGB 4-B": "SGB 4-B", "SGB 4-C": "SGB 4-C", "SGB 4-D": "SGB 4-D", "SGB 4-E": "SGB 4-E", "SGB 4-F": "SGB 4-F", "SGB 4-G": "SGB 4-G", "SGB 4-H": "SGB 4-H", "mgba gb colors": "mgba gb colors", "mgba sgb borders": "mgba sgb borders", "mgba color correction": "mgba color correction", "mgba solar sensor level": "mgba solar sensor level", "mgba force gbp": "mgba force gbp", "Remove Known": "Remove Known", "Detect and Remove": "Detect and Remove", "Don't Remove": "Don't Remove", "mgba idle optimization": "mgba idle optimization", "mgba frameskip threshold": "mgba frameskip threshold", "mgba frameskip interval": "mgba frameskip interval", "GB - DMG": "GB - DMG", "GB - Pocket": "GB - Pocket", "GB - Light": "GB - Light", "GBC - Blue": "GBC - Blue", "GBC - Brown": "GBC - Brown", "GBC - Dark Blue": "GBC - Dark Blue", "GBC - Dark Brown": "GBC - <PERSON> Brown", "GBC - Dark Green": "GBC - Dark Green", "GBC - Grayscale": "GBC - Grayscale", "GBC - Green": "GBC - Green", "GBC - Inverted": "GBC - Inverted", "GBC - Orange": "GBC - Orange", "GBC - Pastel Mix": "GBC - Pastel Mix", "GBC - Red": "GBC - Red", "GBC - Yellow": "GBC - Yellow", "SGB - 1A": "SGB - 1A", "SGB - 1B": "SGB - 1B", "SGB - 1C": "SGB - 1C", "SGB - 1D": "SGB - 1D", "SGB - 1E": "SGB - 1E", "SGB - 1F": "SGB - 1F", "SGB - 1G": "SGB - 1G", "SGB - 1H": "SGB - 1H", "SGB - 2A": "SGB - 2A", "SGB - 2B": "SGB - 2B", "SGB - 2C": "SGB - 2C", "SGB - 2D": "SGB - 2D", "SGB - 2E": "SGB - 2E", "SGB - 2F": "SGB - 2F", "SGB - 2G": "SGB - 2G", "SGB - 2H": "SGB - 2H", "SGB - 3A": "SGB - 3A", "SGB - 3B": "SGB - 3B", "SGB - 3C": "SGB - 3C", "SGB - 3D": "SGB - 3D", "SGB - 3E": "SGB - 3E", "SGB - 3F": "SGB - 3F", "SGB - 3G": "SGB - 3G", "SGB - 3H": "SGB - 3H", "SGB - 4A": "SGB - 4A", "SGB - 4B": "SGB - 4B", "SGB - 4C": "SGB - 4C", "SGB - 4D": "SGB - 4D", "SGB - 4E": "SGB - 4E", "SGB - 4F": "SGB - 4F", "SGB - 4G": "SGB - 4G", "SGB - 4H": "SGB - 4H", "Special 1": "Special 1", "Special 2": "Special 2", "Special 3": "Special 3", "Special 4 (TI-83 Legacy)": "Special 4 (TI-83 Legacy)", "TWB64 - Pack 1": "TWB64 - Pack 1", "TWB64 - Pack 2": "TWB64 - Pack 2", "PixelShift - Pack 1": "PixelShift - Pack 1", "gambatte gb internal palette": "gambatte gb internal palette", "TWB64 001 - Aqours Blue": "TWB64 001 - Aqours Blue", "TWB64 002 - Anime Expo Ver.": "TWB64 002 - Anime Expo Ver.", "TWB64 003 - SpongeBob Yellow": "TWB64 003 - <PERSON>ponge<PERSON><PERSON> Yellow", "TWB64 004 - Patrick Star Pink": "TWB64 004 - <PERSON>", "TWB64 005 - Neon Red": "TWB64 005 - Neon Red", "TWB64 006 - Neon Blue": "TWB64 006 - Neon Blue", "TWB64 007 - Neon Yellow": "TWB64 007 - Neon Yellow", "TWB64 008 - Neon Green": "TWB64 008 - Neon Green", "TWB64 009 - Neon Pink": "TWB64 009 - Neon Pink", "TWB64 010 - Mario Red": "TWB64 010 - <PERSON>", "TWB64 011 - Nick Orange": "TWB64 011 - <PERSON>", "TWB64 012 - Virtual Boy Ver.": "TWB64 012 - Virtual Boy Ver.", "TWB64 013 - Golden Wild": "TWB64 013 - Golden Wild", "TWB64 014 - Builder Yellow": "TWB64 014 - Builder Yellow", "TWB64 015 - Classic Blurple": "TWB64 015 - Classic Blurple", "TWB64 016 - 765 Production Ver.": "TWB64 016 - 765 Production Ver.", "TWB64 017 - Superball Ivory": "TWB64 017 - Superball Ivory", "TWB64 018 - Crunchyroll Orange": "TWB64 018 - Crunchyroll Orange", "TWB64 019 - Muse Pink": "TWB64 019 - <PERSON>", "TWB64 020 - Nijigasaki Yellow": "TWB64 020 - Nijigasaki Yellow", "TWB64 021 - Gamate Ver.": "TWB64 021 - <PERSON><PERSON><PERSON> Ver.", "TWB64 022 - Greenscale Ver.": "TWB64 022 - Greenscale Ver.", "TWB64 023 - Odyssey Gold": "TWB64 023 - Odyssey Gold", "TWB64 024 - Super Saiyan God": "TWB64 024 - <PERSON> Saiyan God", "TWB64 025 - Super Saiyan Blue": "TWB64 025 - Super Saiyan Blue", "TWB64 026 - Bizarre Pink": "TWB64 026 - <PERSON><PERSON><PERSON>", "TWB64 027 - Nintendo Switch Lite Ver.": "TWB64 027 - Nintendo Switch Lite Ver.", "TWB64 028 - Game.com Ver.": "TWB64 028 - Game.com Ver.", "TWB64 029 - Sanrio Pink": "TWB64 029 - <PERSON><PERSON>", "TWB64 030 - BANDAI NAMCO Ver.": "TWB64 030 - BANDAI NAMCO Ver.", "TWB64 031 - Cosmo Green": "TWB64 031 - <PERSON><PERSON>", "TWB64 032 - Wanda Pink": "TWB64 032 - <PERSON>", "TWB64 033 - Link's Awakening DX Ver.": "TWB64 033 - Link's Awakening DX Ver.", "TWB64 034 - Travel Wood": "TWB64 034 - Travel Wood", "TWB64 035 - Pokemon Ver.": "TWB64 035 - <PERSON><PERSON><PERSON> Ver.", "TWB64 036 - Game Grump Orange": "TWB64 036 - Game Grump Orange", "TWB64 037 - Scooby-Doo Mystery Ver.": "TWB64 037 - Scooby-Doo Mystery Ver.", "TWB64 038 - Pokemon mini Ver.": "TWB64 038 - Pokemon mini Ver.", "TWB64 039 - Supervision Ver.": "TWB64 039 - Supervision Ver.", "TWB64 040 - DMG Ver.": "TWB64 040 - DMG Ver.", "TWB64 041 - Pocket Ver.": "TWB64 041 - Pocket Ver.", "TWB64 042 - Light Ver.": "TWB64 042 - Light Ver.", "TWB64 043 - Miraitowa Blue": "TWB64 043 - Miraitowa Blue", "TWB64 044 - Someity Pink": "TWB64 044 - <PERSON><PERSON> Pink", "TWB64 045 - Pikachu Yellow": "TWB64 045 - <PERSON><PERSON><PERSON>", "TWB64 046 - Eevee Brown": "TWB64 046 - <PERSON><PERSON><PERSON>", "TWB64 047 - Microvision Ver.": "TWB64 047 - Microvision Ver.", "TWB64 048 - TI-83 Ver.": "TWB64 048 - TI-83 Ver.", "TWB64 049 - Aegis Cherry": "TWB64 049 - <PERSON><PERSON><PERSON>", "TWB64 050 - Labo Fawn": "TWB64 050 - <PERSON><PERSON> Fawn", "TWB64 051 - MILLION LIVE GOLD!": "TWB64 051 - <PERSON><PERSON><PERSON> LIVE GOLD!", "TWB64 052 - Tokyo Midtown Ver.": "TWB64 052 - Tokyo Midtown Ver.", "TWB64 053 - VMU Ver.": "TWB64 053 - VMU Ver.", "TWB64 054 - Game Master Ver.": "TWB64 054 - Game Master Ver.", "TWB64 055 - Android Green": "TWB64 055 - Android Green", "TWB64 056 - Ticketmaster Azure": "TWB64 056 - Ticketmaster Azure", "TWB64 057 - Google Red": "TWB64 057 - Google Red", "TWB64 058 - Google Blue": "TWB64 058 - Google Blue", "TWB64 059 - Google Yellow": "TWB64 059 - Google Yellow", "TWB64 060 - Google Green": "TWB64 060 - Google Green", "TWB64 061 - WonderSwan Ver.": "TWB64 061 - WonderSwan Ver.", "TWB64 062 - Neo Geo Pocket Ver.": "TWB64 062 - Neo Geo Pocket Ver.", "TWB64 063 - Dew Green": "TWB64 063 - <PERSON><PERSON>", "TWB64 064 - Coca-Cola Red": "TWB64 064 - Coca-Cola Red", "TWB64 065 - GameKing Ver.": "TWB64 065 - Game<PERSON><PERSON> Ver.", "TWB64 066 - Do The Dew Ver.": "TWB64 066 - Do The Dew Ver.", "TWB64 067 - Digivice Ver.": "TWB64 067 - Digivice Ver.", "TWB64 068 - Bikini Bottom Ver.": "TWB64 068 - Bikini Bottom Ver.", "TWB64 069 - Blossom Pink": "TWB64 069 - <PERSON><PERSON>", "TWB64 070 - Bubbles Blue": "TWB64 070 - <PERSON><PERSON><PERSON> Blue", "TWB64 071 - Buttercup Green": "TWB64 071 - Buttercup Green", "TWB64 072 - NASCAR Ver.": "TWB64 072 - NASCAR Ver.", "TWB64 073 - Lemon-Lime Green": "TWB64 073 - <PERSON>-<PERSON>e Green", "TWB64 074 - Mega Man V Ver.": "TWB64 074 - Mega Man V Ver.", "TWB64 075 - Tamagotchi Ver.": "TWB64 075 - <PERSON><PERSON><PERSON><PERSON> V<PERSON>.", "TWB64 076 - Phantom Red": "TWB64 076 - Phantom Red", "TWB64 077 - Halloween Ver.": "TWB64 077 - <PERSON> Ver.", "TWB64 078 - Christmas Ver.": "TWB64 078 - <PERSON> Ver.", "TWB64 079 - Cardcaptor Pink": "TWB64 079 - Cardcaptor Pink", "TWB64 080 - Pretty Guardian Gold": "TWB64 080 - Pretty Guardian Gold", "TWB64 081 - Camouflage Ver.": "TWB64 081 - Camouflage Ver.", "TWB64 082 - Legendary Super Saiyan": "TWB64 082 - Legendary Super Saiyan", "TWB64 083 - Super Saiyan Rose": "TWB64 083 - <PERSON> Saiyan Rose", "TWB64 084 - Super Saiyan": "TWB64 084 - <PERSON> Saiyan", "TWB64 085 - Perfected Ultra Instinct": "TWB64 085 - Perfected Ultra Instinct", "TWB64 086 - Saint Snow Red": "TWB64 086 - Saint Snow Red", "TWB64 087 - Yellow Banana": "TWB64 087 - <PERSON>", "TWB64 088 - Green Banana": "TWB64 088 - <PERSON> Banana", "TWB64 089 - Super Saiyan 3": "TWB64 089 - Super Saiyan 3", "TWB64 090 - Super Saiyan Blue Evolved": "TWB64 090 - Super Saiyan Blue Evolved", "TWB64 091 - Pocket Tales Ver.": "TWB64 091 - Pocket Tales Ver.", "TWB64 092 - Investigation Yellow": "TWB64 092 - Investigation Yellow", "TWB64 093 - S.E.E.S. Blue": "TWB64 093 - S.E.E.S. Blue", "TWB64 094 - Game Awards Cyan": "TWB64 094 - Game Awards Cyan", "TWB64 095 - Hokage Orange": "TWB64 095 - Hokage Orange", "TWB64 096 - Straw Hat Red": "TWB64 096 - Straw Hat Red", "TWB64 097 - Sword Art Cyan": "TWB64 097 - <PERSON> Art Cyan", "TWB64 098 - Deku Alpha Emerald": "TWB64 098 - Deku Alpha Emerald", "TWB64 099 - Blue Stripes Ver.": "TWB64 099 - Blue Stripes Ver.", "TWB64 100 - Stone Orange": "TWB64 100 - Stone Orange", "gambatte gb palette twb64 1": "gambatte gb palette twb64 1", "TWB64 101 - 765PRO Pink": "TWB64 101 - 765PRO Pink", "TWB64 102 - CINDERELLA Blue": "TWB64 102 - CINDERELLA Blue", "TWB64 103 - MILLION Yellow!": "TWB64 103 - MILLION Yellow!", "TWB64 104 - SideM Green": "TWB64 104 - <PERSON><PERSON> Green", "TWB64 105 - SHINY Sky Blue": "TWB64 105 - SHINY Sky Blue", "TWB64 106 - Angry Volcano Ver.": "TWB64 106 - Angry Volcano Ver.", "TWB64 107 - Yo-kai Pink": "TWB64 107 - <PERSON>-ka<PERSON>", "TWB64 108 - Yo-kai Green": "TWB64 108 - <PERSON>-ka<PERSON> Green", "TWB64 109 - Yo-kai Blue": "TWB64 109 - <PERSON>-kai Blue", "TWB64 110 - Yo-kai Purple": "TWB64 110 - <PERSON>-kai Purple", "TWB64 111 - Aquatic Iro": "TWB64 111 - Aquatic Iro", "TWB64 112 - Tea Midori": "TWB64 112 - <PERSON>", "TWB64 113 - Sakura Pink": "TWB64 113 - <PERSON>", "TWB64 114 - Wisteria Murasaki": "TWB64 114 - <PERSON><PERSON><PERSON>", "TWB64 115 - Oni Aka": "TWB64 115 - <PERSON><PERSON>", "TWB64 116 - Golden Kiiro": "TWB64 116 - <PERSON>", "TWB64 117 - Silver Shiro": "TWB64 117 - <PERSON>", "TWB64 118 - Fruity Orange": "TWB64 118 - Fruity <PERSON>", "TWB64 119 - AKB48 Pink": "TWB64 119 - <PERSON><PERSON><PERSON> <PERSON>", "TWB64 120 - Miku Blue": "TWB64 120 - <PERSON><PERSON>", "TWB64 121 - Fairy Tail Red": "TWB64 121 - <PERSON>l <PERSON>", "TWB64 122 - Survey Corps Brown": "TWB64 122 - Survey <PERSON> Brown", "TWB64 123 - Island Green": "TWB64 123 - Island Green", "TWB64 124 - Mania Plus Green": "TWB64 124 - Mania Plus Green", "TWB64 125 - Ninja Turtle Green": "TWB64 125 - <PERSON> Turtle Green", "TWB64 126 - Slime Blue": "TWB64 126 - Slime Blue", "TWB64 127 - Lime Midori": "TWB64 127 - <PERSON><PERSON>", "TWB64 128 - Ghostly Aoi": "TWB64 128 - <PERSON><PERSON>", "TWB64 129 - Retro Bogeda": "TWB64 129 - <PERSON><PERSON>", "TWB64 130 - Royal Blue": "TWB64 130 - Royal Blue", "TWB64 131 - Neon Purple": "TWB64 131 - Neon Purple", "TWB64 132 - Neon Orange": "TWB64 132 - Neon Orange", "TWB64 133 - Moonlight Vision": "TWB64 133 - Moonlight Vision", "TWB64 134 - Tokyo Red": "TWB64 134 - Tokyo Red", "TWB64 135 - Paris Gold": "TWB64 135 - Paris Gold", "TWB64 136 - Beijing Blue": "TWB64 136 - Beijing Blue", "TWB64 137 - Pac-Man Yellow": "TWB64 137 - <PERSON><PERSON><PERSON>", "TWB64 138 - Irish Green": "TWB64 138 - Irish Green", "TWB64 139 - Kakarot Orange": "TWB64 139 - <PERSON><PERSON><PERSON> Orange", "TWB64 140 - Dragon Ball Orange": "TWB64 140 - Dragon Ball Orange", "TWB64 141 - Christmas Gold": "TWB64 141 - Christmas Gold", "TWB64 142 - Pepsi-Cola Blue": "TWB64 142 - Pepsi-Cola Blue", "TWB64 143 - Bubblun Green": "TWB64 143 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "TWB64 144 - Bobblun Blue": "TWB64 144 - <PERSON><PERSON><PERSON><PERSON>", "TWB64 145 - Baja Blast Storm": "TWB64 145 - Baja Blast Storm", "TWB64 146 - Olympic Gold": "TWB64 146 - Olympic Gold", "TWB64 147 - Value Orange": "TWB64 147 - Value Orange", "TWB64 148 - Liella Purple!": "TWB64 148 - <PERSON><PERSON>!", "TWB64 149 - Olympic Silver": "TWB64 149 - Olympic Silver", "TWB64 150 - Olympic Bronze": "TWB64 150 - Olympic Bronze", "TWB64 151 - ANA Sky Blue": "TWB64 151 - ANA Sky Blue", "TWB64 152 - Nijigasaki Orange": "TWB64 152 - Nijigasaki Orange", "TWB64 153 - HoloBlue": "TWB64 153 - HoloBlue", "TWB64 154 - Wrestling Red": "TWB64 154 - Wrestling Red", "TWB64 155 - Yoshi Egg Green": "TWB64 155 - <PERSON><PERSON>", "TWB64 156 - Pokedex Red": "TWB64 156 - Pokedex Red", "TWB64 157 - Disney Dream Blue": "TWB64 157 - Disney Dream Blue", "TWB64 158 - Xbox Green": "TWB64 158 - <PERSON> Green", "TWB64 159 - Sonic Mega Blue": "TWB64 159 - Sonic Mega Blue", "TWB64 160 - G4 Orange": "TWB64 160 - G4 Orange", "TWB64 161 - Scarlett Green": "TWB64 161 - <PERSON>", "TWB64 162 - Glitchy Blue": "TWB64 162 - <PERSON><PERSON><PERSON> Blue", "TWB64 163 - Classic LCD": "TWB64 163 - Classic LCD", "TWB64 164 - 3DS Virtual Console Ver.": "TWB64 164 - 3DS Virtual Console Ver.", "TWB64 165 - PocketStation Ver.": "TWB64 165 - PocketStation Ver.", "TWB64 166 - Game and Gold": "TWB64 166 - Game and Gold", "TWB64 167 - Smurfy Blue": "TWB64 167 - Smurfy Blue", "TWB64 168 - Swampy Ogre Green": "TWB64 168 - <PERSON><PERSON>", "TWB64 169 - Sailor Spinach Green": "TWB64 169 - <PERSON> <PERSON><PERSON>", "TWB64 170 - Shenron Green": "TWB64 170 - <PERSON><PERSON> Green", "TWB64 171 - Berserk Blood": "TWB64 171 - Berserk Blood", "TWB64 172 - Super Star Pink": "TWB64 172 - Super Star Pink", "TWB64 173 - Gamebuino Classic Ver.": "TWB64 173 - Gamebuino Classic Ver.", "TWB64 174 - Barbie Pink": "TWB64 174 - <PERSON>", "TWB64 175 - Star Command Green": "TWB64 175 - Star Command Green", "TWB64 176 - Nokia 3310 Ver.": "TWB64 176 - Nokia 3310 Ver.", "TWB64 177 - Clover Green": "TWB64 177 - <PERSON><PERSON>", "TWB64 178 - Crash Orange": "TWB64 178 - <PERSON>", "TWB64 179 - Famicom Disk Yellow": "TWB64 179 - Famicom Disk Yellow", "TWB64 180 - Team Rocket Red": "TWB64 180 - Team Rocket Red", "TWB64 181 - SEIKO Timer Yellow": "TWB64 181 - SEIKO Timer Yellow", "TWB64 182 - PINK109": "TWB64 182 - PINK109", "TWB64 183 - Doraemon Blue": "TWB64 183 - <PERSON><PERSON>", "TWB64 184 - Fury Blue": "TWB64 184 - Fury Blue", "TWB64 185 - Rockstar Orange": "TWB64 185 - Rockstar Orange", "TWB64 186 - Puyo Puyo Green": "TWB64 186 - <PERSON><PERSON><PERSON> Puyo Green", "TWB64 187 - Susan G. Pink": "TWB64 187 - <PERSON>", "TWB64 188 - Pizza Hut Red": "TWB64 188 - Pizza Hut Red", "TWB64 189 - Plumbob Green": "TWB64 189 - Plumbob Green", "TWB64 190 - Grand Ivory": "TWB64 190 - <PERSON> Ivory", "TWB64 191 - Demon's Gold": "TWB64 191 - Demon's Gold", "TWB64 192 - SEGA Tokyo Blue": "TWB64 192 - SEGA Tokyo Blue", "TWB64 193 - Champion Blue": "TWB64 193 - Champion Blue", "TWB64 194 - DK Barrel Brown": "TWB64 194 - <PERSON><PERSON>", "TWB64 195 - Evangelion Green": "TWB64 195 - <PERSON><PERSON><PERSON>", "TWB64 196 - Equestrian Purple": "TWB64 196 - Equest<PERSON>", "TWB64 197 - Autobot Red": "TWB64 197 - Autobot Red", "TWB64 198 - Niconico Sea Green": "TWB64 198 - Niconico Sea Green", "TWB64 199 - Duracell Copper": "TWB64 199 - Duracell Copper", "TWB64 200 - TOKYO SKYTREE CLOUDY BLUE": "TWB64 200 - TOK<PERSON>O SKYTREE CLOUDY BLUE", "gambatte gb palette twb64 2": "gambatte gb palette twb64 2", "PixelShift 01 - Arctic Green": "PixelShift 01 - Arctic Green", "PixelShift 02 - Arduboy": "PixelShift 02 - <PERSON><PERSON><PERSON><PERSON>", "PixelShift 03 - BGB 0.3 Emulator": "PixelShift 03 - BGB 0.3 Emulator", "PixelShift 04 - Camouflage": "PixelShift 04 - Camouflage", "PixelShift 05 - Chocolate Bar": "PixelShift 05 - Chocolate Bar", "PixelShift 06 - CMYK": "PixelShift 06 - CMYK", "PixelShift 07 - Cotton Candy": "PixelShift 07 - <PERSON>", "PixelShift 08 - Easy Greens": "PixelShift 08 - Easy Greens", "PixelShift 09 - Gamate": "PixelShift 09 - Gamate", "PixelShift 10 - Game Boy Light": "PixelShift 10 - Game Boy Light", "PixelShift 11 - Game Boy Pocket": "PixelShift 11 - Game Boy Pocket", "PixelShift 12 - Game Boy Pocket Alt": "PixelShift 12 - Game Boy Pocket Alt", "PixelShift 13 - Game Pocket Computer": "PixelShift 13 - Game Pocket Computer", "PixelShift 14 - Game & Watch Ball": "PixelShift 14 - Game & Watch Ball", "PixelShift 15 - GB Backlight Blue": "PixelShift 15 - GB Backlight Blue", "PixelShift 16 - GB Backlight Faded": "PixelShift 16 - GB Backlight Faded", "PixelShift 17 - GB Backlight Orange": "PixelShift 17 - GB Backlight Orange", "PixelShift 18 - GB Backlight White ": "PixelShift 18 - GB Backlight White ", "PixelShift 19 - GB Backlight Yellow Dark": "PixelShift 19 - GB Backlight Yellow Dark", "PixelShift 20 - GB Bootleg": "PixelShift 20 - GB Bootleg", "PixelShift 21 - GB Hunter": "PixelShift 21 - GB Hunter", "PixelShift 22 - GB Kiosk": "PixelShift 22 - GB Kiosk", "PixelShift 23 - GB Kiosk 2": "PixelShift 23 - GB Kiosk 2", "PixelShift 24 - GB New": "PixelShift 24 - GB New", "PixelShift 25 - GB Nuked": "PixelShift 25 - GB Nuked", "PixelShift 26 - GB Old": "PixelShift 26 - GB Old", "PixelShift 27 - GBP Bivert": "PixelShift 27 - <PERSON><PERSON> B<PERSON>t", "PixelShift 28 - GB Washed Yellow Backlight": "PixelShift 28 - GB Washed Yellow Backlight", "PixelShift 29 - Ghost": "PixelShift 29 - Ghost", "PixelShift 30 - Glow In The Dark": "PixelShift 30 - Glow In The Dark", "PixelShift 31 - Gold Bar": "PixelShift 31 - Gold Bar", "PixelShift 32 - Grapefruit": "PixelShift 32 - Grapefruit", "PixelShift 33 - Gray Green Mix": "PixelShift 33 - <PERSON>", "PixelShift 34 - Missingno": "PixelShift 34 - Missing<PERSON>", "PixelShift 35 - MS-Dos": "PixelShift 35 - MS-Dos", "PixelShift 36 - Newspaper": "PixelShift 36 - Newspaper", "PixelShift 37 - Pip-Boy": "PixelShift 37 - <PERSON><PERSON>-<PERSON>", "PixelShift 38 - Pocket Girl": "PixelShift 38 - Pocket Girl", "PixelShift 39 - Silhouette": "PixelShift 39 - <PERSON><PERSON><PERSON><PERSON>", "PixelShift 40 - Sunburst": "PixelShift 40 - Sunburst", "PixelShift 41 - Technicolor": "PixelShift 41 - Technicolor", "PixelShift 42 - Tron": "PixelShift 42 - Tron", "PixelShift 43 - Vaporwave": "PixelShift 43 - Vaporwave", "PixelShift 44 - Virtual Boy": "PixelShift 44 - Virtual Boy", "PixelShift 45 - Wish": "PixelShift 45 - Wish", "gambatte gb palette pixelshift 1": "gambatte gb palette pixelshift 1", "gambatte dark filter level": "gambatte dark filter level", "GB": "GB", "GBC": "GBC", "GBA": "GBA", "gambatte gb hwmode": "gambatte gb hwmode", "gambatte turbo period": "gambatte turbo period", "gambatte rumble level": "gambatte rumble level", "pcsx rearmed psxclock": "pcsx rearmed psxclock", "pcsx rearmed frameskip threshold": "pcsx rearmed frameskip threshold", "pcsx rearmed frameskip interval": "pcsx rearmed frameskip interval", "pcsx rearmed input sensitivity": "pcsx rearmed input sensitivity", "pcsx rearmed gunconadjustx": "pcsx rearmed gun<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcsx rearmed gunconadjusty": "pcsx rearmed gun<PERSON><PERSON><PERSON><PERSON>", "pcsx rearmed gunconadjustratiox": "pcsx rearmed gunconadju<PERSON><PERSON><PERSON>", "pcsx rearmed gunconadjustratioy": "pcsx rearmed gun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Japan NTSC": "Japan NTSC", "Japan PAL": "Japan PAL", "US": "US", "Europe": "Europe", "picodrive region": "picodrive region", "Game Gear": "Game Gear", "Master System": "Master System", "SG-1000": "SG-1000", "SC-3000": "SC-3000", "picodrive smstype": "picodrive smstype", "Sega": "Sega", "Codemasters": "Codemasters", "Korea": "Korea", "Korea MSX": "Korea MSX", "Korea X-in-1": "Korea X-in-1", "Korea 4-Pak": "Korea 4-Pak", "Korea Janggun": "Korea Janggun", "Korea Nemesis": "Korea Nemesis", "Taiwan 8K RAM": "Taiwan 8K RAM", "picodrive smsmapper": "picodrive smsmapper", "PAR": "PAR", "CRT": "CRT", "picodrive aspect": "picodrive aspect", "native": "native", "picodrive sound rate": "picodrive sound rate", "picodrive lowpass range": "picodrive lowpass range", "picodrive frameskip threshold": "picodrive frameskip threshold", "genesis plus gx frameskip threshold": "genesis plus gx frameskip threshold", "genesis plus gx lowpass range": "genesis plus gx lowpass range", "genesis plus gx psg preamp": "genesis plus gx psg preamp", "genesis plus gx fm preamp": "genesis plus gx fm preamp", "genesis plus gx cdda volume": "genesis plus gx cdda volume", "genesis plus gx pcm volume": "genesis plus gx pcm volume", "genesis plus gx audio eq low": "genesis plus gx audio eq low", "genesis plus gx audio eq mid": "genesis plus gx audio eq mid", "genesis plus gx audio eq high": "genesis plus gx audio eq high", "genesis plus gx enhanced vscroll limit": "genesis plus gx enhanced vscroll limit", "genesis plus gx psg channel 0 volume": "genesis plus gx psg channel 0 volume", "genesis plus gx psg channel 1 volume": "genesis plus gx psg channel 1 volume", "genesis plus gx psg channel 2 volume": "genesis plus gx psg channel 2 volume", "genesis plus gx psg channel 3 volume": "genesis plus gx psg channel 3 volume", "genesis plus gx md channel 0 volume": "genesis plus gx md channel 0 volume", "genesis plus gx md channel 1 volume": "genesis plus gx md channel 1 volume", "genesis plus gx md channel 2 volume": "genesis plus gx md channel 2 volume", "genesis plus gx md channel 3 volume": "genesis plus gx md channel 3 volume", "genesis plus gx md channel 4 volume": "genesis plus gx md channel 4 volume", "genesis plus gx md channel 5 volume": "genesis plus gx md channel 5 volume", "genesis plus gx sms fm channel 0 volume": "genesis plus gx sms fm channel 0 volume", "genesis plus gx sms fm channel 1 volume": "genesis plus gx sms fm channel 1 volume", "genesis plus gx sms fm channel 2 volume": "genesis plus gx sms fm channel 2 volume", "genesis plus gx sms fm channel 3 volume": "genesis plus gx sms fm channel 3 volume", "genesis plus gx sms fm channel 4 volume": "genesis plus gx sms fm channel 4 volume", "genesis plus gx sms fm channel 5 volume": "genesis plus gx sms fm channel 5 volume", "genesis plus gx sms fm channel 6 volume": "genesis plus gx sms fm channel 6 volume", "genesis plus gx sms fm channel 7 volume": "genesis plus gx sms fm channel 7 volume", "genesis plus gx sms fm channel 8 volume": "genesis plus gx sms fm channel 8 volume", "anaglyph": "anaglyph", "cyberscope": "cyberscope", "side-by-side": "side-by-side", "vli": "vli", "hli": "hli", "vb 3dmode": "vb 3dmode", "black & red": "black & red", "black & white": "black & white", "black & blue": "black & blue", "black & cyan": "black & cyan", "black & electric cyan": "black & electric cyan", "black & green": "black & green", "black & magenta": "black & magenta", "black & yellow": "black & yellow", "vb color mode": "vb color mode", "accurate": "accurate", "fast": "fast", "vb cpu emulation": "vb cpu emulation"}