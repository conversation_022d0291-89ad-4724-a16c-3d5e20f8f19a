{"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "Restart": "<PERSON><PERSON>", "Pause": "Pause", "Play": "Play", "Save State": "Save State", "Load State": "Load State", "Control Settings": "Control Settings", "Cheats": "Cheats", "Cache Manager": "<PERSON><PERSON> Manager", "Export Save File": "Export Save File", "Import Save File": "Import Save File", "Netplay": "Netplay", "Mute": "Mute", "Unmute": "Unmute", "Settings": "Settings", "Enter Fullscreen": "Enter Fullscreen", "Exit Fullscreen": "Exit Fullscreen", "Context Menu": "Context Menu", "Reset": "Reset", "Clear": "Clear", "Close": "Close", "QUICK SAVE STATE": "QUICK SAVE STATE", "QUICK LOAD STATE": "QUICK LOAD STATE", "CHANGE STATE SLOT": "CHANGE STATE SLOT", "FAST FORWARD": "FAST FORWARD", "Player": "Player", "Connected Gamepad": "Connected Gamepad", "Gamepad": "Gamepad", "Keyboard": "Keyboard", "Set": "Set", "Add Cheat": "Add Cheat", "Note that some cheats require a restart to disable": "Note that some cheats require a restart to disable", "Create a Room": "Create a Room", "Rooms": "Rooms", "Start Game": "Start Game", "Click to resume Emulator": "Click to resume Emulator", "Drop save state here to load": "Drop save state here to load", "Loading...": "Loading...", "Download Game Core": "Download Game Core", "Outdated graphics driver": "Outdated graphics driver", "Decompress Game Core": "Decompress Game Core", "Download Game Data": "Download Game Data", "Decompress Game Data": "Decompress Game Data", "Shaders": "Shaders", "Disabled": "Disabled", "2xScaleHQ": "2xScaleHQ", "4xScaleHQ": "4xScaleHQ", "CRT easymode": "CRT easymode", "CRT aperture": "CRT aperture", "CRT geom": "CRT geom", "CRT mattias": "CRT mattias", "FPS": "FPS", "show": "show", "hide": "hide", "Fast Forward Ratio": "Fast Forward Ratio", "Fast Forward": "Fast Forward", "Enabled": "Enabled", "Save State Slot": "Save State Slot", "Save State Location": "Save State Location", "Download": "Download", "Keep in Browser": "Keep in Browser", "Auto": "Auto", "NTSC": "NTSC", "PAL": "PAL", "Dendy": "<PERSON><PERSON>", "8:7 PAR": "8:7 PAR", "4:3": "4:3", "Low": "Low", "High": "High", "Very High": "Very High", "None": "None", "Player 1": "Player 1", "Player 2": "Player 2", "Both": "Both", "SAVED STATE TO SLOT": "SAVED STATE TO SLOT", "LOADED STATE FROM SLOT": "LOADED STATE FROM SLOT", "SET SAVE STATE SLOT TO": "SET SAVE STATE SLOT TO", "Network Error": "Network Error", "Submit": "Submit", "Description": "Description", "Code": "Code", "Add Cheat Code": "Add Cheat Code", "Leave Room": "Leave Room", "Password": "Password", "Password (optional)": "Password (optional)", "Max Players": "Max Players", "Room Name": "Room Name", "Join": "Join", "Player Name": "Player Name", "Set Player Name": "Set Player Name", "Left Handed Mode": "Left Handed Mode", "Virtual Gamepad": "Virtual Gamepad", "Disk": "Disk", "Press Keyboard": "Press Keyboard", "INSERT COIN": "INSERT COIN", "Remove": "Remove", "SAVE LOADED FROM BROWSER": "SAVE LOADED FROM BROWSER", "SAVE SAVED TO BROWSER": "SAVE SAVED TO BROWSER", "Join the discord": "Join the discord", "View on GitHub": "View on GitHub", "Failed to start game": "Failed to start game", "Download Game BIOS": "Download Game BIOS", "Decompress Game BIOS": "Decompress Game BIOS", "Download Game Parent": "Download Game Parent", "Decompress Game Parent": "Decompress Game Parent", "Download Game Patch": "Download Game Patch", "Decompress Game Patch": "Decompress Game Patch", "Download Game State": "Download Game State", "Check console": "Check console", "Error for site owner": "Error for site owner", "EmulatorJS": "EmulatorJS", "Clear All": "Clear All", "Take Screenshot": "Take Screenshot", "Start Screen Recording": "Start Screen Recording", "Stop Screen Recording": "Stop Screen Recording", "Quick Save": "Quick Save", "Quick Load": "Quick Load", "REWIND": "REWIND", "Rewind Enabled (requires restart)": "Rewind Enabled (requires restart)", "Rewind Granularity": "Rewind Granularity", "Slow Motion Ratio": "Slow Motion Ratio", "Slow Motion": "Slow Motion", "Home": "Home", "EmulatorJS License": "EmulatorJS License", "RetroArch License": "RetroArch License", "This project is powered by": "This project is powered by", "View the RetroArch license here": "View the RetroArch license here", "SLOW MOTION": "SLOW MOTION", "A": "A", "B": "B", "SELECT": "SELECT", "START": "START", "UP": "UP", "DOWN": "DOWN", "LEFT": "LEFT", "RIGHT": "RIGHT", "X": "X", "Y": "Y", "L": "L", "R": "R", "Z": "Z", "STICK UP": "STICK UP", "STICK DOWN": "STICK DOWN", "STICK LEFT": "STICK LEFT", "STICK RIGHT": "STICK RIGHT", "C-PAD UP": "C-PAD UP", "C-PAD DOWN": "C-PAD DOWN", "C-PAD LEFT": "C-PAD LEFT", "C-PAD RIGHT": "C-PAD RIGHT", "MICROPHONE": "MICROPHONE", "BUTTON 1 / START": "BUTTON 1 / START", "BUTTON 2": "BUTTON 2", "BUTTON": "BUTTON", "LEFT D-PAD UP": "LEFT D-PAD UP", "LEFT D-PAD DOWN": "LEFT D-PAD DOWN", "LEFT D-PAD LEFT": "LEFT D-PAD LEFT", "LEFT D-PAD RIGHT": "LEFT D-PAD RIGHT", "RIGHT D-PAD UP": "RIGHT D-PAD UP", "RIGHT D-PAD DOWN": "RIGHT D-PAD DOWN", "RIGHT D-PAD LEFT": "RIGHT D-PAD LEFT", "RIGHT D-PAD RIGHT": "RIGHT D-PAD RIGHT", "C": "C", "MODE": "MODE", "FIRE": "FIRE", "RESET": "RESET", "LEFT DIFFICULTY A": "LEFT DIFFICULTY A", "LEFT DIFFICULTY B": "LEFT DIFFICULTY B", "RIGHT DIFFICULTY A": "RIGHT DIFFICULTY A", "RIGHT DIFFICULTY B": "RIGHT DIFFICULTY B", "COLOR": "COLOR", "B/W": "B/W", "PAUSE": "PAUSE", "OPTION": "OPTION", "OPTION 1": "OPTION 1", "OPTION 2": "OPTION 2", "L2": "L2", "R2": "R2", "L3": "L3", "R3": "R3", "L STICK UP": "L STICK UP", "L STICK DOWN": "L STICK DOWN", "L STICK LEFT": "L STICK LEFT", "L STICK RIGHT": "L STICK RIGHT", "R STICK UP": "R STICK UP", "R STICK DOWN": "R STICK DOWN", "R STICK LEFT": "R STICK LEFT", "R STICK RIGHT": "R STICK RIGHT", "Start": "Start", "Select": "Select", "Fast": "Fast", "Slow": "Slow", "a": "a", "b": "b", "c": "c", "d": "d", "e": "e", "f": "f", "g": "g", "h": "h", "i": "i", "j": "j", "k": "k", "l": "l", "m": "m", "n": "n", "o": "o", "p": "p", "q": "q", "r": "r", "s": "s", "t": "t", "u": "u", "v": "v", "w": "w", "x": "x", "y": "y", "z": "z", "enter": "enter", "escape": "escape", "space": "space", "tab": "tab", "backspace": "backspace", "delete": "delete", "arrowup": "arrowup", "arrowdown": "arrowdown", "arrowleft": "arrowleft", "arrowright": "arrowright", "f1": "f1", "f2": "f2", "f3": "f3", "f4": "f4", "f5": "f5", "f6": "f6", "f7": "f7", "f8": "f8", "f9": "f9", "f10": "f10", "f11": "f11", "f12": "f12", "shift": "shift", "control": "control", "alt": "alt", "meta": "meta", "capslock": "capslock", "insert": "insert", "home": "home", "end": "end", "pageup": "pageup", "pagedown": "pagedown", "!": "!", "@": "@", "#": "#", "$": "$", "%": "%", "^": "^", "&": "&", "*": "*", "(": "(", ")": ")", "-": "-", "_": "_", "+": "+", "=": "=", "[": "[", "]": "]", "{": "{", "}": "}", ";": ";", ":": ":", "'": "'", "\"": "\"", ",": ",", ".": ".", "<": "<", ">": ">", "/": "/", "?": "?", "LEFT_STICK_X": "LEFT_STICK_X", "LEFT_STICK_Y": "LEFT_STICK_Y", "RIGHT_STICK_X": "RIGHT_STICK_X", "RIGHT_STICK_Y": "RIGHT_STICK_Y", "LEFT_TRIGGER": "LEFT_TRIGGER", "RIGHT_TRIGGER": "RIGHT_TRIGGER", "A_BUTTON": "A_BUTTON", "B_BUTTON": "B_BUTTON", "X_BUTTON": "X_BUTTON", "Y_BUTTON": "Y_BUTTON", "START_BUTTON": "START_BUTTON", "SELECT_BUTTON": "SELECT_BUTTON", "L1_BUTTON": "L1_BUTTON", "R1_BUTTON": "R1_BUTTON", "L2_BUTTON": "L2_BUTTON", "R2_BUTTON": "R2_BUTTON", "LEFT_THUMB_BUTTON": "LEFT_THUMB_BUTTON", "RIGHT_THUMB_BUTTON": "RIGHT_THUMB_BUTTON", "DPAD_UP": "DPAD_UP", "DPAD_DOWN": "DPAD_DOWN", "DPAD_LEFT": "DPAD_LEFT", "DPAD_RIGHT": "DPAD_RIGHT", "Disks": "Disks", "Exit EmulatorJS": "Exit EmulatorJS", "BUTTON_1": "BUTTON_1", "BUTTON_2": "BUTTON_2", "BUTTON_3": "BUTTON_3", "BUTTON_4": "BUTTON_4", "up arrow": "up arrow", "down arrow": "down arrow", "left arrow": "left arrow", "right arrow": "right arrow", "LEFT_TOP_SHOULDER": "LEFT_TOP_SHOULDER", "RIGHT_TOP_SHOULDER": "RIGHT_TOP_SHOULDER", "CRT beam": "CRT beam", "CRT caligari": "CRT caligari", "CRT lottes": "CRT lottes", "CRT yeetron": "CRT yeetron", "CRT zfast": "CRT zfast", "SABR": "SABR", "Bicubic": "Bicubic", "Mix frames": "Mix frames", "WebGL2": "WebGL2", "Requires restart": "Requires restart", "VSync": "VSync", "Video Rotation": "Video Rotation", "Rewind Enabled (Requires restart)": "Rewind Enabled (Requires restart)", "System Save interval": "System Save interval", "Menu Bar Button": "<PERSON><PERSON>", "visible": "visible", "hidden": "hidden"}